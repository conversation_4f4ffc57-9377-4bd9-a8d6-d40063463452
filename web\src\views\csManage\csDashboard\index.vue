<template>
  <div class="cs-manage-dashboard min-h-screen bg-gray-50 p-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">客服数据管理</h1>
      <p class="text-gray-600 mt-1">客服会话数据统计与分析</p>
    </div>

    <!-- 筛选器 -->
    <div class="bg-white rounded-lg shadow-sm p-4 mb-6">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex items-center gap-2 flex-nowrap">
          <label class="text-sm font-medium text-gray-700 whitespace-nowrap">日期范围:</label>
          <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期" format="YYYY-MM-DD" value-format="YYYY-MM-DD" @change="handleDateChange"
            class="!w-64" :clearable="false" :editable="false" :shortcuts="shortcuts" />
        </div>

        <div class="flex items-center gap-2 flex-nowrap">
          <label class="text-sm font-medium text-gray-700 whitespace-nowrap">客服组:</label>
          <el-select
            v-model="selectedGroupIds"
            placeholder="选择客服组"
            @change="handleGroupChange"
            class="!w-56"
            :popper-class="'max-h-80 overflow-y-auto'"
            :clearable="false"
            :disabled="loading"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
          >
            <el-option v-for="group in availableGroups" :key="group.id" :label="group.name" :value="group.id">
              <div class="truncate max-w-48">{{ group.name }}</div>
            </el-option>
          </el-select>
        </div>

        <el-button type="primary" @click="refreshData" :loading="loading" :disabled="loading">
          <el-icon class="mr-1">
            <Refresh />
          </el-icon>
          刷新数据
        </el-button>
      </div>
    </div>

    <!-- 数据卡片 - 分组显示 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
      <template v-for="(groupData, groupKey) in dataGroupedCards" :key="groupKey">
        <DataCard
          v-if="shouldShowGroup(groupKey)"
          :isGroupCard="true"
          :groupTitle="groupData.config.title"
          :groupIcon="groupData.config.icon"
          :groupIconColor="groupData.config.color"
          :groupItems="getGroupItems(groupData.cards)"
          :loading="loading"
        />
      </template>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 会话趋势图 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">会话量趋势</h3>
        <SessionTrendChart :data="dashboardData.charts?.session_trend" :loading="loading" />
      </div>

      <!-- 工单趋势图 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">工单量趋势</h3>
        <WorksheetTrendChart :data="dashboardData.charts?.worksheet_trend" :loading="loading" />
      </div>
    </div>

    <!-- 第二行图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
      <!-- 质量指标图 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">质量指标趋势</h3>
        <QualityMetricsChart :data="dashboardData.charts?.quality_metrics" :loading="loading" />
      </div>

      <!-- 情绪趋势图 -->
      <div v-if="dashboardData.charts?.emotion_trend && Object.keys(dashboardData.charts.emotion_trend).length > 0"
        class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">情绪分析趋势</h3>
        <EmotionTrendChart :data="dashboardData.charts?.emotion_trend" :loading="loading" />
      </div>
    </div>

    <!-- 客服组趋势图表（全部组别或多选时显示） -->
    <div v-if="shouldShowTrendChart && dashboardData.charts?.group_trend" class="bg-white rounded-lg shadow-sm p-6 mt-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">
        {{ selectedGroupIds.length > 1 && !selectedGroupIds.includes(0) ? '选中客服组会话量趋势' : '各客服组会话量趋势' }}
      </h3>
      <GroupTrendChart
        :key="`trend-${selectedGroupIds.join('-')}`"
        :data="dashboardData.charts?.group_trend"
        :loading="loading"
      />
    </div>

    <!-- Link排行榜 -->
    <div class="bg-white rounded-lg shadow-sm p-6 mt-6">
      <LinkRankingChart
        :topArticles="linkRankingData.top_articles || []"
        :summary="linkRankingData.summary || { total_links: 0, total_articles: 0, period: { start_time: '', end_time: '' } }"
        @openArticle="handleOpenArticle"
        @refresh="fetchLinkRankingData"
        @topCountChange="handleTopCountChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, ChatDotRound, Service, User, Document, SwitchButton,
         SmileFilled, CircleCheck, Timer, Star, Message, AlarmClock, MagicStick, Connection } from '@element-plus/icons-vue'
import DataCard from './components/DataCard.vue'
import SessionTrendChart from './components/SessionTrendChart.vue'
import WorksheetTrendChart from './components/WorksheetTrendChart.vue'
import QualityMetricsChart from './components/QualityMetricsChart.vue'
import EmotionTrendChart from './components/EmotionTrendChart.vue'
import GroupTrendChart from './components/GroupTrendChart.vue'
import LinkRankingChart from './components/LinkRankingChart.vue'
import { getDashboardOverview, getLinkRanking } from './api'
import { getDataGroupedCards, type CardConfig } from './config/cardConfig'
import moment from 'moment'

// 响应式数据
const loading = ref(false)
const dateRange = ref<[string, string]>()
const selectedGroupIds = ref<number[]>([0]) // 改为数组，默认选择全部
const availableGroups = ref<Array<{id: number, name: string}>>([])

const dashboardData = reactive({
  cards: {},
  charts: {},
  filters: {}
})

const linkRankingData = reactive({
  top_articles: [],
  summary: {
    total_links: 0,
    total_articles: 0,
    period: {
      start_time: '',
      end_time: ''
    }
  }
})

// Link排行榜top数量
const topCount = ref(10)

const shortcuts = [
  {
    text: '昨天 ',
    value: () => {
      const start = moment().subtract(1, 'days').startOf('day').format('YYYY-MM-DD')
      // 加一天
      const end = moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD')
      return [start, end]
    },
  },
  {
    text: '最近一周',
    value: () => {
      const start = moment().subtract(8, 'days').startOf('day').format('YYYY-MM-DD')
      // 加一天
      const end = moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD')
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const start = moment().subtract(30, 'days').startOf('day').format('YYYY-MM-DD')
      const end = moment().subtract(1, 'days').endOf('day').format('YYYY-MM-DD')
      return [start, end]
    },
  }
]

// 计算属性
const currentFilters = computed(() => ({
  start_date: dateRange.value?.[0],
  end_date: dateRange.value?.[1],
  group_ids: selectedGroupIds.value.includes(0) ? undefined : selectedGroupIds.value
}))

// 判断是否显示趋势图（全部数据或多选时显示）
const shouldShowTrendChart = computed(() => {
  // 包含全部(0)，或者选择了多个组别
  return selectedGroupIds.value.includes(0) || selectedGroupIds.value.length > 1
})

// 配置化数据卡片
const dataGroupedCards = computed(() => getDataGroupedCards())

// 卡片相关方法
const getCardValue = (key: string) => {
  return dashboardData.cards?.[key] || 0
}

// 分组卡片相关方法
const shouldShowGroup = (groupKey: string) => {
  // 情绪分析组只在有数据时显示
  if (groupKey === 'emotion') {
    return dashboardData.cards?.emotion_score !== null && dashboardData.cards?.emotion_score !== undefined
  }
  return true
}

const getGroupItems = (cards: CardConfig[]) => {
  return cards.map(card => ({
    key: card.key,
    title: card.title,
    value: getCardValue(card.key),
    suffix: card.suffix,
    icon: card.icon,
    color: card.color
  }))
}

// 方法
const initializeDefaultDate = () => {
  // 默认时间为15天前到昨天
  const startDate = new Date()
  startDate.setDate(startDate.getDate() - 15)
  const endDate = new Date()
  endDate.setDate(endDate.getDate() - 1)
  dateRange.value = [startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]]
}

const fetchDashboardData = async () => {
  // 防止重复请求
  if (loading.value) {
    return
  }

  try {
    loading.value = true

    const params = {
      start_date: currentFilters.value.start_date,
      end_date: currentFilters.value.end_date,
      group_ids: currentFilters.value.group_ids
    }

    console.log('[Dashboard] 发送请求参数:', params)

    const response = await getDashboardOverview(params)

    if (response.code === 2000) {
      // 更新数据
      Object.assign(dashboardData.cards, response.data.cards)
      Object.assign(dashboardData.charts, response.data.charts)
      Object.assign(dashboardData.filters, response.data.filters)

      // 更新可用组别
      availableGroups.value = response.data.filters.groups || []

      ElMessage.success('数据加载成功')
    } else {
      ElMessage.error('获取数据失败，请稍后重试')
    }
  } catch (error) {
    ElMessage.error('获取数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

const fetchLinkRankingData = async () => {
  try {
    const params = {
      start_time: dateRange.value[0],
      end_time: dateRange.value[1],
      group_ids: selectedGroupIds.value.includes(0) ? undefined : selectedGroupIds.value,
      top_count: topCount.value
    }

    console.log('[Link排行榜] 发送请求参数:', params)
    const response = await getLinkRanking(params)
    
    // 后端返回的是SuccessResponse格式，数据在response.data中
    if (response.code === 2000 && response.data) {
      Object.assign(linkRankingData, response.data)
    } else {
      console.error('Link排行榜数据格式错误:', response)
    }
  } catch (error) {
    console.error('获取Link排行榜数据失败:', error)
  }
}

const handleOpenArticle = (articleId) => {
  // 打开文章详情页面
  window.open(`/#/kcs/article?id=${articleId}`, '_blank')
}

const handleDateChange = () => {
  fetchDashboardData()
  fetchLinkRankingData()
}

const handleGroupChange = () => {
  fetchDashboardData()
  fetchLinkRankingData()
}

const refreshData = () => {
  fetchDashboardData()
  fetchLinkRankingData()
}

// 处理top数量变化
const handleTopCountChange = (count: number) => {
  topCount.value = count
  fetchLinkRankingData()
}

// 生命周期
onMounted(() => {
  initializeDefaultDate()
  fetchDashboardData()
  fetchLinkRankingData()
})
</script>

<style scoped>
.cs-manage-dashboard {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}
</style>
