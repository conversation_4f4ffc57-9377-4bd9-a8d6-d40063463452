import {
  editor_main_exports
} from "./chunk-FKOI2X5Y.js";
import "./chunk-FSPS2RIA.js";
import {
  <PERSON><PERSON>,
  editor,
  languages
} from "./chunk-IT3I4ICD.js";
import {
  Et
} from "./chunk-RDE5VBAP.js";
import "./chunk-FFLZCXYO.js";
import "./chunk-RUCDUSHK.js";
import "./chunk-SQPW7ARH.js";
import "./chunk-GQR6RJUV.js";
import "./chunk-XBAZBRKF.js";
import {
  cloneDeep_default,
  debounce_default
} from "./chunk-6KFXODJP.js";
import "./chunk-6PRCX2O7.js";
import {
  createElementBlock,
  defineComponent,
  onMounted,
  onUnmounted,
  openBlock,
  ref,
  watch
} from "./chunk-VL4YS5HC.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/index-b1b23590.mjs
var J = defineComponent({
  __name: "index",
  props: {
    language: {},
    modelValue: {},
    config: {},
    schema: {},
    debounce: {},
    init: {},
    readonly: { type: Boolean },
    disabled: { type: Boolean },
    id: {}
  },
  emits: ["update:modelValue", "change", "ready"],
  setup(m, { emit: d }) {
    const a = m, l = ref();
    let g = ref();
    onUnmounted(() => {
    });
    const i = d, f = debounce_default((e) => {
      i("update:modelValue", e);
    }, a.debounce || 500);
    async function p(e) {
      const n = editor.create(l.value, {
        automaticLayout: true,
        value: a.modelValue,
        language: e.language,
        theme: "vs-dark",
        minimap: { enabled: false },
        readOnly: a.readonly || a.disabled,
        hover: {
          enabled: true
        },
        ...e.config
      });
      return n.onDidChangeModelContent((s) => {
        const o = n.getValue();
        a.modelValue !== o && (i("change", o), f(o));
      }), g.value = n, e.instance = n, i("ready", e), n;
    }
    async function h(e) {
      await import("./javascript.contribution-84301c19-GBFMICOI.js"), languages.register({ id: "javascript" });
    }
    async function y(e) {
      await import("./monaco.contribution-824f7607-V6PKSAFD.js"), languages.register({ id: "json" });
      const n = [];
      e.schema && n.push({
        // uri: "http://myserver/foo-schema.json", // id of the first schema
        fileMatch: ["*"],
        // associate with our model
        schema: {
          ...e.schema
        }
      }), languages.json.jsonDefaults.setDiagnosticsOptions({
        validate: true,
        enableSchemaRequest: false,
        schemas: n
      });
    }
    async function v(e) {
      await import("./yaml.contribution-d4fdad3e-PJUG6AO7.js");
      const { configureMonacoYaml: n } = await import("./monaco-yaml-573FHFWU.js");
      languages.register({ id: "yaml" });
      const s = [];
      e.schema && s.push({
        fileMatch: ["*"],
        // associate with our model
        schema: {
          ...e.schema
        },
        uri: "http://myserver/foo-schema.json"
      }), n(editor_main_exports, {
        schemas: s,
        format: true,
        hover: true,
        completion: true,
        validate: true,
        isKubernetes: false,
        enableSchemaRequest: false
      });
      const o = Uri.parse(a.id || "fs-editor-code-yaml.yaml"), r = editor.getModel(o);
      r && r.dispose(), e.config.model = editor.createModel(e.initialValue, null, o);
    }
    async function c() {
      const e = {
        monaco: editor_main_exports,
        language: a.language || "javascript",
        config: cloneDeep_default(a.config || {}),
        initialValue: a.modelValue || "",
        schema: a.schema
      };
      e.language === "javascript" ? await h() : e.language === "yaml" ? await v(e) : e.language === "json" && await y(e), await p(e);
    }
    return onMounted(async () => {
      await Et(), await c(), watch(
        () => ({
          language: a.language,
          config: a.config
        }),
        (e) => {
          c();
        }
      );
    }), (e, n) => (openBlock(), createElementBlock("div", {
      ref_key: "monacoRef",
      ref: l,
      class: "fs-editor-code"
    }, null, 512));
  }
});
export {
  J as default
};
//# sourceMappingURL=index-b1b23590-RNOKM5SP.js.map
