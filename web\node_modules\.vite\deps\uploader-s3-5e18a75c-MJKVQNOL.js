import {
  bt,
  vt
} from "./chunk-RDE5VBAP.js";
import "./chunk-FFLZCXYO.js";
import "./chunk-RUCDUSHK.js";
import "./chunk-SQPW7ARH.js";
import "./chunk-GQR6RJUV.js";
import "./chunk-XBAZBRKF.js";
import {
  cloneDeep_default,
  merge_default
} from "./chunk-6KFXODJP.js";
import "./chunk-6PRCX2O7.js";
import "./chunk-VL4YS5HC.js";
import {
  axios_default
} from "./chunk-ESNACNFC.js";
import "./chunk-2LSFTFF7.js";

// node_modules/@fast-crud/fast-extends/dist/uploader-s3-5e18a75c.mjs
var ic = Object.defineProperty;
var oc = (e, t, r) => t in e ? ic(e, t, { enumerable: true, configurable: true, writable: true, value: r }) : e[t] = r;
var _ = (e, t, r) => (oc(e, typeof t != "symbol" ? t + "" : t, r), r);
var fc = (e) => {
  let t = e.httpHandler;
  return {
    setHttpHandler(r) {
      t = r;
    },
    httpHandler() {
      return t;
    },
    updateHttpClientConfig(r, s) {
      t.updateHttpClientConfig(r, s);
    },
    httpHandlerConfigs() {
      return t.httpHandlerConfigs();
    }
  };
};
var hc = (e) => ({
  httpHandler: e.httpHandler()
});
var tn;
(function(e) {
  e.HEADER = "header", e.QUERY = "query";
})(tn || (tn = {}));
var rn;
(function(e) {
  e.HEADER = "header", e.QUERY = "query";
})(rn || (rn = {}));
var Bt;
(function(e) {
  e.HTTP = "http", e.HTTPS = "https";
})(Bt || (Bt = {}));
var Kt;
(function(e) {
  e.MD5 = "md5", e.CRC32 = "crc32", e.CRC32C = "crc32c", e.SHA1 = "sha1", e.SHA256 = "sha256";
})(Kt || (Kt = {}));
var sn;
(function(e) {
  e[e.HEADER = 0] = "HEADER", e[e.TRAILER = 1] = "TRAILER";
})(sn || (sn = {}));
var rs = "__smithy_context";
var nn;
(function(e) {
  e.PROFILE = "profile", e.SSO_SESSION = "sso-session", e.SERVICES = "services";
})(nn || (nn = {}));
var on;
(function(e) {
  e.HTTP_0_9 = "http/0.9", e.HTTP_1_0 = "http/1.0", e.TDS_8_0 = "tds/8.0";
})(on || (on = {}));
var J = class _J {
  constructor(t) {
    this.method = t.method || "GET", this.hostname = t.hostname || "localhost", this.port = t.port, this.query = t.query || {}, this.headers = t.headers || {}, this.body = t.body, this.protocol = t.protocol ? t.protocol.slice(-1) !== ":" ? `${t.protocol}:` : t.protocol : "https:", this.path = t.path ? t.path.charAt(0) !== "/" ? `/${t.path}` : t.path : "/", this.username = t.username, this.password = t.password, this.fragment = t.fragment;
  }
  static clone(t) {
    const r = new _J({
      ...t,
      headers: { ...t.headers }
    });
    return r.query && (r.query = pc(r.query)), r;
  }
  static isInstance(t) {
    if (!t)
      return false;
    const r = t;
    return "method" in r && "protocol" in r && "hostname" in r && "path" in r && typeof r.query == "object" && typeof r.headers == "object";
  }
  clone() {
    return _J.clone(this);
  }
};
function pc(e) {
  return Object.keys(e).reduce((t, r) => {
    const s = e[r];
    return {
      ...t,
      [r]: Array.isArray(s) ? [...s] : s
    };
  }, {});
}
var Nt = class {
  constructor(t) {
    this.statusCode = t.statusCode, this.reason = t.reason, this.headers = t.headers || {}, this.body = t.body;
  }
  static isInstance(t) {
    if (!t)
      return false;
    const r = t;
    return typeof r.statusCode == "number" && typeof r.headers == "object";
  }
};
function gc(e) {
  return (t) => async (r) => {
    var n, i;
    const { request: s } = r;
    return J.isInstance(s) && s.body && e.runtime === "node" && ((i = (n = e.requestHandler) == null ? void 0 : n.constructor) == null ? void 0 : i.name) !== "FetchHttpHandler" && (s.headers = {
      ...s.headers,
      Expect: "100-continue"
    }), t({
      ...r,
      request: s
    });
  };
}
var mc = {
  step: "build",
  tags: ["SET_EXPECT_HEADER", "EXPECT_HEADER"],
  name: "addExpectContinueMiddleware",
  override: true
};
var yc = (e) => ({
  applyToStack: (t) => {
    t.add(gc(e), mc);
  }
});
var ut = {
  WHEN_SUPPORTED: "WHEN_SUPPORTED",
  WHEN_REQUIRED: "WHEN_REQUIRED"
};
var wc = ut.WHEN_SUPPORTED;
var yr = {
  WHEN_SUPPORTED: "WHEN_SUPPORTED",
  WHEN_REQUIRED: "WHEN_REQUIRED"
};
var bc = ut.WHEN_SUPPORTED;
var L;
(function(e) {
  e.MD5 = "MD5", e.CRC32 = "CRC32", e.CRC32C = "CRC32C", e.CRC64NVME = "CRC64NVME", e.SHA1 = "SHA1", e.SHA256 = "SHA256";
})(L || (L = {}));
var an;
(function(e) {
  e.HEADER = "header", e.TRAILER = "trailer";
})(an || (an = {}));
var ss = L.CRC32;
var cn;
(function(e) {
  e.ENV = "env", e.CONFIG = "shared config entry";
})(cn || (cn = {}));
function Ec(e, t, r) {
  return e.$source || (e.$source = {}), e.$source[t] = r, e;
}
function Q(e, t, r) {
  e.__aws_sdk_context ? e.__aws_sdk_context.features || (e.__aws_sdk_context.features = {}) : e.__aws_sdk_context = {
    features: {}
  }, e.__aws_sdk_context.features[t] = r;
}
var un = (e) => {
  var t, r;
  return Nt.isInstance(e) ? ((t = e.headers) == null ? void 0 : t.date) ?? ((r = e.headers) == null ? void 0 : r.Date) : void 0;
};
var bs = (e) => new Date(Date.now() + e);
var xc = (e, t) => Math.abs(bs(t).getTime() - e) >= 3e5;
var dn = (e, t) => {
  const r = Date.parse(e);
  return xc(r, t) ? r - Date.now() : t;
};
var At = (e, t) => {
  if (!t)
    throw new Error(`Property \`${e}\` is not resolved for AWS SDK SigV4Auth`);
  return t;
};
var uo = async (e) => {
  var u, l, f;
  const t = At("context", e.context), r = At("config", e.config), s = (f = (l = (u = t.endpointV2) == null ? void 0 : u.properties) == null ? void 0 : l.authSchemes) == null ? void 0 : f[0], i = await At("signer", r.signer)(s), o = e == null ? void 0 : e.signingRegion, a = e == null ? void 0 : e.signingRegionSet, c = e == null ? void 0 : e.signingName;
  return {
    config: r,
    signer: i,
    signingRegion: o,
    signingRegionSet: a,
    signingName: c
  };
};
var lo = class {
  async sign(t, r, s) {
    var f;
    if (!J.isInstance(t))
      throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");
    const n = await uo(s), { config: i, signer: o } = n;
    let { signingRegion: a, signingName: c } = n;
    const u = s.context;
    if (((f = u == null ? void 0 : u.authSchemes) == null ? void 0 : f.length) ?? 0 > 1) {
      const [p, g] = u.authSchemes;
      (p == null ? void 0 : p.name) === "sigv4a" && (g == null ? void 0 : g.name) === "sigv4" && (a = (g == null ? void 0 : g.signingRegion) ?? a, c = (g == null ? void 0 : g.signingName) ?? c);
    }
    return await o.sign(t, {
      signingDate: bs(i.systemClockOffset),
      signingRegion: a,
      signingService: c
    });
  }
  errorHandler(t) {
    return (r) => {
      const s = r.ServerTime ?? un(r.$response);
      if (s) {
        const n = At("config", t.config), i = n.systemClockOffset;
        n.systemClockOffset = dn(s, n.systemClockOffset), n.systemClockOffset !== i && r.$metadata && (r.$metadata.clockSkewCorrected = true);
      }
      throw r;
    };
  }
  successHandler(t, r) {
    const s = un(t);
    if (s) {
      const n = At("config", r.config);
      n.systemClockOffset = dn(s, n.systemClockOffset);
    }
  }
};
var Sc = class extends lo {
  async sign(t, r, s) {
    var p;
    if (!J.isInstance(t))
      throw new Error("The request is not an instance of `HttpRequest` and cannot be signed");
    const { config: n, signer: i, signingRegion: o, signingRegionSet: a, signingName: c } = await uo(s), l = (await ((p = n.sigv4aSigningRegionSet) == null ? void 0 : p.call(n)) ?? a ?? [o]).join(",");
    return await i.sign(t, {
      signingDate: bs(n.systemClockOffset),
      signingRegion: l,
      signingService: c
    });
  }
};
var pt = (e) => e[rs] || (e[rs] = {});
var Pe = (e) => {
  if (typeof e == "function")
    return e;
  const t = Promise.resolve(e);
  return () => t;
};
function Ac(e) {
  const t = /* @__PURE__ */ new Map();
  for (const r of e)
    t.set(r.schemeId, r);
  return t;
}
var Cc = (e, t) => (r, s) => async (n) => {
  var u;
  const i = e.httpAuthSchemeProvider(await t.httpAuthSchemeParametersProvider(e, s, n.input)), o = Ac(e.httpAuthSchemes), a = pt(s), c = [];
  for (const l of i) {
    const f = o.get(l.schemeId);
    if (!f) {
      c.push(`HttpAuthScheme \`${l.schemeId}\` was not enabled for this service.`);
      continue;
    }
    const p = f.identityProvider(await t.identityProviderConfigProvider(e));
    if (!p) {
      c.push(`HttpAuthScheme \`${l.schemeId}\` did not have an IdentityProvider configured.`);
      continue;
    }
    const { identityProperties: g = {}, signingProperties: E = {} } = ((u = l.propertiesExtractor) == null ? void 0 : u.call(l, e, s)) || {};
    l.identityProperties = Object.assign(l.identityProperties || {}, g), l.signingProperties = Object.assign(l.signingProperties || {}, E), a.selectedHttpAuthScheme = {
      httpAuthOption: l,
      identity: await p(l.identityProperties),
      signer: f.signer
    };
    break;
  }
  if (!a.selectedHttpAuthScheme)
    throw new Error(c.join(`
`));
  return r(n);
};
var vc = {
  step: "serialize",
  tags: ["HTTP_AUTH_SCHEME"],
  name: "httpAuthSchemeMiddleware",
  override: true,
  relation: "before",
  toMiddleware: "endpointV2Middleware"
};
var Rc = (e, { httpAuthSchemeParametersProvider: t, identityProviderConfigProvider: r }) => ({
  applyToStack: (s) => {
    s.addRelativeTo(Cc(e, {
      httpAuthSchemeParametersProvider: t,
      identityProviderConfigProvider: r
    }), vc);
  }
});
var kc = (e, t) => (r, s) => async (n) => {
  var o, a, c, u;
  const { response: i } = await r(n);
  try {
    const l = await t(i, e);
    return {
      response: i,
      output: l
    };
  } catch (l) {
    if (Object.defineProperty(l, "$response", {
      value: i
    }), !("$metadata" in l)) {
      const f = "Deserialization error: to see the raw response, inspect the hidden field {error}.$response on this object.";
      try {
        l.message += `
  ` + f;
      } catch {
        !s.logger || ((a = (o = s.logger) == null ? void 0 : o.constructor) == null ? void 0 : a.name) === "NoOpLogger" || (u = (c = s.logger) == null ? void 0 : c.warn) == null || u.call(c, f);
      }
      typeof l.$responseBodyText < "u" && l.$response && (l.$response.body = l.$responseBodyText);
    }
    throw l;
  }
};
var Tc = (e, t) => (r, s) => async (n) => {
  var a;
  const i = (a = s.endpointV2) != null && a.url && e.urlParser ? async () => e.urlParser(s.endpointV2.url) : e.endpoint;
  if (!i)
    throw new Error("No valid endpoint provider available.");
  const o = await t(n.input, { ...e, endpoint: i });
  return r({
    ...n,
    request: o
  });
};
var Bc = {
  name: "deserializerMiddleware",
  step: "deserialize",
  tags: ["DESERIALIZER"],
  override: true
};
var Es = {
  name: "serializerMiddleware",
  step: "serialize",
  tags: ["SERIALIZER"],
  override: true
};
function fo(e, t, r) {
  return {
    applyToStack: (s) => {
      s.add(kc(e, r), Bc), s.add(Tc(e, t), Es);
    }
  };
}
Es.name;
var Nc = (e) => (t) => {
  throw t;
};
var Ic = (e, t) => {
};
var Pc = (e) => (t, r) => async (s) => {
  if (!J.isInstance(s.request))
    return t(s);
  const i = pt(r).selectedHttpAuthScheme;
  if (!i)
    throw new Error("No HttpAuthScheme was selected: unable to sign request");
  const { httpAuthOption: { signingProperties: o = {} }, identity: a, signer: c } = i, u = await t({
    ...s,
    request: await c.sign(s.request, a, o)
  }).catch((c.errorHandler || Nc)(o));
  return (c.successHandler || Ic)(u.response, o), u;
};
var ho = {
  step: "finalizeRequest",
  tags: ["HTTP_SIGNING"],
  name: "httpSigningMiddleware",
  aliases: ["apiKeyMiddleware", "tokenMiddleware", "awsAuthMiddleware"],
  override: true,
  relation: "after",
  toMiddleware: "retryMiddleware"
};
var _c = (e) => ({
  applyToStack: (t) => {
    t.addRelativeTo(Pc(), ho);
  }
});
var it = (e) => {
  if (typeof e == "function")
    return e;
  const t = Promise.resolve(e);
  return () => t;
};
var Ve = {};
var gt = new Array(64);
for (let e = 0, t = "A".charCodeAt(0), r = "Z".charCodeAt(0); e + t <= r; e++) {
  const s = String.fromCharCode(e + t);
  Ve[s] = e, gt[e] = s;
}
for (let e = 0, t = "a".charCodeAt(0), r = "z".charCodeAt(0); e + t <= r; e++) {
  const s = String.fromCharCode(e + t), n = e + 26;
  Ve[s] = n, gt[n] = s;
}
for (let e = 0; e < 10; e++) {
  Ve[e.toString(10)] = e + 52;
  const t = e.toString(10), r = e + 52;
  Ve[t] = r, gt[r] = t;
}
Ve["+"] = 62;
gt[62] = "+";
Ve["/"] = 63;
gt[63] = "/";
var at = 6;
var Ct = 8;
var Mc = 63;
var xs = (e) => {
  let t = e.length / 4 * 3;
  e.slice(-2) === "==" ? t -= 2 : e.slice(-1) === "=" && t--;
  const r = new ArrayBuffer(t), s = new DataView(r);
  for (let n = 0; n < e.length; n += 4) {
    let i = 0, o = 0;
    for (let u = n, l = n + 3; u <= l; u++)
      if (e[u] !== "=") {
        if (!(e[u] in Ve))
          throw new TypeError(`Invalid character ${e[u]} in base64 string.`);
        i |= Ve[e[u]] << (l - u) * at, o += at;
      } else
        i >>= at;
    const a = n / 4 * 3;
    i >>= o % Ct;
    const c = Math.floor(o / Ct);
    for (let u = 0; u < c; u++) {
      const l = (c - u - 1) * Ct;
      s.setUint8(a + u, (i & 255 << l) >> l);
    }
  }
  return new Uint8Array(r);
};
var et = (e) => new TextEncoder().encode(e);
var ct = (e) => typeof e == "string" ? et(e) : ArrayBuffer.isView(e) ? new Uint8Array(e.buffer, e.byteOffset, e.byteLength / Uint8Array.BYTES_PER_ELEMENT) : new Uint8Array(e);
var Ss = (e) => {
  if (typeof e == "string")
    return e;
  if (typeof e != "object" || typeof e.byteOffset != "number" || typeof e.byteLength != "number")
    throw new Error("@smithy/util-utf8: toUtf8 encoder function only accepts string | Uint8Array.");
  return new TextDecoder("utf-8").decode(e);
};
function ir(e) {
  let t;
  typeof e == "string" ? t = et(e) : t = e;
  const r = typeof t == "object" && typeof t.length == "number", s = typeof t == "object" && typeof t.byteOffset == "number" && typeof t.byteLength == "number";
  if (!r && !s)
    throw new Error("@smithy/util-base64: toBase64 encoder function only accepts string | Uint8Array.");
  let n = "";
  for (let i = 0; i < t.length; i += 3) {
    let o = 0, a = 0;
    for (let u = i, l = Math.min(i + 3, t.length); u < l; u++)
      o |= t[u] << (l - u - 1) * Ct, a += Ct;
    const c = Math.ceil(a / at);
    o <<= c * at - a;
    for (let u = 1; u <= c; u++) {
      const l = (c - u) * at;
      n += gt[(o & Mc << l) >> l];
    }
    n += "==".slice(0, 4 - c);
  }
  return n;
}
function Oc(e, t = "utf-8") {
  return t === "base64" ? ir(e) : Ss(e);
}
function Fc(e, t) {
  return t === "base64" ? Ye.mutate(xs(e)) : Ye.mutate(et(e));
}
var Ye = class _Ye extends Uint8Array {
  static fromString(t, r = "utf-8") {
    switch (typeof t) {
      case "string":
        return Fc(t, r);
      default:
        throw new Error(`Unsupported conversion from ${typeof t} to Uint8ArrayBlobAdapter.`);
    }
  }
  static mutate(t) {
    return Object.setPrototypeOf(t, _Ye.prototype), t;
  }
  transformToString(t = "utf-8") {
    return Oc(this, t);
  }
};
var Dc = typeof ReadableStream == "function" ? ReadableStream : function() {
};
var Uc = class extends Dc {
};
var ns = (e) => {
  var t;
  return typeof ReadableStream == "function" && (((t = e == null ? void 0 : e.constructor) == null ? void 0 : t.name) === ReadableStream.name || e instanceof ReadableStream);
};
var $c = ({ expectedChecksum: e, checksum: t, source: r, checksumSourceLocation: s, base64Encoder: n }) => {
  var c;
  if (!ns(r))
    throw new Error(`@smithy/util-stream: unsupported source type ${((c = r == null ? void 0 : r.constructor) == null ? void 0 : c.name) ?? r} in ChecksumStream.`);
  const i = n ?? ir;
  if (typeof TransformStream != "function")
    throw new Error("@smithy/util-stream: unable to instantiate ChecksumStream because API unavailable: ReadableStream/TransformStream.");
  const o = new TransformStream({
    start() {
    },
    async transform(u, l) {
      t.update(u), l.enqueue(u);
    },
    async flush(u) {
      const l = await t.digest(), f = i(l);
      if (e !== f) {
        const p = new Error(`Checksum mismatch: expected "${e}" but received "${f}" in response header "${s}".`);
        u.error(p);
      } else
        u.terminate();
    }
  });
  r.pipeThrough(o);
  const a = o.readable;
  return Object.setPrototypeOf(a, Uc.prototype), a;
};
var Lc = class {
  constructor(t) {
    this.allocByteArray = t, this.byteLength = 0, this.byteArrays = [];
  }
  push(t) {
    this.byteArrays.push(t), this.byteLength += t.byteLength;
  }
  flush() {
    if (this.byteArrays.length === 1) {
      const s = this.byteArrays[0];
      return this.reset(), s;
    }
    const t = this.allocByteArray(this.byteLength);
    let r = 0;
    for (let s = 0; s < this.byteArrays.length; ++s) {
      const n = this.byteArrays[s];
      t.set(n, r), r += n.byteLength;
    }
    return this.reset(), t;
  }
  reset() {
    this.byteArrays = [], this.byteLength = 0;
  }
};
function Hc(e, t, r) {
  const s = e.getReader();
  let n = false, i = 0;
  const o = ["", new Lc((u) => new Uint8Array(u))];
  let a = -1;
  const c = async (u) => {
    const { value: l, done: f } = await s.read(), p = l;
    if (f) {
      if (a !== -1) {
        const g = wr(o, a);
        vt2(g) > 0 && u.enqueue(g);
      }
      u.close();
    } else {
      const g = Vc(p);
      if (a !== g && (a >= 0 && u.enqueue(wr(o, a)), a = g), a === -1) {
        u.enqueue(p);
        return;
      }
      const E = vt2(p);
      i += E;
      const k = vt2(o[a]);
      if (E >= t && k === 0)
        u.enqueue(p);
      else {
        const S = qc(o, a, p);
        !n && i > t * 2 && (n = true, r == null || r.warn(`@smithy/util-stream - stream chunk size ${E} is below threshold of ${t}, automatically buffering.`)), S >= t ? u.enqueue(wr(o, a)) : await c(u);
      }
    }
  };
  return new ReadableStream({
    pull: c
  });
}
var zc = Hc;
function qc(e, t, r) {
  switch (t) {
    case 0:
      return e[0] += r, vt2(e[0]);
    case 1:
    case 2:
      return e[t].push(r), vt2(e[t]);
  }
}
function wr(e, t) {
  switch (t) {
    case 0:
      const r = e[0];
      return e[0] = "", r;
    case 1:
    case 2:
      return e[t].flush();
  }
  throw new Error(`@smithy/util-stream - invalid index ${t} given to flush()`);
}
function vt2(e) {
  return (e == null ? void 0 : e.byteLength) ?? (e == null ? void 0 : e.length) ?? 0;
}
function Vc(e) {
  return typeof Buffer < "u" && e instanceof Buffer ? 2 : e instanceof Uint8Array ? 1 : typeof e == "string" ? 0 : -1;
}
var jc = (e, t) => {
  const { base64Encoder: r, bodyLengthChecker: s, checksumAlgorithmFn: n, checksumLocationName: i, streamHasher: o } = t, a = r !== void 0 && s !== void 0 && n !== void 0 && i !== void 0 && o !== void 0, c = a ? o(n, e) : void 0, u = e.getReader();
  return new ReadableStream({
    async pull(l) {
      const { value: f, done: p } = await u.read();
      if (p) {
        if (l.enqueue(`0\r
`), a) {
          const g = r(await c);
          l.enqueue(`${i}:${g}\r
`), l.enqueue(`\r
`);
        }
        l.close();
      } else
        l.enqueue(`${(s(f) || 0).toString(16)}\r
${f}\r
`);
    }
  });
};
async function Wc(e, t) {
  let r = 0;
  const s = [], n = e.getReader();
  let i = false;
  for (; !i; ) {
    const { done: c, value: u } = await n.read();
    if (u && (s.push(u), r += (u == null ? void 0 : u.byteLength) ?? 0), r >= t)
      break;
    i = c;
  }
  n.releaseLock();
  const o = new Uint8Array(Math.min(t, r));
  let a = 0;
  for (const c of s) {
    if (c.byteLength > o.byteLength - a) {
      o.set(c.subarray(0, o.byteLength - a), a);
      break;
    } else
      o.set(c, a);
    a += c.length;
  }
  return o;
}
var Je = (e) => encodeURIComponent(e).replace(/[!'()*]/g, Gc);
var Gc = (e) => `%${e.charCodeAt(0).toString(16).toUpperCase()}`;
function Kc(e) {
  const t = [];
  for (let r of Object.keys(e).sort()) {
    const s = e[r];
    if (r = Je(r), Array.isArray(s))
      for (let n = 0, i = s.length; n < i; n++)
        t.push(`${r}=${Je(s[n])}`);
    else {
      let n = r;
      (s || typeof s == "string") && (n += `=${Je(s)}`), t.push(n);
    }
  }
  return t.join("&");
}
function ln(e, t) {
  return new Request(e, t);
}
function Xc(e = 0) {
  return new Promise((t, r) => {
    e && setTimeout(() => {
      const s = new Error(`Request did not complete within ${e} ms`);
      s.name = "TimeoutError", r(s);
    }, e);
  });
}
var br = {
  supported: void 0
};
var As = class _As {
  static create(t) {
    return typeof (t == null ? void 0 : t.handle) == "function" ? t : new _As(t);
  }
  constructor(t) {
    typeof t == "function" ? this.configProvider = t().then((r) => r || {}) : (this.config = t ?? {}, this.configProvider = Promise.resolve(this.config)), br.supported === void 0 && (br.supported = typeof Request < "u" && "keepalive" in ln("https://[::1]"));
  }
  destroy() {
  }
  async handle(t, { abortSignal: r } = {}) {
    var N;
    this.config || (this.config = await this.configProvider);
    const s = this.config.requestTimeout, n = this.config.keepAlive === true, i = this.config.credentials;
    if (r != null && r.aborted) {
      const P = new Error("Request aborted");
      return P.name = "AbortError", Promise.reject(P);
    }
    let o = t.path;
    const a = Kc(t.query || {});
    a && (o += `?${a}`), t.fragment && (o += `#${t.fragment}`);
    let c = "";
    if (t.username != null || t.password != null) {
      const P = t.username ?? "", V = t.password ?? "";
      c = `${P}:${V}@`;
    }
    const { port: u, method: l } = t, f = `${t.protocol}//${c}${t.hostname}${u ? `:${u}` : ""}${o}`, p = l === "GET" || l === "HEAD" ? void 0 : t.body, g = {
      body: p,
      headers: new Headers(t.headers),
      method: l,
      credentials: i
    };
    (N = this.config) != null && N.cache && (g.cache = this.config.cache), p && (g.duplex = "half"), typeof AbortController < "u" && (g.signal = r), br.supported && (g.keepalive = n), typeof this.config.requestInit == "function" && Object.assign(g, this.config.requestInit(t));
    let E = () => {
    };
    const k = ln(f, g), S = [
      fetch(k).then((P) => {
        const V = P.headers, H = {};
        for (const be of V.entries())
          H[be[0]] = be[1];
        return P.body != null ? {
          response: new Nt({
            headers: H,
            reason: P.statusText,
            statusCode: P.status,
            body: P.body
          })
        } : P.blob().then((be) => ({
          response: new Nt({
            headers: H,
            reason: P.statusText,
            statusCode: P.status,
            body: be
          })
        }));
      }),
      Xc(s)
    ];
    return r && S.push(new Promise((P, V) => {
      const H = () => {
        const pe = new Error("Request aborted");
        pe.name = "AbortError", V(pe);
      };
      if (typeof r.addEventListener == "function") {
        const pe = r;
        pe.addEventListener("abort", H, { once: true }), E = () => pe.removeEventListener("abort", H);
      } else
        r.onabort = H;
    })), Promise.race(S).finally(E);
  }
  updateHttpClientConfig(t, r) {
    this.config = void 0, this.configProvider = this.configProvider.then((s) => (s[t] = r, s));
  }
  httpHandlerConfigs() {
    return this.config ?? {};
  }
};
var po = async (e) => {
  var t;
  return typeof Blob == "function" && e instanceof Blob || ((t = e.constructor) == null ? void 0 : t.name) === "Blob" ? Blob.prototype.arrayBuffer !== void 0 ? new Uint8Array(await e.arrayBuffer()) : Zc(e) : Qc(e);
};
async function Zc(e) {
  const t = await Yc(e), r = xs(t);
  return new Uint8Array(r);
}
async function Qc(e) {
  const t = [], r = e.getReader();
  let s = false, n = 0;
  for (; !s; ) {
    const { done: a, value: c } = await r.read();
    c && (t.push(c), n += c.length), s = a;
  }
  const i = new Uint8Array(n);
  let o = 0;
  for (const a of t)
    i.set(a, o), o += a.length;
  return i;
}
function Yc(e) {
  return new Promise((t, r) => {
    const s = new FileReader();
    s.onloadend = () => {
      if (s.readyState !== 2)
        return r(new Error("Reader aborted too early"));
      const n = s.result ?? "", i = n.indexOf(","), o = i > -1 ? i + 1 : n.length;
      t(n.substring(o));
    }, s.onabort = () => r(new Error("Read aborted")), s.onerror = () => r(s.error), s.readAsDataURL(e);
  });
}
var go = {};
var is = {};
for (let e = 0; e < 256; e++) {
  let t = e.toString(16).toLowerCase();
  t.length === 1 && (t = `0${t}`), go[e] = t, is[t] = e;
}
function mo(e) {
  if (e.length % 2 !== 0)
    throw new Error("Hex encoded strings must have an even number length");
  const t = new Uint8Array(e.length / 2);
  for (let r = 0; r < e.length; r += 2) {
    const s = e.slice(r, r + 2).toLowerCase();
    if (s in is)
      t[r / 2] = is[s];
    else
      throw new Error(`Cannot decode unrecognized sequence ${s} as hexadecimal`);
  }
  return t;
}
function Ee(e) {
  let t = "";
  for (let r = 0; r < e.byteLength; r++)
    t += go[e[r]];
  return t;
}
var fn = "The stream has already been transformed.";
var Jc = (e) => {
  var n, i;
  if (!hn(e) && !ns(e)) {
    const o = ((i = (n = e == null ? void 0 : e.__proto__) == null ? void 0 : n.constructor) == null ? void 0 : i.name) || e;
    throw new Error(`Unexpected stream implementation, expect Blob or ReadableStream, got ${o}`);
  }
  let t = false;
  const r = async () => {
    if (t)
      throw new Error(fn);
    return t = true, await po(e);
  }, s = (o) => {
    if (typeof o.stream != "function")
      throw new Error(`Cannot transform payload Blob to web stream. Please make sure the Blob.stream() is polyfilled.
If you are using React Native, this API is not yet supported, see: https://react-native.canny.io/feature-requests/p/fetch-streaming-body`);
    return o.stream();
  };
  return Object.assign(e, {
    transformToByteArray: r,
    transformToString: async (o) => {
      const a = await r();
      if (o === "base64")
        return ir(a);
      if (o === "hex")
        return Ee(a);
      if (o === void 0 || o === "utf8" || o === "utf-8")
        return Ss(a);
      if (typeof TextDecoder == "function")
        return new TextDecoder(o).decode(a);
      throw new Error("TextDecoder is not available, please make sure polyfill is provided.");
    },
    transformToWebStream: () => {
      if (t)
        throw new Error(fn);
      if (t = true, hn(e))
        return s(e);
      if (ns(e))
        return e;
      throw new Error(`Cannot transform payload to web stream, got ${e}`);
    }
  });
};
var hn = (e) => typeof Blob == "function" && e instanceof Blob;
async function eu(e) {
  return typeof e.stream == "function" && (e = e.stream()), e.tee();
}
var yo = async (e = new Uint8Array(), t) => {
  if (e instanceof Uint8Array)
    return Ye.mutate(e);
  if (!e)
    return Ye.mutate(new Uint8Array());
  const r = t.streamCollector(e);
  return Ye.mutate(await r);
};
function pn(e) {
  return encodeURIComponent(e).replace(/[!'()*]/g, function(t) {
    return "%" + t.charCodeAt(0).toString(16).toUpperCase();
  });
}
var tu = (e, t, r, s, n, i) => {
  if (t != null && t[r] !== void 0) {
    const o = s();
    if (o.length <= 0)
      throw new Error("Empty value provided for input HTTP label: " + r + ".");
    e = e.replace(n, i ? o.split("/").map((a) => pn(a)).join("/") : pn(o));
  } else
    throw new Error("No value provided for input HTTP label: " + r + ".");
  return e;
};
function wo(e, t) {
  return new ru(e, t);
}
var ru = class {
  constructor(t, r) {
    this.input = t, this.context = r, this.query = {}, this.method = "", this.headers = {}, this.path = "", this.body = null, this.hostname = "", this.resolvePathStack = [];
  }
  async build() {
    const { hostname: t, protocol: r = "https", port: s, path: n } = await this.context.endpoint();
    this.path = n;
    for (const i of this.resolvePathStack)
      i(this.path);
    return new J({
      protocol: r,
      hostname: this.hostname || t,
      port: s,
      method: this.method,
      path: this.path,
      query: this.query,
      body: this.body,
      headers: this.headers
    });
  }
  hn(t) {
    return this.hostname = t, this;
  }
  bp(t) {
    return this.resolvePathStack.push((r) => {
      this.path = `${r != null && r.endsWith("/") ? r.slice(0, -1) : r || ""}` + t;
    }), this;
  }
  p(t, r, s, n) {
    return this.resolvePathStack.push((i) => {
      this.path = tu(i, this.input, t, r, s, n);
    }), this;
  }
  h(t) {
    return this.headers = t, this;
  }
  q(t) {
    return this.query = t, this;
  }
  b(t) {
    return this.body = t, this;
  }
  m(t) {
    return this.method = t, this;
  }
};
function su(e, t, r) {
  e.__smithy_context ? e.__smithy_context.features || (e.__smithy_context.features = {}) : e.__smithy_context = {
    features: {}
  }, e.__smithy_context.features[t] = r;
}
var nu = class {
  constructor(t) {
    this.authSchemes = /* @__PURE__ */ new Map();
    for (const [r, s] of Object.entries(t))
      s !== void 0 && this.authSchemes.set(r, s);
  }
  getIdentityProvider(t) {
    return this.authSchemes.get(t);
  }
};
var iu = (e) => (t) => bo(t) && t.expiration.getTime() - Date.now() < e;
var ou = 3e5;
var au = iu(ou);
var bo = (e) => e.expiration !== void 0;
var cu = (e, t, r) => {
  if (e === void 0)
    return;
  const s = typeof e != "function" ? async () => Promise.resolve(e) : e;
  let n, i, o, a = false;
  const c = async (u) => {
    i || (i = s(u));
    try {
      n = await i, o = true, a = false;
    } finally {
      i = void 0;
    }
    return n;
  };
  return t === void 0 ? async (u) => ((!o || u != null && u.forceRefresh) && (n = await c(u)), n) : async (u) => ((!o || u != null && u.forceRefresh) && (n = await c(u)), a ? n : r(n) ? (t(n) && await c(u), n) : (a = true, n));
};
var uu = (e, t, r) => {
  let s, n, i, o = false;
  const a = async () => {
    n || (n = e());
    try {
      s = await n, i = true, o = false;
    } finally {
      n = void 0;
    }
    return s;
  };
  return t === void 0 ? async (c) => ((!i || c != null && c.forceRefresh) && (s = await a()), s) : async (c) => ((!i || c != null && c.forceRefresh) && (s = await a()), o ? s : r && !r(s) ? (o = true, s) : (t(s) && await a(), s));
};
var du = (e) => (e.sigv4aSigningRegionSet = it(e.sigv4aSigningRegionSet), e);
var lu = "X-Amz-Algorithm";
var fu = "X-Amz-Credential";
var Eo = "X-Amz-Date";
var hu = "X-Amz-SignedHeaders";
var pu = "X-Amz-Expires";
var xo = "X-Amz-Signature";
var So = "X-Amz-Security-Token";
var Ao = "authorization";
var Co = Eo.toLowerCase();
var gu = "date";
var mu = [Ao, Co, gu];
var yu = xo.toLowerCase();
var os = "x-amz-content-sha256";
var wu = So.toLowerCase();
var bu = {
  authorization: true,
  "cache-control": true,
  connection: true,
  expect: true,
  from: true,
  "keep-alive": true,
  "max-forwards": true,
  pragma: true,
  referer: true,
  te: true,
  trailer: true,
  "transfer-encoding": true,
  upgrade: true,
  "user-agent": true,
  "x-amzn-trace-id": true
};
var Eu = /^proxy-/;
var xu = /^sec-/;
var Er = "AWS4-HMAC-SHA256";
var Su = "AWS4-HMAC-SHA256-PAYLOAD";
var Au = "UNSIGNED-PAYLOAD";
var Cu = 50;
var vo = "aws4_request";
var vu = 60 * 60 * 24 * 7;
var Mt = {};
var xr = [];
var Sr = (e, t, r) => `${e}/${t}/${r}/${vo}`;
var Ru = async (e, t, r, s, n) => {
  const i = await gn(e, t.secretAccessKey, t.accessKeyId), o = `${r}:${s}:${n}:${Ee(i)}:${t.sessionToken}`;
  if (o in Mt)
    return Mt[o];
  for (xr.push(o); xr.length > Cu; )
    delete Mt[xr.shift()];
  let a = `AWS4${t.secretAccessKey}`;
  for (const c of [r, s, n, vo])
    a = await gn(e, a, c);
  return Mt[o] = a;
};
var gn = (e, t, r) => {
  const s = new e(t);
  return s.update(ct(r)), s.digest();
};
var mn = ({ headers: e }, t, r) => {
  const s = {};
  for (const n of Object.keys(e).sort()) {
    if (e[n] == null)
      continue;
    const i = n.toLowerCase();
    (i in bu || t != null && t.has(i) || Eu.test(i) || xu.test(i)) && (!r || r && !r.has(i)) || (s[i] = e[n].trim().replace(/\s+/g, " "));
  }
  return s;
};
var ku = ({ query: e = {} }) => {
  const t = [], r = {};
  for (const s of Object.keys(e)) {
    if (s.toLowerCase() === yu)
      continue;
    const n = Je(s);
    t.push(n);
    const i = e[s];
    typeof i == "string" ? r[n] = `${n}=${Je(i)}` : Array.isArray(i) && (r[n] = i.slice(0).reduce((o, a) => o.concat([`${n}=${Je(a)}`]), []).sort().join("&"));
  }
  return t.sort().map((s) => r[s]).filter((s) => s).join("&");
};
var Ro = (e) => typeof ArrayBuffer == "function" && e instanceof ArrayBuffer || Object.prototype.toString.call(e) === "[object ArrayBuffer]";
var Ar = async ({ headers: e, body: t }, r) => {
  for (const s of Object.keys(e))
    if (s.toLowerCase() === os)
      return e[s];
  if (t == null)
    return "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855";
  if (typeof t == "string" || ArrayBuffer.isView(t) || Ro(t)) {
    const s = new r();
    return s.update(ct(t)), Ee(await s.digest());
  }
  return Au;
};
var Tu = class {
  format(t) {
    const r = [];
    for (const i of Object.keys(t)) {
      const o = et(i);
      r.push(Uint8Array.from([o.byteLength]), o, this.formatHeaderValue(t[i]));
    }
    const s = new Uint8Array(r.reduce((i, o) => i + o.byteLength, 0));
    let n = 0;
    for (const i of r)
      s.set(i, n), n += i.byteLength;
    return s;
  }
  formatHeaderValue(t) {
    switch (t.type) {
      case "boolean":
        return Uint8Array.from([t.value ? 0 : 1]);
      case "byte":
        return Uint8Array.from([2, t.value]);
      case "short":
        const r = new DataView(new ArrayBuffer(3));
        return r.setUint8(0, 3), r.setInt16(1, t.value, false), new Uint8Array(r.buffer);
      case "integer":
        const s = new DataView(new ArrayBuffer(5));
        return s.setUint8(0, 4), s.setInt32(1, t.value, false), new Uint8Array(s.buffer);
      case "long":
        const n = new Uint8Array(9);
        return n[0] = 5, n.set(t.value.bytes, 1), n;
      case "binary":
        const i = new DataView(new ArrayBuffer(3 + t.value.byteLength));
        i.setUint8(0, 6), i.setUint16(1, t.value.byteLength, false);
        const o = new Uint8Array(i.buffer);
        return o.set(t.value, 3), o;
      case "string":
        const a = et(t.value), c = new DataView(new ArrayBuffer(3 + a.byteLength));
        c.setUint8(0, 7), c.setUint16(1, a.byteLength, false);
        const u = new Uint8Array(c.buffer);
        return u.set(a, 3), u;
      case "timestamp":
        const l = new Uint8Array(9);
        return l[0] = 8, l.set(Nu.fromNumber(t.value.valueOf()).bytes, 1), l;
      case "uuid":
        if (!Bu.test(t.value))
          throw new Error(`Invalid UUID received: ${t.value}`);
        const f = new Uint8Array(17);
        return f[0] = 9, f.set(mo(t.value.replace(/\-/g, "")), 1), f;
    }
  }
};
var yn;
(function(e) {
  e[e.boolTrue = 0] = "boolTrue", e[e.boolFalse = 1] = "boolFalse", e[e.byte = 2] = "byte", e[e.short = 3] = "short", e[e.integer = 4] = "integer", e[e.long = 5] = "long", e[e.byteArray = 6] = "byteArray", e[e.string = 7] = "string", e[e.timestamp = 8] = "timestamp", e[e.uuid = 9] = "uuid";
})(yn || (yn = {}));
var Bu = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;
var Nu = class ko {
  constructor(t) {
    if (this.bytes = t, t.byteLength !== 8)
      throw new Error("Int64 buffers must be exactly 8 bytes");
  }
  static fromNumber(t) {
    if (t > 9223372036854776e3 || t < -9223372036854776e3)
      throw new Error(`${t} is too large (or, if negative, too small) to represent as an Int64`);
    const r = new Uint8Array(8);
    for (let s = 7, n = Math.abs(Math.round(t)); s > -1 && n > 0; s--, n /= 256)
      r[s] = n;
    return t < 0 && wn(r), new ko(r);
  }
  valueOf() {
    const t = this.bytes.slice(0), r = t[0] & 128;
    return r && wn(t), parseInt(Ee(t), 16) * (r ? -1 : 1);
  }
  toString() {
    return String(this.valueOf());
  }
};
function wn(e) {
  for (let t = 0; t < 8; t++)
    e[t] ^= 255;
  for (let t = 7; t > -1 && (e[t]++, e[t] === 0); t--)
    ;
}
var Iu = (e, t) => {
  e = e.toLowerCase();
  for (const r of Object.keys(t))
    if (e === r.toLowerCase())
      return true;
  return false;
};
var Pu = (e, t = {}) => {
  var n, i;
  const { headers: r, query: s = {} } = J.clone(e);
  for (const o of Object.keys(r)) {
    const a = o.toLowerCase();
    (a.slice(0, 6) === "x-amz-" && !((n = t.unhoistableHeaders) != null && n.has(a)) || (i = t.hoistableHeaders) != null && i.has(a)) && (s[o] = r[o], delete r[o]);
  }
  return {
    ...e,
    headers: r,
    query: s
  };
};
var bn = (e) => {
  e = J.clone(e);
  for (const t of Object.keys(e.headers))
    mu.indexOf(t.toLowerCase()) > -1 && delete e.headers[t];
  return e;
};
var _u = (e) => Mu(e).toISOString().replace(/\.\d{3}Z$/, "Z");
var Mu = (e) => typeof e == "number" ? new Date(e * 1e3) : typeof e == "string" ? Number(e) ? new Date(Number(e) * 1e3) : new Date(e) : e;
var as = class {
  constructor({ applyChecksum: t, credentials: r, region: s, service: n, sha256: i, uriEscapePath: o = true }) {
    this.headerFormatter = new Tu(), this.service = n, this.sha256 = i, this.uriEscapePath = o, this.applyChecksum = typeof t == "boolean" ? t : true, this.regionProvider = Pe(s), this.credentialProvider = Pe(r);
  }
  async presign(t, r = {}) {
    const { signingDate: s = /* @__PURE__ */ new Date(), expiresIn: n = 3600, unsignableHeaders: i, unhoistableHeaders: o, signableHeaders: a, hoistableHeaders: c, signingRegion: u, signingService: l } = r, f = await this.credentialProvider();
    this.validateResolvedCredentials(f);
    const p = u ?? await this.regionProvider(), { longDate: g, shortDate: E } = Ot(s);
    if (n > vu)
      return Promise.reject("Signature version 4 presigned URLs must have an expiration date less than one week in the future");
    const k = Sr(E, p, l ?? this.service), S = Pu(bn(t), { unhoistableHeaders: o, hoistableHeaders: c });
    f.sessionToken && (S.query[So] = f.sessionToken), S.query[lu] = Er, S.query[fu] = `${f.accessKeyId}/${k}`, S.query[Eo] = g, S.query[pu] = n.toString(10);
    const N = mn(S, i, a);
    return S.query[hu] = En(N), S.query[xo] = await this.getSignature(g, k, this.getSigningKey(f, p, E, l), this.createCanonicalRequest(S, N, await Ar(t, this.sha256))), S;
  }
  async sign(t, r) {
    return typeof t == "string" ? this.signString(t, r) : t.headers && t.payload ? this.signEvent(t, r) : t.message ? this.signMessage(t, r) : this.signRequest(t, r);
  }
  async signEvent({ headers: t, payload: r }, { signingDate: s = /* @__PURE__ */ new Date(), priorSignature: n, signingRegion: i, signingService: o }) {
    const a = i ?? await this.regionProvider(), { shortDate: c, longDate: u } = Ot(s), l = Sr(c, a, o ?? this.service), f = await Ar({ headers: {}, body: r }, this.sha256), p = new this.sha256();
    p.update(t);
    const g = Ee(await p.digest()), E = [
      Su,
      u,
      l,
      n,
      g,
      f
    ].join(`
`);
    return this.signString(E, { signingDate: s, signingRegion: a, signingService: o });
  }
  async signMessage(t, { signingDate: r = /* @__PURE__ */ new Date(), signingRegion: s, signingService: n }) {
    return this.signEvent({
      headers: this.headerFormatter.format(t.message.headers),
      payload: t.message.body
    }, {
      signingDate: r,
      signingRegion: s,
      signingService: n,
      priorSignature: t.priorSignature
    }).then((o) => ({ message: t.message, signature: o }));
  }
  async signString(t, { signingDate: r = /* @__PURE__ */ new Date(), signingRegion: s, signingService: n } = {}) {
    const i = await this.credentialProvider();
    this.validateResolvedCredentials(i);
    const o = s ?? await this.regionProvider(), { shortDate: a } = Ot(r), c = new this.sha256(await this.getSigningKey(i, o, a, n));
    return c.update(ct(t)), Ee(await c.digest());
  }
  async signRequest(t, { signingDate: r = /* @__PURE__ */ new Date(), signableHeaders: s, unsignableHeaders: n, signingRegion: i, signingService: o } = {}) {
    const a = await this.credentialProvider();
    this.validateResolvedCredentials(a);
    const c = i ?? await this.regionProvider(), u = bn(t), { longDate: l, shortDate: f } = Ot(r), p = Sr(f, c, o ?? this.service);
    u.headers[Co] = l, a.sessionToken && (u.headers[wu] = a.sessionToken);
    const g = await Ar(u, this.sha256);
    !Iu(os, u.headers) && this.applyChecksum && (u.headers[os] = g);
    const E = mn(u, n, s), k = await this.getSignature(l, p, this.getSigningKey(a, c, f, o), this.createCanonicalRequest(u, E, g));
    return u.headers[Ao] = `${Er} Credential=${a.accessKeyId}/${p}, SignedHeaders=${En(E)}, Signature=${k}`, u;
  }
  createCanonicalRequest(t, r, s) {
    const n = Object.keys(r).sort();
    return `${t.method}
${this.getCanonicalPath(t)}
${ku(t)}
${n.map((i) => `${i}:${r[i]}`).join(`
`)}

${n.join(";")}
${s}`;
  }
  async createStringToSign(t, r, s) {
    const n = new this.sha256();
    n.update(ct(s));
    const i = await n.digest();
    return `${Er}
${t}
${r}
${Ee(i)}`;
  }
  getCanonicalPath({ path: t }) {
    if (this.uriEscapePath) {
      const r = [];
      for (const i of t.split("/"))
        (i == null ? void 0 : i.length) !== 0 && i !== "." && (i === ".." ? r.pop() : r.push(i));
      const s = `${t != null && t.startsWith("/") ? "/" : ""}${r.join("/")}${r.length > 0 && (t != null && t.endsWith("/")) ? "/" : ""}`;
      return Je(s).replace(/%2F/g, "/");
    }
    return t;
  }
  async getSignature(t, r, s, n) {
    const i = await this.createStringToSign(t, r, n), o = new this.sha256(await s);
    return o.update(ct(i)), Ee(await o.digest());
  }
  getSigningKey(t, r, s, n) {
    return Ru(this.sha256, t, s, r, n || this.service);
  }
  validateResolvedCredentials(t) {
    if (typeof t != "object" || typeof t.accessKeyId != "string" || typeof t.secretAccessKey != "string")
      throw new Error("Resolved credential object is not valid");
  }
};
var Ot = (e) => {
  const t = _u(e).replace(/[\-:]/g, "");
  return {
    longDate: t,
    shortDate: t.slice(0, 8)
  };
};
var En = (e) => Object.keys(e).sort().join(";");
var Ou = (e) => {
  let t = false, r;
  e.credentials && (t = true, r = cu(e.credentials, au, bo)), r || (e.credentialDefaultProvider ? r = it(e.credentialDefaultProvider(Object.assign({}, e, {
    parentClientConfig: e
  }))) : r = async () => {
    throw new Error("`credentials` is missing");
  });
  const s = async () => r({ callerClientConfig: e }), { signingEscapePath: n = true, systemClockOffset: i = e.systemClockOffset || 0, sha256: o } = e;
  let a;
  return e.signer ? a = it(e.signer) : e.regionInfoProvider ? a = () => it(e.region)().then(async (c) => [
    await e.regionInfoProvider(c, {
      useFipsEndpoint: await e.useFipsEndpoint(),
      useDualstackEndpoint: await e.useDualstackEndpoint()
    }) || {},
    c
  ]).then(([c, u]) => {
    const { signingRegion: l, signingService: f } = c;
    e.signingRegion = e.signingRegion || l || u, e.signingName = e.signingName || f || e.serviceId;
    const p = {
      ...e,
      credentials: s,
      region: e.signingRegion,
      service: e.signingName,
      sha256: o,
      uriEscapePath: n
    }, g = e.signerConstructor || as;
    return new g(p);
  }) : a = async (c) => {
    c = Object.assign({}, {
      name: "sigv4",
      signingName: e.signingName || e.defaultSigningName,
      signingRegion: await it(e.region)(),
      properties: {}
    }, c);
    const u = c.signingRegion, l = c.signingName;
    e.signingRegion = e.signingRegion || u, e.signingName = e.signingName || l || e.serviceId;
    const f = {
      ...e,
      credentials: s,
      region: e.signingRegion,
      service: e.signingName,
      sha256: o,
      uriEscapePath: n
    }, p = e.signerConstructor || as;
    return new p(f);
  }, {
    ...e,
    systemClockOffset: i,
    signingEscapePath: n,
    credentials: t ? async () => s().then((c) => Ec(c, "CREDENTIALS_CODE", "e")) : s,
    signer: a
  };
};
var Ke = (e, t) => {
  const r = [];
  if (e && r.push(e), t)
    for (const s of t)
      r.push(s);
  return r;
};
var Fe = (e, t) => `${e || "anonymous"}${t && t.length > 0 ? ` (a.k.a. ${t.join(",")})` : ""}`;
var Xt = () => {
  let e = [], t = [], r = false;
  const s = /* @__PURE__ */ new Set(), n = (f) => f.sort((p, g) => xn[g.step] - xn[p.step] || Sn[g.priority || "normal"] - Sn[p.priority || "normal"]), i = (f) => {
    let p = false;
    const g = (E) => {
      const k = Ke(E.name, E.aliases);
      if (k.includes(f)) {
        p = true;
        for (const S of k)
          s.delete(S);
        return false;
      }
      return true;
    };
    return e = e.filter(g), t = t.filter(g), p;
  }, o = (f) => {
    let p = false;
    const g = (E) => {
      if (E.middleware === f) {
        p = true;
        for (const k of Ke(E.name, E.aliases))
          s.delete(k);
        return false;
      }
      return true;
    };
    return e = e.filter(g), t = t.filter(g), p;
  }, a = (f) => {
    var p;
    return e.forEach((g) => {
      f.add(g.middleware, { ...g });
    }), t.forEach((g) => {
      f.addRelativeTo(g.middleware, { ...g });
    }), (p = f.identifyOnResolve) == null || p.call(f, l.identifyOnResolve()), f;
  }, c = (f) => {
    const p = [];
    return f.before.forEach((g) => {
      g.before.length === 0 && g.after.length === 0 ? p.push(g) : p.push(...c(g));
    }), p.push(f), f.after.reverse().forEach((g) => {
      g.before.length === 0 && g.after.length === 0 ? p.push(g) : p.push(...c(g));
    }), p;
  }, u = (f = false) => {
    const p = [], g = [], E = {};
    return e.forEach((S) => {
      const N = {
        ...S,
        before: [],
        after: []
      };
      for (const P of Ke(N.name, N.aliases))
        E[P] = N;
      p.push(N);
    }), t.forEach((S) => {
      const N = {
        ...S,
        before: [],
        after: []
      };
      for (const P of Ke(N.name, N.aliases))
        E[P] = N;
      g.push(N);
    }), g.forEach((S) => {
      if (S.toMiddleware) {
        const N = E[S.toMiddleware];
        if (N === void 0) {
          if (f)
            return;
          throw new Error(`${S.toMiddleware} is not found when adding ${Fe(S.name, S.aliases)} middleware ${S.relation} ${S.toMiddleware}`);
        }
        S.relation === "after" && N.after.push(S), S.relation === "before" && N.before.push(S);
      }
    }), n(p).map(c).reduce((S, N) => (S.push(...N), S), []);
  }, l = {
    add: (f, p = {}) => {
      const { name: g, override: E, aliases: k } = p, S = {
        step: "initialize",
        priority: "normal",
        middleware: f,
        ...p
      }, N = Ke(g, k);
      if (N.length > 0) {
        if (N.some((P) => s.has(P))) {
          if (!E)
            throw new Error(`Duplicate middleware name '${Fe(g, k)}'`);
          for (const P of N) {
            const V = e.findIndex((pe) => {
              var be;
              return pe.name === P || ((be = pe.aliases) == null ? void 0 : be.some((mt) => mt === P));
            });
            if (V === -1)
              continue;
            const H = e[V];
            if (H.step !== S.step || S.priority !== H.priority)
              throw new Error(`"${Fe(H.name, H.aliases)}" middleware with ${H.priority} priority in ${H.step} step cannot be overridden by "${Fe(g, k)}" middleware with ${S.priority} priority in ${S.step} step.`);
            e.splice(V, 1);
          }
        }
        for (const P of N)
          s.add(P);
      }
      e.push(S);
    },
    addRelativeTo: (f, p) => {
      const { name: g, override: E, aliases: k } = p, S = {
        middleware: f,
        ...p
      }, N = Ke(g, k);
      if (N.length > 0) {
        if (N.some((P) => s.has(P))) {
          if (!E)
            throw new Error(`Duplicate middleware name '${Fe(g, k)}'`);
          for (const P of N) {
            const V = t.findIndex((pe) => {
              var be;
              return pe.name === P || ((be = pe.aliases) == null ? void 0 : be.some((mt) => mt === P));
            });
            if (V === -1)
              continue;
            const H = t[V];
            if (H.toMiddleware !== S.toMiddleware || H.relation !== S.relation)
              throw new Error(`"${Fe(H.name, H.aliases)}" middleware ${H.relation} "${H.toMiddleware}" middleware cannot be overridden by "${Fe(g, k)}" middleware ${S.relation} "${S.toMiddleware}" middleware.`);
            t.splice(V, 1);
          }
        }
        for (const P of N)
          s.add(P);
      }
      t.push(S);
    },
    clone: () => a(Xt()),
    use: (f) => {
      f.applyToStack(l);
    },
    remove: (f) => typeof f == "string" ? i(f) : o(f),
    removeByTag: (f) => {
      let p = false;
      const g = (E) => {
        const { tags: k, name: S, aliases: N } = E;
        if (k && k.includes(f)) {
          const P = Ke(S, N);
          for (const V of P)
            s.delete(V);
          return p = true, false;
        }
        return true;
      };
      return e = e.filter(g), t = t.filter(g), p;
    },
    concat: (f) => {
      var g;
      const p = a(Xt());
      return p.use(f), p.identifyOnResolve(r || p.identifyOnResolve() || (((g = f.identifyOnResolve) == null ? void 0 : g.call(f)) ?? false)), p;
    },
    applyToStack: a,
    identify: () => u(true).map((f) => {
      const p = f.step ?? f.relation + " " + f.toMiddleware;
      return Fe(f.name, f.aliases) + " - " + p;
    }),
    identifyOnResolve(f) {
      return typeof f == "boolean" && (r = f), r;
    },
    resolve: (f, p) => {
      for (const g of u().map((E) => E.middleware).reverse())
        f = g(f, p);
      return f;
    }
  };
  return l;
};
var xn = {
  initialize: 5,
  serialize: 4,
  build: 3,
  finalizeRequest: 2,
  deserialize: 1
};
var Sn = {
  high: 3,
  normal: 2,
  low: 1
};
var Fu = class {
  constructor(t) {
    this.config = t, this.middlewareStack = Xt();
  }
  send(t, r, s) {
    const n = typeof r != "function" ? r : void 0, i = typeof r == "function" ? r : s, o = n === void 0 && this.config.cacheMiddleware === true;
    let a;
    if (o) {
      this.handlers || (this.handlers = /* @__PURE__ */ new WeakMap());
      const c = this.handlers;
      c.has(t.constructor) ? a = c.get(t.constructor) : (a = t.resolveMiddleware(this.middlewareStack, this.config, n), c.set(t.constructor, a));
    } else
      delete this.handlers, a = t.resolveMiddleware(this.middlewareStack, this.config, n);
    if (i)
      a(t).then((c) => i(null, c.output), (c) => i(c)).catch(() => {
      });
    else
      return a(t).then((c) => c.output);
  }
  destroy() {
    var t, r, s;
    (s = (r = (t = this.config) == null ? void 0 : t.requestHandler) == null ? void 0 : r.destroy) == null || s.call(r), delete this.handlers;
  }
};
var Cs = class {
  constructor() {
    this.middlewareStack = Xt();
  }
  static classBuilder() {
    return new Du();
  }
  resolveMiddlewareWithContext(t, r, s, { middlewareFn: n, clientName: i, commandName: o, inputFilterSensitiveLog: a, outputFilterSensitiveLog: c, smithyContext: u, additionalContext: l, CommandCtor: f }) {
    for (const S of n.bind(this)(f, t, r, s))
      this.middlewareStack.use(S);
    const p = t.concat(this.middlewareStack), { logger: g } = r, E = {
      logger: g,
      clientName: i,
      commandName: o,
      inputFilterSensitiveLog: a,
      outputFilterSensitiveLog: c,
      [rs]: {
        commandInstance: this,
        ...u
      },
      ...l
    }, { requestHandler: k } = r;
    return p.resolve((S) => k.handle(S.request, s || {}), E);
  }
};
var Du = class {
  constructor() {
    this._init = () => {
    }, this._ep = {}, this._middlewareFn = () => [], this._commandName = "", this._clientName = "", this._additionalContext = {}, this._smithyContext = {}, this._inputFilterSensitiveLog = (t) => t, this._outputFilterSensitiveLog = (t) => t, this._serializer = null, this._deserializer = null;
  }
  init(t) {
    this._init = t;
  }
  ep(t) {
    return this._ep = t, this;
  }
  m(t) {
    return this._middlewareFn = t, this;
  }
  s(t, r, s = {}) {
    return this._smithyContext = {
      service: t,
      operation: r,
      ...s
    }, this;
  }
  c(t = {}) {
    return this._additionalContext = t, this;
  }
  n(t, r) {
    return this._clientName = t, this._commandName = r, this;
  }
  f(t = (s) => s, r = (s) => s) {
    return this._inputFilterSensitiveLog = t, this._outputFilterSensitiveLog = r, this;
  }
  ser(t) {
    return this._serializer = t, this;
  }
  de(t) {
    return this._deserializer = t, this;
  }
  build() {
    const t = this;
    let r;
    return r = class extends Cs {
      static getEndpointParameterInstructions() {
        return t._ep;
      }
      constructor(...[s]) {
        super(), this.serialize = t._serializer, this.deserialize = t._deserializer, this.input = s ?? {}, t._init(this);
      }
      resolveMiddleware(s, n, i) {
        return this.resolveMiddlewareWithContext(s, n, i, {
          CommandCtor: r,
          middlewareFn: t._middlewareFn,
          clientName: t._clientName,
          commandName: t._commandName,
          inputFilterSensitiveLog: t._inputFilterSensitiveLog,
          outputFilterSensitiveLog: t._outputFilterSensitiveLog,
          smithyContext: t._smithyContext,
          additionalContext: t._additionalContext
        });
      }
    };
  }
};
var Ne = "***SensitiveInformation***";
var To = (e) => {
  switch (e) {
    case "true":
      return true;
    case "false":
      return false;
    default:
      throw new Error(`Unable to parse boolean value "${e}"`);
  }
};
var Uu = (e) => {
  if (e != null) {
    if (typeof e == "string") {
      const t = parseFloat(e);
      if (!Number.isNaN(t))
        return String(t) !== String(e) && Po.warn(Io(`Expected number but observed string: ${e}`)), t;
    }
    if (typeof e == "number")
      return e;
    throw new TypeError(`Expected number, got ${typeof e}: ${e}`);
  }
};
var $u = Math.ceil(2 ** 127 * (2 - 2 ** -23));
var An = (e) => {
  const t = Uu(e);
  if (t !== void 0 && !Number.isNaN(t) && t !== 1 / 0 && t !== -1 / 0 && Math.abs(t) > $u)
    throw new TypeError(`Expected 32-bit float, got ${e}`);
  return t;
};
var cs = (e) => {
  if (e != null) {
    if (Number.isInteger(e) && !Number.isNaN(e))
      return e;
    throw new TypeError(`Expected integer, got ${typeof e}: ${e}`);
  }
};
var Cn = (e) => Bo(e, 16);
var vn = (e) => Bo(e, 8);
var Bo = (e, t) => {
  const r = cs(e);
  if (r !== void 0 && Lu(r, t) !== r)
    throw new TypeError(`Expected ${t}-bit integer, got ${e}`);
  return r;
};
var Lu = (e, t) => {
  switch (t) {
    case 32:
      return Int32Array.of(e)[0];
    case 16:
      return Int16Array.of(e)[0];
    case 8:
      return Int8Array.of(e)[0];
  }
};
var No = (e, t) => {
  if (e == null)
    throw t ? new TypeError(`Expected a non-null value for ${t}`) : new TypeError("Expected a non-null value");
  return e;
};
var Hu = (e) => {
  if (e == null)
    return;
  if (typeof e == "object" && !Array.isArray(e))
    return e;
  const t = Array.isArray(e) ? "array" : typeof e;
  throw new TypeError(`Expected object, got ${t}: ${e}`);
};
var Rt = (e) => {
  if (e != null) {
    if (typeof e == "string")
      return e;
    if (["boolean", "number", "bigint"].includes(typeof e))
      return Po.warn(Io(`Expected string, got ${typeof e}: ${e}`)), String(e);
    throw new TypeError(`Expected string, got ${typeof e}: ${e}`);
  }
};
var zu = (e) => An(typeof e == "string" ? or(e) : e);
var qu = /(-?(?:0|[1-9]\d*)(?:\.\d+)?(?:[eE][+-]?\d+)?)|(-?Infinity)|(NaN)/g;
var or = (e) => {
  const t = e.match(qu);
  if (t === null || t[0].length !== e.length)
    throw new TypeError("Expected real number, got implicit NaN");
  return parseFloat(e);
};
var Vu = (e) => cs(typeof e == "string" ? or(e) : e);
var ju = (e) => Cn(typeof e == "string" ? or(e) : e);
var Wu = (e) => vn(typeof e == "string" ? or(e) : e);
var Io = (e) => String(new TypeError(e).stack || e).split(`
`).slice(0, 5).filter((t) => !t.includes("stackTraceWarning")).join(`
`);
var Po = {
  warn: console.warn
};
var Gu = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
var _o = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
function Ku(e) {
  const t = e.getUTCFullYear(), r = e.getUTCMonth(), s = e.getUTCDay(), n = e.getUTCDate(), i = e.getUTCHours(), o = e.getUTCMinutes(), a = e.getUTCSeconds(), c = n < 10 ? `0${n}` : `${n}`, u = i < 10 ? `0${i}` : `${i}`, l = o < 10 ? `0${o}` : `${o}`, f = a < 10 ? `0${a}` : `${a}`;
  return `${Gu[s]}, ${c} ${_o[r]} ${t} ${u}:${l}:${f} GMT`;
}
var Xu = new RegExp(/^(\d{4})-(\d{2})-(\d{2})[tT](\d{2}):(\d{2}):(\d{2})(?:\.(\d+))?(([-+]\d{2}\:\d{2})|[zZ])$/);
var Zu = (e) => {
  if (e == null)
    return;
  if (typeof e != "string")
    throw new TypeError("RFC-3339 date-times must be expressed as strings");
  const t = Xu.exec(e);
  if (!t)
    throw new TypeError("Invalid RFC-3339 date-time value");
  const [r, s, n, i, o, a, c, u, l] = t, f = ju(Mo(s)), p = kt(n, "month", 1, 12), g = kt(i, "day", 1, 31), E = Qu(f, p, g, { hours: o, minutes: a, seconds: c, fractionalMilliseconds: u });
  return l.toUpperCase() != "Z" && E.setTime(E.getTime() - rd(l)), E;
};
var Qu = (e, t, r, s) => {
  const n = t - 1;
  return Ju(e, n, r), new Date(Date.UTC(e, n, r, kt(s.hours, "hour", 0, 23), kt(s.minutes, "minute", 0, 59), kt(s.seconds, "seconds", 0, 60), td(s.fractionalMilliseconds)));
};
var Yu = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
var Ju = (e, t, r) => {
  let s = Yu[t];
  if (t === 1 && ed(e) && (s = 29), r > s)
    throw new TypeError(`Invalid day for ${_o[t]} in ${e}: ${r}`);
};
var ed = (e) => e % 4 === 0 && (e % 100 !== 0 || e % 400 === 0);
var kt = (e, t, r, s) => {
  const n = Wu(Mo(e));
  if (n < r || n > s)
    throw new TypeError(`${t} must be between ${r} and ${s}, inclusive`);
  return n;
};
var td = (e) => e == null ? 0 : zu("0." + e) * 1e3;
var rd = (e) => {
  const t = e[0];
  let r = 1;
  if (t == "+")
    r = 1;
  else if (t == "-")
    r = -1;
  else
    throw new TypeError(`Offset direction, ${t}, must be "+" or "-"`);
  const s = Number(e.substring(1, 3)), n = Number(e.substring(4, 6));
  return r * (s * 60 + n) * 60 * 1e3;
};
var Mo = (e) => {
  let t = 0;
  for (; t < e.length - 1 && e.charAt(t) === "0"; )
    t++;
  return t === 0 ? e : e.slice(t);
};
var ot = class _ot extends Error {
  constructor(t) {
    super(t.message), Object.setPrototypeOf(this, Object.getPrototypeOf(this).constructor.prototype), this.name = t.name, this.$fault = t.$fault, this.$metadata = t.$metadata;
  }
  static isInstance(t) {
    if (!t)
      return false;
    const r = t;
    return _ot.prototype.isPrototypeOf(r) || !!r.$fault && !!r.$metadata && (r.$fault === "client" || r.$fault === "server");
  }
  static [Symbol.hasInstance](t) {
    if (!t)
      return false;
    const r = t;
    return this === _ot ? _ot.isInstance(t) : _ot.isInstance(t) ? r.name && this.name ? this.prototype.isPrototypeOf(t) || r.name === this.name : this.prototype.isPrototypeOf(t) : false;
  }
};
var Se = (e, t = {}) => {
  Object.entries(t).filter(([, s]) => s !== void 0).forEach(([s, n]) => {
    (e[s] == null || e[s] === "") && (e[s] = n);
  });
  const r = e.message || e.Message || "UnknownError";
  return e.message = r, delete e.Message, e;
};
var sd = ({ output: e, parsedBody: t, exceptionCtor: r, errorCode: s }) => {
  const n = id(e), i = n.httpStatusCode ? n.httpStatusCode + "" : void 0, o = new r({
    name: (t == null ? void 0 : t.code) || (t == null ? void 0 : t.Code) || s || i || "UnknownError",
    $fault: "client",
    $metadata: n
  });
  throw Se(o, t);
};
var nd = (e) => ({ output: t, parsedBody: r, errorCode: s }) => {
  sd({ output: t, parsedBody: r, exceptionCtor: e, errorCode: s });
};
var id = (e) => ({
  httpStatusCode: e.statusCode,
  requestId: e.headers["x-amzn-requestid"] ?? e.headers["x-amzn-request-id"] ?? e.headers["x-amz-request-id"],
  extendedRequestId: e.headers["x-amz-id-2"],
  cfId: e.headers["x-amz-cf-id"]
});
var od = (e) => {
  switch (e) {
    case "standard":
      return {
        retryMode: "standard",
        connectionTimeout: 3100
      };
    case "in-region":
      return {
        retryMode: "standard",
        connectionTimeout: 1100
      };
    case "cross-region":
      return {
        retryMode: "standard",
        connectionTimeout: 3100
      };
    case "mobile":
      return {
        retryMode: "standard",
        connectionTimeout: 3e4
      };
    default:
      return {};
  }
};
var ad = (e) => {
  const t = [];
  for (const r in Kt) {
    const s = Kt[r];
    e[s] !== void 0 && t.push({
      algorithmId: () => s,
      checksumConstructor: () => e[s]
    });
  }
  return {
    _checksumAlgorithms: t,
    addChecksumAlgorithm(r) {
      this._checksumAlgorithms.push(r);
    },
    checksumAlgorithms() {
      return this._checksumAlgorithms;
    }
  };
};
var cd = (e) => {
  const t = {};
  return e.checksumAlgorithms().forEach((r) => {
    t[r.algorithmId()] = r.checksumConstructor();
  }), t;
};
var ud = (e) => {
  let t = e.retryStrategy;
  return {
    setRetryStrategy(r) {
      t = r;
    },
    retryStrategy() {
      return t;
    }
  };
};
var dd = (e) => {
  const t = {};
  return t.retryStrategy = e.retryStrategy(), t;
};
var ld = (e) => ({
  ...ad(e),
  ...ud(e)
});
var fd = (e) => ({
  ...cd(e),
  ...dd(e)
});
var Oo = (e) => {
  const t = "#text";
  for (const r in e)
    e.hasOwnProperty(r) && e[r][t] !== void 0 ? e[r] = e[r][t] : typeof e[r] == "object" && e[r] !== null && (e[r] = Oo(e[r]));
  return e;
};
var ze = (e) => e != null;
var vs = class {
  trace() {
  }
  debug() {
  }
  info() {
  }
  warn() {
  }
  error() {
  }
};
function ee(e, t, r) {
  let s, n, i;
  if (typeof t > "u" && typeof r > "u")
    s = {}, i = e;
  else {
    if (s = e, typeof t == "function")
      return n = t, i = r, hd(s, n, i);
    i = t;
  }
  for (const o of Object.keys(i)) {
    if (!Array.isArray(i[o])) {
      s[o] = i[o];
      continue;
    }
    pd(s, null, i, o);
  }
  return s;
}
var hd = (e, t, r) => ee(e, Object.entries(r).reduce((s, [n, i]) => (Array.isArray(i) ? s[n] = i : typeof i == "function" ? s[n] = [t, i()] : s[n] = [t, i], s), {}));
var pd = (e, t, r, s) => {
  if (t !== null) {
    let o = r[s];
    typeof o == "function" && (o = [, o]);
    const [a = gd, c = md, u = s] = o;
    (typeof a == "function" && a(t[u]) || typeof a != "function" && a) && (e[s] = c(t[u]));
    return;
  }
  let [n, i] = r[s];
  if (typeof i == "function") {
    let o;
    const a = n === void 0 && (o = i()) != null, c = typeof n == "function" && !!n(void 0) || typeof n != "function" && !!n;
    a ? e[s] = o : c && (e[s] = i());
  } else {
    const o = n === void 0 && i != null, a = typeof n == "function" && !!n(i) || typeof n != "function" && !!n;
    (o || a) && (e[s] = i);
  }
};
var gd = (e) => e != null;
var md = (e) => e;
var yd = (e) => e.toISOString().replace(".000Z", "Z");
var wd = (e, t) => yo(e, t).then((r) => t.utf8Encoder(r));
var Rs = {};
var ar = {};
(function(e) {
  const t = ":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD", r = t + "\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040", s = "[" + t + "][" + r + "]*", n = new RegExp("^" + s + "$"), i = function(a, c) {
    const u = [];
    let l = c.exec(a);
    for (; l; ) {
      const f = [];
      f.startIndex = c.lastIndex - l[0].length;
      const p = l.length;
      for (let g = 0; g < p; g++)
        f.push(l[g]);
      u.push(f), l = c.exec(a);
    }
    return u;
  }, o = function(a) {
    const c = n.exec(a);
    return !(c === null || typeof c > "u");
  };
  e.isExist = function(a) {
    return typeof a < "u";
  }, e.isEmptyObject = function(a) {
    return Object.keys(a).length === 0;
  }, e.merge = function(a, c, u) {
    if (c) {
      const l = Object.keys(c), f = l.length;
      for (let p = 0; p < f; p++)
        u === "strict" ? a[l[p]] = [c[l[p]]] : a[l[p]] = c[l[p]];
    }
  }, e.getValue = function(a) {
    return e.isExist(a) ? a : "";
  }, e.isName = o, e.getAllMatches = i, e.nameRegexp = s;
})(ar);
var ks = ar;
var bd = {
  allowBooleanAttributes: false,
  //A tag can have attributes without any value
  unpairedTags: []
};
Rs.validate = function(e, t) {
  t = Object.assign({}, bd, t);
  const r = [];
  let s = false, n = false;
  e[0] === "\uFEFF" && (e = e.substr(1));
  for (let i = 0; i < e.length; i++)
    if (e[i] === "<" && e[i + 1] === "?") {
      if (i += 2, i = kn(e, i), i.err)
        return i;
    } else if (e[i] === "<") {
      let o = i;
      if (i++, e[i] === "!") {
        i = Tn(e, i);
        continue;
      } else {
        let a = false;
        e[i] === "/" && (a = true, i++);
        let c = "";
        for (; i < e.length && e[i] !== ">" && e[i] !== " " && e[i] !== "	" && e[i] !== `
` && e[i] !== "\r"; i++)
          c += e[i];
        if (c = c.trim(), c[c.length - 1] === "/" && (c = c.substring(0, c.length - 1), i--), !kd(c)) {
          let f;
          return c.trim().length === 0 ? f = "Invalid space after '<'." : f = "Tag '" + c + "' is an invalid name.", W("InvalidTag", f, ge(e, i));
        }
        const u = Sd(e, i);
        if (u === false)
          return W("InvalidAttr", "Attributes for '" + c + "' have open quote.", ge(e, i));
        let l = u.value;
        if (i = u.index, l[l.length - 1] === "/") {
          const f = i - l.length;
          l = l.substring(0, l.length - 1);
          const p = Bn(l, t);
          if (p === true)
            s = true;
          else
            return W(p.err.code, p.err.msg, ge(e, f + p.err.line));
        } else if (a)
          if (u.tagClosed) {
            if (l.trim().length > 0)
              return W("InvalidTag", "Closing tag '" + c + "' can't have attributes or invalid starting.", ge(e, o));
            if (r.length === 0)
              return W("InvalidTag", "Closing tag '" + c + "' has not been opened.", ge(e, o));
            {
              const f = r.pop();
              if (c !== f.tagName) {
                let p = ge(e, f.tagStartPos);
                return W(
                  "InvalidTag",
                  "Expected closing tag '" + f.tagName + "' (opened in line " + p.line + ", col " + p.col + ") instead of closing tag '" + c + "'.",
                  ge(e, o)
                );
              }
              r.length == 0 && (n = true);
            }
          } else
            return W("InvalidTag", "Closing tag '" + c + "' doesn't have proper closing.", ge(e, i));
        else {
          const f = Bn(l, t);
          if (f !== true)
            return W(f.err.code, f.err.msg, ge(e, i - l.length + f.err.line));
          if (n === true)
            return W("InvalidXml", "Multiple possible root nodes found.", ge(e, i));
          t.unpairedTags.indexOf(c) !== -1 || r.push({ tagName: c, tagStartPos: o }), s = true;
        }
        for (i++; i < e.length; i++)
          if (e[i] === "<")
            if (e[i + 1] === "!") {
              i++, i = Tn(e, i);
              continue;
            } else if (e[i + 1] === "?") {
              if (i = kn(e, ++i), i.err)
                return i;
            } else
              break;
          else if (e[i] === "&") {
            const f = vd(e, i);
            if (f == -1)
              return W("InvalidChar", "char '&' is not expected.", ge(e, i));
            i = f;
          } else if (n === true && !Rn(e[i]))
            return W("InvalidXml", "Extra text at the end", ge(e, i));
        e[i] === "<" && i--;
      }
    } else {
      if (Rn(e[i]))
        continue;
      return W("InvalidChar", "char '" + e[i] + "' is not expected.", ge(e, i));
    }
  if (s) {
    if (r.length == 1)
      return W("InvalidTag", "Unclosed tag '" + r[0].tagName + "'.", ge(e, r[0].tagStartPos));
    if (r.length > 0)
      return W("InvalidXml", "Invalid '" + JSON.stringify(r.map((i) => i.tagName), null, 4).replace(/\r?\n/g, "") + "' found.", { line: 1, col: 1 });
  } else
    return W("InvalidXml", "Start tag expected.", 1);
  return true;
};
function Rn(e) {
  return e === " " || e === "	" || e === `
` || e === "\r";
}
function kn(e, t) {
  const r = t;
  for (; t < e.length; t++)
    if (e[t] == "?" || e[t] == " ") {
      const s = e.substr(r, t - r);
      if (t > 5 && s === "xml")
        return W("InvalidXml", "XML declaration allowed only at the start of the document.", ge(e, t));
      if (e[t] == "?" && e[t + 1] == ">") {
        t++;
        break;
      } else
        continue;
    }
  return t;
}
function Tn(e, t) {
  if (e.length > t + 5 && e[t + 1] === "-" && e[t + 2] === "-") {
    for (t += 3; t < e.length; t++)
      if (e[t] === "-" && e[t + 1] === "-" && e[t + 2] === ">") {
        t += 2;
        break;
      }
  } else if (e.length > t + 8 && e[t + 1] === "D" && e[t + 2] === "O" && e[t + 3] === "C" && e[t + 4] === "T" && e[t + 5] === "Y" && e[t + 6] === "P" && e[t + 7] === "E") {
    let r = 1;
    for (t += 8; t < e.length; t++)
      if (e[t] === "<")
        r++;
      else if (e[t] === ">" && (r--, r === 0))
        break;
  } else if (e.length > t + 9 && e[t + 1] === "[" && e[t + 2] === "C" && e[t + 3] === "D" && e[t + 4] === "A" && e[t + 5] === "T" && e[t + 6] === "A" && e[t + 7] === "[") {
    for (t += 8; t < e.length; t++)
      if (e[t] === "]" && e[t + 1] === "]" && e[t + 2] === ">") {
        t += 2;
        break;
      }
  }
  return t;
}
var Ed = '"';
var xd = "'";
function Sd(e, t) {
  let r = "", s = "", n = false;
  for (; t < e.length; t++) {
    if (e[t] === Ed || e[t] === xd)
      s === "" ? s = e[t] : s !== e[t] || (s = "");
    else if (e[t] === ">" && s === "") {
      n = true;
      break;
    }
    r += e[t];
  }
  return s !== "" ? false : {
    value: r,
    index: t,
    tagClosed: n
  };
}
var Ad = new RegExp(`(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['"])(([\\s\\S])*?)\\5)?`, "g");
function Bn(e, t) {
  const r = ks.getAllMatches(e, Ad), s = {};
  for (let n = 0; n < r.length; n++) {
    if (r[n][1].length === 0)
      return W("InvalidAttr", "Attribute '" + r[n][2] + "' has no space in starting.", yt(r[n]));
    if (r[n][3] !== void 0 && r[n][4] === void 0)
      return W("InvalidAttr", "Attribute '" + r[n][2] + "' is without value.", yt(r[n]));
    if (r[n][3] === void 0 && !t.allowBooleanAttributes)
      return W("InvalidAttr", "boolean attribute '" + r[n][2] + "' is not allowed.", yt(r[n]));
    const i = r[n][2];
    if (!Rd(i))
      return W("InvalidAttr", "Attribute '" + i + "' is an invalid name.", yt(r[n]));
    if (!s.hasOwnProperty(i))
      s[i] = 1;
    else
      return W("InvalidAttr", "Attribute '" + i + "' is repeated.", yt(r[n]));
  }
  return true;
}
function Cd(e, t) {
  let r = /\d/;
  for (e[t] === "x" && (t++, r = /[\da-fA-F]/); t < e.length; t++) {
    if (e[t] === ";")
      return t;
    if (!e[t].match(r))
      break;
  }
  return -1;
}
function vd(e, t) {
  if (t++, e[t] === ";")
    return -1;
  if (e[t] === "#")
    return t++, Cd(e, t);
  let r = 0;
  for (; t < e.length; t++, r++)
    if (!(e[t].match(/\w/) && r < 20)) {
      if (e[t] === ";")
        break;
      return -1;
    }
  return t;
}
function W(e, t, r) {
  return {
    err: {
      code: e,
      msg: t,
      line: r.line || r,
      col: r.col
    }
  };
}
function Rd(e) {
  return ks.isName(e);
}
function kd(e) {
  return ks.isName(e);
}
function ge(e, t) {
  const r = e.substring(0, t).split(/\r?\n/);
  return {
    line: r.length,
    // column number is last line's length + 1, because column numbering starts at 1:
    col: r[r.length - 1].length + 1
  };
}
function yt(e) {
  return e.startIndex + e[1].length;
}
var Ts = {};
var Fo = {
  preserveOrder: false,
  attributeNamePrefix: "@_",
  attributesGroupName: false,
  textNodeName: "#text",
  ignoreAttributes: true,
  removeNSPrefix: false,
  // remove NS from tag name or attribute name if true
  allowBooleanAttributes: false,
  //a tag can have attributes without any value
  //ignoreRootElement : false,
  parseTagValue: true,
  parseAttributeValue: false,
  trimValues: true,
  //Trim string values of tag and attributes
  cdataPropName: false,
  numberParseOptions: {
    hex: true,
    leadingZeros: true,
    eNotation: true
  },
  tagValueProcessor: function(e, t) {
    return t;
  },
  attributeValueProcessor: function(e, t) {
    return t;
  },
  stopNodes: [],
  //nested tags will not be parsed even for errors
  alwaysCreateTextNode: false,
  isArray: () => false,
  commentPropName: false,
  unpairedTags: [],
  processEntities: true,
  htmlEntities: false,
  ignoreDeclaration: false,
  ignorePiTags: false,
  transformTagName: false,
  transformAttributeName: false,
  updateTag: function(e, t, r) {
    return e;
  }
  // skipEmptyListItem: false
};
var Td = function(e) {
  return Object.assign({}, Fo, e);
};
Ts.buildOptions = Td;
Ts.defaultOptions = Fo;
var Bd = class {
  constructor(t) {
    this.tagname = t, this.child = [], this[":@"] = {};
  }
  add(t, r) {
    t === "__proto__" && (t = "#__proto__"), this.child.push({ [t]: r });
  }
  addChild(t) {
    t.tagname === "__proto__" && (t.tagname = "#__proto__"), t[":@"] && Object.keys(t[":@"]).length > 0 ? this.child.push({ [t.tagname]: t.child, ":@": t[":@"] }) : this.child.push({ [t.tagname]: t.child });
  }
};
var Nd = Bd;
var Id = ar;
function Pd(e, t) {
  const r = {};
  if (e[t + 3] === "O" && e[t + 4] === "C" && e[t + 5] === "T" && e[t + 6] === "Y" && e[t + 7] === "P" && e[t + 8] === "E") {
    t = t + 9;
    let s = 1, n = false, i = false, o = "";
    for (; t < e.length; t++)
      if (e[t] === "<" && !i) {
        if (n && Od(e, t))
          t += 7, [entityName, val, t] = _d(e, t + 1), val.indexOf("&") === -1 && (r[$d(entityName)] = {
            regx: RegExp(`&${entityName};`, "g"),
            val
          });
        else if (n && Fd(e, t))
          t += 8;
        else if (n && Dd(e, t))
          t += 8;
        else if (n && Ud(e, t))
          t += 9;
        else if (Md)
          i = true;
        else
          throw new Error("Invalid DOCTYPE");
        s++, o = "";
      } else if (e[t] === ">") {
        if (i ? e[t - 1] === "-" && e[t - 2] === "-" && (i = false, s--) : s--, s === 0)
          break;
      } else
        e[t] === "[" ? n = true : o += e[t];
    if (s !== 0)
      throw new Error("Unclosed DOCTYPE");
  } else
    throw new Error("Invalid Tag instead of DOCTYPE");
  return { entities: r, i: t };
}
function _d(e, t) {
  let r = "";
  for (; t < e.length && e[t] !== "'" && e[t] !== '"'; t++)
    r += e[t];
  if (r = r.trim(), r.indexOf(" ") !== -1)
    throw new Error("External entites are not supported");
  const s = e[t++];
  let n = "";
  for (; t < e.length && e[t] !== s; t++)
    n += e[t];
  return [r, n, t];
}
function Md(e, t) {
  return e[t + 1] === "!" && e[t + 2] === "-" && e[t + 3] === "-";
}
function Od(e, t) {
  return e[t + 1] === "!" && e[t + 2] === "E" && e[t + 3] === "N" && e[t + 4] === "T" && e[t + 5] === "I" && e[t + 6] === "T" && e[t + 7] === "Y";
}
function Fd(e, t) {
  return e[t + 1] === "!" && e[t + 2] === "E" && e[t + 3] === "L" && e[t + 4] === "E" && e[t + 5] === "M" && e[t + 6] === "E" && e[t + 7] === "N" && e[t + 8] === "T";
}
function Dd(e, t) {
  return e[t + 1] === "!" && e[t + 2] === "A" && e[t + 3] === "T" && e[t + 4] === "T" && e[t + 5] === "L" && e[t + 6] === "I" && e[t + 7] === "S" && e[t + 8] === "T";
}
function Ud(e, t) {
  return e[t + 1] === "!" && e[t + 2] === "N" && e[t + 3] === "O" && e[t + 4] === "T" && e[t + 5] === "A" && e[t + 6] === "T" && e[t + 7] === "I" && e[t + 8] === "O" && e[t + 9] === "N";
}
function $d(e) {
  if (Id.isName(e))
    return e;
  throw new Error(`Invalid entity name ${e}`);
}
var Ld = Pd;
var Hd = /^[-+]?0x[a-fA-F0-9]+$/;
var zd = /^([\-\+])?(0*)([0-9]*(\.[0-9]*)?)$/;
var qd = {
  hex: true,
  // oct: false,
  leadingZeros: true,
  decimalPoint: ".",
  eNotation: true
  //skipLike: /regex/
};
function Vd(e, t = {}) {
  if (t = Object.assign({}, qd, t), !e || typeof e != "string")
    return e;
  let r = e.trim();
  if (t.skipLike !== void 0 && t.skipLike.test(r))
    return e;
  if (e === "0")
    return 0;
  if (t.hex && Hd.test(r))
    return Wd(r, 16);
  if (r.search(/[eE]/) !== -1) {
    const s = r.match(/^([-\+])?(0*)([0-9]*(\.[0-9]*)?[eE][-\+]?[0-9]+)$/);
    if (s) {
      if (t.leadingZeros)
        r = (s[1] || "") + s[3];
      else if (!(s[2] === "0" && s[3][0] === "."))
        return e;
      return t.eNotation ? Number(r) : e;
    } else
      return e;
  } else {
    const s = zd.exec(r);
    if (s) {
      const n = s[1], i = s[2];
      let o = jd(s[3]);
      if (!t.leadingZeros && i.length > 0 && n && r[2] !== ".")
        return e;
      if (!t.leadingZeros && i.length > 0 && !n && r[1] !== ".")
        return e;
      if (t.leadingZeros && i === e)
        return 0;
      {
        const a = Number(r), c = "" + a;
        return c.search(/[eE]/) !== -1 ? t.eNotation ? a : e : r.indexOf(".") !== -1 ? c === "0" && o === "" || c === o || n && c === "-" + o ? a : e : i ? o === c || n + o === c ? a : e : r === c || r === n + c ? a : e;
      }
    } else
      return e;
  }
}
function jd(e) {
  return e && e.indexOf(".") !== -1 && (e = e.replace(/0+$/, ""), e === "." ? e = "0" : e[0] === "." ? e = "0" + e : e[e.length - 1] === "." && (e = e.substr(0, e.length - 1))), e;
}
function Wd(e, t) {
  if (parseInt)
    return parseInt(e, t);
  if (Number.parseInt)
    return Number.parseInt(e, t);
  if (window && window.parseInt)
    return window.parseInt(e, t);
  throw new Error("parseInt, Number.parseInt, window.parseInt are not supported");
}
var Gd = Vd;
var Do = ar;
var wt = Nd;
var Kd = Ld;
var Xd = Gd;
var Zd = class {
  constructor(t) {
    this.options = t, this.currentNode = null, this.tagsNodeStack = [], this.docTypeEntities = {}, this.lastEntities = {
      apos: { regex: /&(apos|#39|#x27);/g, val: "'" },
      gt: { regex: /&(gt|#62|#x3E);/g, val: ">" },
      lt: { regex: /&(lt|#60|#x3C);/g, val: "<" },
      quot: { regex: /&(quot|#34|#x22);/g, val: '"' }
    }, this.ampEntity = { regex: /&(amp|#38|#x26);/g, val: "&" }, this.htmlEntities = {
      space: { regex: /&(nbsp|#160);/g, val: " " },
      // "lt" : { regex: /&(lt|#60);/g, val: "<" },
      // "gt" : { regex: /&(gt|#62);/g, val: ">" },
      // "amp" : { regex: /&(amp|#38);/g, val: "&" },
      // "quot" : { regex: /&(quot|#34);/g, val: "\"" },
      // "apos" : { regex: /&(apos|#39);/g, val: "'" },
      cent: { regex: /&(cent|#162);/g, val: "¢" },
      pound: { regex: /&(pound|#163);/g, val: "£" },
      yen: { regex: /&(yen|#165);/g, val: "¥" },
      euro: { regex: /&(euro|#8364);/g, val: "€" },
      copyright: { regex: /&(copy|#169);/g, val: "©" },
      reg: { regex: /&(reg|#174);/g, val: "®" },
      inr: { regex: /&(inr|#8377);/g, val: "₹" },
      num_dec: { regex: /&#([0-9]{1,7});/g, val: (r, s) => String.fromCharCode(Number.parseInt(s, 10)) },
      num_hex: { regex: /&#x([0-9a-fA-F]{1,6});/g, val: (r, s) => String.fromCharCode(Number.parseInt(s, 16)) }
    }, this.addExternalEntities = Qd, this.parseXml = rl, this.parseTextData = Yd, this.resolveNameSpace = Jd, this.buildAttributesMap = tl, this.isItStopNode = ol, this.replaceEntitiesValue = nl, this.readStopNodeData = cl, this.saveTextToParentTag = il, this.addChild = sl;
  }
};
function Qd(e) {
  const t = Object.keys(e);
  for (let r = 0; r < t.length; r++) {
    const s = t[r];
    this.lastEntities[s] = {
      regex: new RegExp("&" + s + ";", "g"),
      val: e[s]
    };
  }
}
function Yd(e, t, r, s, n, i, o) {
  if (e !== void 0 && (this.options.trimValues && !s && (e = e.trim()), e.length > 0)) {
    o || (e = this.replaceEntitiesValue(e));
    const a = this.options.tagValueProcessor(t, e, r, n, i);
    return a == null ? e : typeof a != typeof e || a !== e ? a : this.options.trimValues ? ds(e, this.options.parseTagValue, this.options.numberParseOptions) : e.trim() === e ? ds(e, this.options.parseTagValue, this.options.numberParseOptions) : e;
  }
}
function Jd(e) {
  if (this.options.removeNSPrefix) {
    const t = e.split(":"), r = e.charAt(0) === "/" ? "/" : "";
    if (t[0] === "xmlns")
      return "";
    t.length === 2 && (e = r + t[1]);
  }
  return e;
}
var el = new RegExp(`([^\\s=]+)\\s*(=\\s*(['"])([\\s\\S]*?)\\3)?`, "gm");
function tl(e, t, r) {
  if (!this.options.ignoreAttributes && typeof e == "string") {
    const s = Do.getAllMatches(e, el), n = s.length, i = {};
    for (let o = 0; o < n; o++) {
      const a = this.resolveNameSpace(s[o][1]);
      let c = s[o][4], u = this.options.attributeNamePrefix + a;
      if (a.length)
        if (this.options.transformAttributeName && (u = this.options.transformAttributeName(u)), u === "__proto__" && (u = "#__proto__"), c !== void 0) {
          this.options.trimValues && (c = c.trim()), c = this.replaceEntitiesValue(c);
          const l = this.options.attributeValueProcessor(a, c, t);
          l == null ? i[u] = c : typeof l != typeof c || l !== c ? i[u] = l : i[u] = ds(
            c,
            this.options.parseAttributeValue,
            this.options.numberParseOptions
          );
        } else
          this.options.allowBooleanAttributes && (i[u] = true);
    }
    if (!Object.keys(i).length)
      return;
    if (this.options.attributesGroupName) {
      const o = {};
      return o[this.options.attributesGroupName] = i, o;
    }
    return i;
  }
}
var rl = function(e) {
  e = e.replace(/\r\n?/g, `
`);
  const t = new wt("!xml");
  let r = t, s = "", n = "";
  for (let i = 0; i < e.length; i++)
    if (e[i] === "<")
      if (e[i + 1] === "/") {
        const a = Qe(e, ">", i, "Closing Tag is not closed.");
        let c = e.substring(i + 2, a).trim();
        if (this.options.removeNSPrefix) {
          const f = c.indexOf(":");
          f !== -1 && (c = c.substr(f + 1));
        }
        this.options.transformTagName && (c = this.options.transformTagName(c)), r && (s = this.saveTextToParentTag(s, r, n));
        const u = n.substring(n.lastIndexOf(".") + 1);
        if (c && this.options.unpairedTags.indexOf(c) !== -1)
          throw new Error(`Unpaired tag can not be used as closing tag: </${c}>`);
        let l = 0;
        u && this.options.unpairedTags.indexOf(u) !== -1 ? (l = n.lastIndexOf(".", n.lastIndexOf(".") - 1), this.tagsNodeStack.pop()) : l = n.lastIndexOf("."), n = n.substring(0, l), r = this.tagsNodeStack.pop(), s = "", i = a;
      } else if (e[i + 1] === "?") {
        let a = us(e, i, false, "?>");
        if (!a)
          throw new Error("Pi Tag is not closed.");
        if (s = this.saveTextToParentTag(s, r, n), !(this.options.ignoreDeclaration && a.tagName === "?xml" || this.options.ignorePiTags)) {
          const c = new wt(a.tagName);
          c.add(this.options.textNodeName, ""), a.tagName !== a.tagExp && a.attrExpPresent && (c[":@"] = this.buildAttributesMap(a.tagExp, n, a.tagName)), this.addChild(r, c, n);
        }
        i = a.closeIndex + 1;
      } else if (e.substr(i + 1, 3) === "!--") {
        const a = Qe(e, "-->", i + 4, "Comment is not closed.");
        if (this.options.commentPropName) {
          const c = e.substring(i + 4, a - 2);
          s = this.saveTextToParentTag(s, r, n), r.add(this.options.commentPropName, [{ [this.options.textNodeName]: c }]);
        }
        i = a;
      } else if (e.substr(i + 1, 2) === "!D") {
        const a = Kd(e, i);
        this.docTypeEntities = a.entities, i = a.i;
      } else if (e.substr(i + 1, 2) === "![") {
        const a = Qe(e, "]]>", i, "CDATA is not closed.") - 2, c = e.substring(i + 9, a);
        s = this.saveTextToParentTag(s, r, n);
        let u = this.parseTextData(c, r.tagname, n, true, false, true, true);
        u == null && (u = ""), this.options.cdataPropName ? r.add(this.options.cdataPropName, [{ [this.options.textNodeName]: c }]) : r.add(this.options.textNodeName, u), i = a + 2;
      } else {
        let a = us(e, i, this.options.removeNSPrefix), c = a.tagName;
        const u = a.rawTagName;
        let l = a.tagExp, f = a.attrExpPresent, p = a.closeIndex;
        this.options.transformTagName && (c = this.options.transformTagName(c)), r && s && r.tagname !== "!xml" && (s = this.saveTextToParentTag(s, r, n, false));
        const g = r;
        if (g && this.options.unpairedTags.indexOf(g.tagname) !== -1 && (r = this.tagsNodeStack.pop(), n = n.substring(0, n.lastIndexOf("."))), c !== t.tagname && (n += n ? "." + c : c), this.isItStopNode(this.options.stopNodes, n, c)) {
          let E = "";
          if (l.length > 0 && l.lastIndexOf("/") === l.length - 1)
            c[c.length - 1] === "/" ? (c = c.substr(0, c.length - 1), n = n.substr(0, n.length - 1), l = c) : l = l.substr(0, l.length - 1), i = a.closeIndex;
          else if (this.options.unpairedTags.indexOf(c) !== -1)
            i = a.closeIndex;
          else {
            const S = this.readStopNodeData(e, u, p + 1);
            if (!S)
              throw new Error(`Unexpected end of ${u}`);
            i = S.i, E = S.tagContent;
          }
          const k = new wt(c);
          c !== l && f && (k[":@"] = this.buildAttributesMap(l, n, c)), E && (E = this.parseTextData(E, c, n, true, f, true, true)), n = n.substr(0, n.lastIndexOf(".")), k.add(this.options.textNodeName, E), this.addChild(r, k, n);
        } else {
          if (l.length > 0 && l.lastIndexOf("/") === l.length - 1) {
            c[c.length - 1] === "/" ? (c = c.substr(0, c.length - 1), n = n.substr(0, n.length - 1), l = c) : l = l.substr(0, l.length - 1), this.options.transformTagName && (c = this.options.transformTagName(c));
            const E = new wt(c);
            c !== l && f && (E[":@"] = this.buildAttributesMap(l, n, c)), this.addChild(r, E, n), n = n.substr(0, n.lastIndexOf("."));
          } else {
            const E = new wt(c);
            this.tagsNodeStack.push(r), c !== l && f && (E[":@"] = this.buildAttributesMap(l, n, c)), this.addChild(r, E, n), r = E;
          }
          s = "", i = p;
        }
      }
    else
      s += e[i];
  return t.child;
};
function sl(e, t, r) {
  const s = this.options.updateTag(t.tagname, r, t[":@"]);
  s === false || (typeof s == "string" && (t.tagname = s), e.addChild(t));
}
var nl = function(e) {
  if (this.options.processEntities) {
    for (let t in this.docTypeEntities) {
      const r = this.docTypeEntities[t];
      e = e.replace(r.regx, r.val);
    }
    for (let t in this.lastEntities) {
      const r = this.lastEntities[t];
      e = e.replace(r.regex, r.val);
    }
    if (this.options.htmlEntities)
      for (let t in this.htmlEntities) {
        const r = this.htmlEntities[t];
        e = e.replace(r.regex, r.val);
      }
    e = e.replace(this.ampEntity.regex, this.ampEntity.val);
  }
  return e;
};
function il(e, t, r, s) {
  return e && (s === void 0 && (s = Object.keys(t.child).length === 0), e = this.parseTextData(
    e,
    t.tagname,
    r,
    false,
    t[":@"] ? Object.keys(t[":@"]).length !== 0 : false,
    s
  ), e !== void 0 && e !== "" && t.add(this.options.textNodeName, e), e = ""), e;
}
function ol(e, t, r) {
  const s = "*." + r;
  for (const n in e) {
    const i = e[n];
    if (s === i || t === i)
      return true;
  }
  return false;
}
function al(e, t, r = ">") {
  let s, n = "";
  for (let i = t; i < e.length; i++) {
    let o = e[i];
    if (s)
      o === s && (s = "");
    else if (o === '"' || o === "'")
      s = o;
    else if (o === r[0])
      if (r[1]) {
        if (e[i + 1] === r[1])
          return {
            data: n,
            index: i
          };
      } else
        return {
          data: n,
          index: i
        };
    else
      o === "	" && (o = " ");
    n += o;
  }
}
function Qe(e, t, r, s) {
  const n = e.indexOf(t, r);
  if (n === -1)
    throw new Error(s);
  return n + t.length - 1;
}
function us(e, t, r, s = ">") {
  const n = al(e, t + 1, s);
  if (!n)
    return;
  let i = n.data;
  const o = n.index, a = i.search(/\s/);
  let c = i, u = true;
  a !== -1 && (c = i.substring(0, a), i = i.substring(a + 1).trimStart());
  const l = c;
  if (r) {
    const f = c.indexOf(":");
    f !== -1 && (c = c.substr(f + 1), u = c !== n.data.substr(f + 1));
  }
  return {
    tagName: c,
    tagExp: i,
    closeIndex: o,
    attrExpPresent: u,
    rawTagName: l
  };
}
function cl(e, t, r) {
  const s = r;
  let n = 1;
  for (; r < e.length; r++)
    if (e[r] === "<")
      if (e[r + 1] === "/") {
        const i = Qe(e, ">", r, `${t} is not closed`);
        if (e.substring(r + 2, i).trim() === t && (n--, n === 0))
          return {
            tagContent: e.substring(s, r),
            i
          };
        r = i;
      } else if (e[r + 1] === "?")
        r = Qe(e, "?>", r + 1, "StopNode is not closed.");
      else if (e.substr(r + 1, 3) === "!--")
        r = Qe(e, "-->", r + 3, "StopNode is not closed.");
      else if (e.substr(r + 1, 2) === "![")
        r = Qe(e, "]]>", r, "StopNode is not closed.") - 2;
      else {
        const i = us(e, r, ">");
        i && ((i && i.tagName) === t && i.tagExp[i.tagExp.length - 1] !== "/" && n++, r = i.closeIndex);
      }
}
function ds(e, t, r) {
  if (t && typeof e == "string") {
    const s = e.trim();
    return s === "true" ? true : s === "false" ? false : Xd(e, r);
  } else
    return Do.isExist(e) ? e : "";
}
var ul = Zd;
var Uo = {};
function dl(e, t) {
  return $o(e, t);
}
function $o(e, t, r) {
  let s;
  const n = {};
  for (let i = 0; i < e.length; i++) {
    const o = e[i], a = ll(o);
    let c = "";
    if (r === void 0 ? c = a : c = r + "." + a, a === t.textNodeName)
      s === void 0 ? s = o[a] : s += "" + o[a];
    else {
      if (a === void 0)
        continue;
      if (o[a]) {
        let u = $o(o[a], t, c);
        const l = hl(u, t);
        o[":@"] ? fl(u, o[":@"], c, t) : Object.keys(u).length === 1 && u[t.textNodeName] !== void 0 && !t.alwaysCreateTextNode ? u = u[t.textNodeName] : Object.keys(u).length === 0 && (t.alwaysCreateTextNode ? u[t.textNodeName] = "" : u = ""), n[a] !== void 0 && n.hasOwnProperty(a) ? (Array.isArray(n[a]) || (n[a] = [n[a]]), n[a].push(u)) : t.isArray(a, c, l) ? n[a] = [u] : n[a] = u;
      }
    }
  }
  return typeof s == "string" ? s.length > 0 && (n[t.textNodeName] = s) : s !== void 0 && (n[t.textNodeName] = s), n;
}
function ll(e) {
  const t = Object.keys(e);
  for (let r = 0; r < t.length; r++) {
    const s = t[r];
    if (s !== ":@")
      return s;
  }
}
function fl(e, t, r, s) {
  if (t) {
    const n = Object.keys(t), i = n.length;
    for (let o = 0; o < i; o++) {
      const a = n[o];
      s.isArray(a, r + "." + a, true, true) ? e[a] = [t[a]] : e[a] = t[a];
    }
  }
}
function hl(e, t) {
  const { textNodeName: r } = t, s = Object.keys(e).length;
  return !!(s === 0 || s === 1 && (e[r] || typeof e[r] == "boolean" || e[r] === 0));
}
Uo.prettify = dl;
var { buildOptions: pl } = Ts;
var gl = ul;
var { prettify: ml } = Uo;
var yl = Rs;
var wl = class {
  constructor(t) {
    this.externalEntities = {}, this.options = pl(t);
  }
  /**
   * Parse XML dats to JS object 
   * @param {string|Buffer} xmlData 
   * @param {boolean|Object} validationOption 
   */
  parse(t, r) {
    if (typeof t != "string")
      if (t.toString)
        t = t.toString();
      else
        throw new Error("XML data is accepted in String or Bytes[] form.");
    if (r) {
      r === true && (r = {});
      const i = yl.validate(t, r);
      if (i !== true)
        throw Error(`${i.err.msg}:${i.err.line}:${i.err.col}`);
    }
    const s = new gl(this.options);
    s.addExternalEntities(this.externalEntities);
    const n = s.parseXml(t);
    return this.options.preserveOrder || n === void 0 ? n : ml(n, this.options);
  }
  /**
   * Add Entity which is not by default supported by this library
   * @param {string} key 
   * @param {string} value 
   */
  addEntity(t, r) {
    if (r.indexOf("&") !== -1)
      throw new Error("Entity value can't have '&'");
    if (t.indexOf("&") !== -1 || t.indexOf(";") !== -1)
      throw new Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");
    if (r === "&")
      throw new Error("An entity with value '&' is not permitted");
    this.externalEntities[t] = r;
  }
};
var bl = wl;
var El = `
`;
function xl(e, t) {
  let r = "";
  return t.format && t.indentBy.length > 0 && (r = El), Lo(e, t, "", r);
}
function Lo(e, t, r, s) {
  let n = "", i = false;
  for (let o = 0; o < e.length; o++) {
    const a = e[o], c = Sl(a);
    if (c === void 0)
      continue;
    let u = "";
    if (r.length === 0 ? u = c : u = `${r}.${c}`, c === t.textNodeName) {
      let E = a[c];
      Al(u, t) || (E = t.tagValueProcessor(c, E), E = Ho(E, t)), i && (n += s), n += E, i = false;
      continue;
    } else if (c === t.cdataPropName) {
      i && (n += s), n += `<![CDATA[${a[c][0][t.textNodeName]}]]>`, i = false;
      continue;
    } else if (c === t.commentPropName) {
      n += s + `<!--${a[c][0][t.textNodeName]}-->`, i = true;
      continue;
    } else if (c[0] === "?") {
      const E = Nn(a[":@"], t), k = c === "?xml" ? "" : s;
      let S = a[c][0][t.textNodeName];
      S = S.length !== 0 ? " " + S : "", n += k + `<${c}${S}${E}?>`, i = true;
      continue;
    }
    let l = s;
    l !== "" && (l += t.indentBy);
    const f = Nn(a[":@"], t), p = s + `<${c}${f}`, g = Lo(a[c], t, u, l);
    t.unpairedTags.indexOf(c) !== -1 ? t.suppressUnpairedNode ? n += p + ">" : n += p + "/>" : (!g || g.length === 0) && t.suppressEmptyNode ? n += p + "/>" : g && g.endsWith(">") ? n += p + `>${g}${s}</${c}>` : (n += p + ">", g && s !== "" && (g.includes("/>") || g.includes("</")) ? n += s + t.indentBy + g + s : n += g, n += `</${c}>`), i = true;
  }
  return n;
}
function Sl(e) {
  const t = Object.keys(e);
  for (let r = 0; r < t.length; r++) {
    const s = t[r];
    if (e.hasOwnProperty(s) && s !== ":@")
      return s;
  }
}
function Nn(e, t) {
  let r = "";
  if (e && !t.ignoreAttributes)
    for (let s in e) {
      if (!e.hasOwnProperty(s))
        continue;
      let n = t.attributeValueProcessor(s, e[s]);
      n = Ho(n, t), n === true && t.suppressBooleanAttributes ? r += ` ${s.substr(t.attributeNamePrefix.length)}` : r += ` ${s.substr(t.attributeNamePrefix.length)}="${n}"`;
    }
  return r;
}
function Al(e, t) {
  e = e.substr(0, e.length - t.textNodeName.length - 1);
  let r = e.substr(e.lastIndexOf(".") + 1);
  for (let s in t.stopNodes)
    if (t.stopNodes[s] === e || t.stopNodes[s] === "*." + r)
      return true;
  return false;
}
function Ho(e, t) {
  if (e && e.length > 0 && t.processEntities)
    for (let r = 0; r < t.entities.length; r++) {
      const s = t.entities[r];
      e = e.replace(s.regex, s.val);
    }
  return e;
}
var Cl = xl;
var vl = Cl;
var Rl = {
  attributeNamePrefix: "@_",
  attributesGroupName: false,
  textNodeName: "#text",
  ignoreAttributes: true,
  cdataPropName: false,
  format: false,
  indentBy: "  ",
  suppressEmptyNode: false,
  suppressUnpairedNode: true,
  suppressBooleanAttributes: true,
  tagValueProcessor: function(e, t) {
    return t;
  },
  attributeValueProcessor: function(e, t) {
    return t;
  },
  preserveOrder: false,
  commentPropName: false,
  unpairedTags: [],
  entities: [
    { regex: new RegExp("&", "g"), val: "&amp;" },
    //it must be on top
    { regex: new RegExp(">", "g"), val: "&gt;" },
    { regex: new RegExp("<", "g"), val: "&lt;" },
    { regex: new RegExp("'", "g"), val: "&apos;" },
    { regex: new RegExp('"', "g"), val: "&quot;" }
  ],
  processEntities: true,
  stopNodes: [],
  // transformTagName: false,
  // transformAttributeName: false,
  oneListGroup: false
};
function Ge(e) {
  this.options = Object.assign({}, Rl, e), this.options.ignoreAttributes || this.options.attributesGroupName ? this.isAttribute = function() {
    return false;
  } : (this.attrPrefixLen = this.options.attributeNamePrefix.length, this.isAttribute = Bl), this.processTextOrObjNode = kl, this.options.format ? (this.indentate = Tl, this.tagEndChar = `>
`, this.newLine = `
`) : (this.indentate = function() {
    return "";
  }, this.tagEndChar = ">", this.newLine = "");
}
Ge.prototype.build = function(e) {
  return this.options.preserveOrder ? vl(e, this.options) : (Array.isArray(e) && this.options.arrayNodeName && this.options.arrayNodeName.length > 1 && (e = {
    [this.options.arrayNodeName]: e
  }), this.j2x(e, 0).val);
};
Ge.prototype.j2x = function(e, t) {
  let r = "", s = "";
  for (let n in e)
    if (Object.prototype.hasOwnProperty.call(e, n))
      if (typeof e[n] > "u")
        this.isAttribute(n) && (s += "");
      else if (e[n] === null)
        this.isAttribute(n) ? s += "" : n[0] === "?" ? s += this.indentate(t) + "<" + n + "?" + this.tagEndChar : s += this.indentate(t) + "<" + n + "/" + this.tagEndChar;
      else if (e[n] instanceof Date)
        s += this.buildTextValNode(e[n], n, "", t);
      else if (typeof e[n] != "object") {
        const i = this.isAttribute(n);
        if (i)
          r += this.buildAttrPairStr(i, "" + e[n]);
        else if (n === this.options.textNodeName) {
          let o = this.options.tagValueProcessor(n, "" + e[n]);
          s += this.replaceEntitiesValue(o);
        } else
          s += this.buildTextValNode(e[n], n, "", t);
      } else if (Array.isArray(e[n])) {
        const i = e[n].length;
        let o = "", a = "";
        for (let c = 0; c < i; c++) {
          const u = e[n][c];
          if (!(typeof u > "u"))
            if (u === null)
              n[0] === "?" ? s += this.indentate(t) + "<" + n + "?" + this.tagEndChar : s += this.indentate(t) + "<" + n + "/" + this.tagEndChar;
            else if (typeof u == "object")
              if (this.options.oneListGroup) {
                const l = this.j2x(u, t + 1);
                o += l.val, this.options.attributesGroupName && u.hasOwnProperty(this.options.attributesGroupName) && (a += l.attrStr);
              } else
                o += this.processTextOrObjNode(u, n, t);
            else if (this.options.oneListGroup) {
              let l = this.options.tagValueProcessor(n, u);
              l = this.replaceEntitiesValue(l), o += l;
            } else
              o += this.buildTextValNode(u, n, "", t);
        }
        this.options.oneListGroup && (o = this.buildObjectNode(o, n, a, t)), s += o;
      } else if (this.options.attributesGroupName && n === this.options.attributesGroupName) {
        const i = Object.keys(e[n]), o = i.length;
        for (let a = 0; a < o; a++)
          r += this.buildAttrPairStr(i[a], "" + e[n][i[a]]);
      } else
        s += this.processTextOrObjNode(e[n], n, t);
  return { attrStr: r, val: s };
};
Ge.prototype.buildAttrPairStr = function(e, t) {
  return t = this.options.attributeValueProcessor(e, "" + t), t = this.replaceEntitiesValue(t), this.options.suppressBooleanAttributes && t === "true" ? " " + e : " " + e + '="' + t + '"';
};
function kl(e, t, r) {
  const s = this.j2x(e, r + 1);
  return e[this.options.textNodeName] !== void 0 && Object.keys(e).length === 1 ? this.buildTextValNode(e[this.options.textNodeName], t, s.attrStr, r) : this.buildObjectNode(s.val, t, s.attrStr, r);
}
Ge.prototype.buildObjectNode = function(e, t, r, s) {
  if (e === "")
    return t[0] === "?" ? this.indentate(s) + "<" + t + r + "?" + this.tagEndChar : this.indentate(s) + "<" + t + r + this.closeTag(t) + this.tagEndChar;
  {
    let n = "</" + t + this.tagEndChar, i = "";
    return t[0] === "?" && (i = "?", n = ""), (r || r === "") && e.indexOf("<") === -1 ? this.indentate(s) + "<" + t + r + i + ">" + e + n : this.options.commentPropName !== false && t === this.options.commentPropName && i.length === 0 ? this.indentate(s) + `<!--${e}-->` + this.newLine : this.indentate(s) + "<" + t + r + i + this.tagEndChar + e + this.indentate(s) + n;
  }
};
Ge.prototype.closeTag = function(e) {
  let t = "";
  return this.options.unpairedTags.indexOf(e) !== -1 ? this.options.suppressUnpairedNode || (t = "/") : this.options.suppressEmptyNode ? t = "/" : t = `></${e}`, t;
};
Ge.prototype.buildTextValNode = function(e, t, r, s) {
  if (this.options.cdataPropName !== false && t === this.options.cdataPropName)
    return this.indentate(s) + `<![CDATA[${e}]]>` + this.newLine;
  if (this.options.commentPropName !== false && t === this.options.commentPropName)
    return this.indentate(s) + `<!--${e}-->` + this.newLine;
  if (t[0] === "?")
    return this.indentate(s) + "<" + t + r + "?" + this.tagEndChar;
  {
    let n = this.options.tagValueProcessor(t, e);
    return n = this.replaceEntitiesValue(n), n === "" ? this.indentate(s) + "<" + t + r + this.closeTag(t) + this.tagEndChar : this.indentate(s) + "<" + t + r + ">" + n + "</" + t + this.tagEndChar;
  }
};
Ge.prototype.replaceEntitiesValue = function(e) {
  if (e && e.length > 0 && this.options.processEntities)
    for (let t = 0; t < this.options.entities.length; t++) {
      const r = this.options.entities[t];
      e = e.replace(r.regex, r.val);
    }
  return e;
};
function Tl(e) {
  return this.options.indentBy.repeat(e);
}
function Bl(e) {
  return e.startsWith(this.options.attributeNamePrefix) && e !== this.options.textNodeName ? e.substr(this.attrPrefixLen) : false;
}
var Nl = Ge;
var Il = Rs;
var Pl = bl;
var _l = Nl;
var Ml = {
  XMLParser: Pl,
  XMLValidator: Il,
  XMLBuilder: _l
};
var zo = (e, t) => wd(e, t).then((r) => {
  if (r.length) {
    const s = new Ml.XMLParser({
      attributeNamePrefix: "",
      htmlEntities: true,
      ignoreAttributes: false,
      ignoreDeclaration: true,
      parseTagValue: false,
      trimValues: false,
      tagValueProcessor: (c, u) => u.trim() === "" && u.includes(`
`) ? "" : void 0
    });
    s.addEntity("#xD", "\r"), s.addEntity("#10", `
`);
    let n;
    try {
      n = s.parse(r, true);
    } catch (c) {
      throw c && typeof c == "object" && Object.defineProperty(c, "$responseBodyText", {
        value: r
      }), c;
    }
    const i = "#text", o = Object.keys(n)[0], a = n[o];
    return a[i] && (a[o] = a[i], delete a[i]), Oo(a);
  }
  return {};
});
var Ol = async (e, t) => {
  const r = await zo(e, t);
  return r.Error && (r.Error.message = r.Error.message ?? r.Error.Message), r;
};
var Fl = (e, t) => {
  var r;
  if (((r = t == null ? void 0 : t.Error) == null ? void 0 : r.Code) !== void 0)
    return t.Error.Code;
  if ((t == null ? void 0 : t.Code) !== void 0)
    return t.Code;
  if (e.statusCode == 404)
    return "NotFound";
};
var ls = [
  L.CRC32,
  L.CRC32C,
  L.CRC64NVME,
  L.SHA1,
  L.SHA256
];
var Dl = [
  L.SHA256,
  L.SHA1,
  L.CRC32,
  L.CRC32C,
  L.CRC64NVME
];
var Ul = (e, { requestChecksumRequired: t, requestAlgorithmMember: r, requestChecksumCalculation: s }) => {
  if (!r)
    return s === ut.WHEN_SUPPORTED || t ? ss : void 0;
  if (!e[r])
    return;
  const n = e[r];
  if (!ls.includes(n))
    throw new Error(`The checksum algorithm "${n}" is not supported by the client. Select one of ${ls}.`);
  return n;
};
var Bs = (e) => e === L.MD5 ? "content-md5" : `x-amz-checksum-${e.toLowerCase()}`;
var $l = (e, t) => {
  const r = e.toLowerCase();
  for (const s of Object.keys(t))
    if (r === s.toLowerCase())
      return true;
  return false;
};
var Ll = (e, t) => {
  const r = e.toLowerCase();
  for (const s of Object.keys(t))
    if (s.toLowerCase().startsWith(r))
      return true;
  return false;
};
var qo = (e) => e !== void 0 && typeof e != "string" && !ArrayBuffer.isView(e) && !Ro(e);
function Ns(e, t, r, s) {
  function n(i) {
    return i instanceof r ? i : new r(function(o) {
      o(i);
    });
  }
  return new (r || (r = Promise))(function(i, o) {
    function a(l) {
      try {
        u(s.next(l));
      } catch (f) {
        o(f);
      }
    }
    function c(l) {
      try {
        u(s.throw(l));
      } catch (f) {
        o(f);
      }
    }
    function u(l) {
      l.done ? i(l.value) : n(l.value).then(a, c);
    }
    u((s = s.apply(e, t || [])).next());
  });
}
function Is(e, t) {
  var r = { label: 0, sent: function() {
    if (i[0] & 1)
      throw i[1];
    return i[1];
  }, trys: [], ops: [] }, s, n, i, o = Object.create((typeof Iterator == "function" ? Iterator : Object).prototype);
  return o.next = a(0), o.throw = a(1), o.return = a(2), typeof Symbol == "function" && (o[Symbol.iterator] = function() {
    return this;
  }), o;
  function a(u) {
    return function(l) {
      return c([u, l]);
    };
  }
  function c(u) {
    if (s)
      throw new TypeError("Generator is already executing.");
    for (; o && (o = 0, u[0] && (r = 0)), r; )
      try {
        if (s = 1, n && (i = u[0] & 2 ? n.return : u[0] ? n.throw || ((i = n.return) && i.call(n), 0) : n.next) && !(i = i.call(n, u[1])).done)
          return i;
        switch (n = 0, i && (u = [u[0] & 2, i.value]), u[0]) {
          case 0:
          case 1:
            i = u;
            break;
          case 4:
            return r.label++, { value: u[1], done: false };
          case 5:
            r.label++, n = u[1], u = [0];
            continue;
          case 7:
            u = r.ops.pop(), r.trys.pop();
            continue;
          default:
            if (i = r.trys, !(i = i.length > 0 && i[i.length - 1]) && (u[0] === 6 || u[0] === 2)) {
              r = 0;
              continue;
            }
            if (u[0] === 3 && (!i || u[1] > i[0] && u[1] < i[3])) {
              r.label = u[1];
              break;
            }
            if (u[0] === 6 && r.label < i[1]) {
              r.label = i[1], i = u;
              break;
            }
            if (i && r.label < i[2]) {
              r.label = i[2], r.ops.push(u);
              break;
            }
            i[2] && r.ops.pop(), r.trys.pop();
            continue;
        }
        u = t.call(e, r);
      } catch (l) {
        u = [6, l], n = 0;
      } finally {
        s = i = 0;
      }
    if (u[0] & 5)
      throw u[1];
    return { value: u[0] ? u[1] : void 0, done: true };
  }
}
function Vo(e) {
  var t = typeof Symbol == "function" && Symbol.iterator, r = t && e[t], s = 0;
  if (r)
    return r.call(e);
  if (e && typeof e.length == "number")
    return {
      next: function() {
        return e && s >= e.length && (e = void 0), { value: e && e[s++], done: !e };
      }
    };
  throw new TypeError(t ? "Object is not iterable." : "Symbol.iterator is not defined.");
}
var jo = (e) => new TextEncoder().encode(e);
var Hl = typeof Buffer < "u" && Buffer.from ? function(e) {
  return Buffer.from(e, "utf8");
} : jo;
function je(e) {
  return e instanceof Uint8Array ? e : typeof e == "string" ? Hl(e) : ArrayBuffer.isView(e) ? new Uint8Array(e.buffer, e.byteOffset, e.byteLength / Uint8Array.BYTES_PER_ELEMENT) : new Uint8Array(e);
}
function It(e) {
  return typeof e == "string" ? e.length === 0 : e.byteLength === 0;
}
function Wo(e) {
  return new Uint8Array([
    (e & 4278190080) >> 24,
    (e & 16711680) >> 16,
    (e & 65280) >> 8,
    e & 255
  ]);
}
function Go(e) {
  if (!Uint32Array.from) {
    for (var t = new Uint32Array(e.length), r = 0; r < e.length; )
      t[r] = e[r], r += 1;
    return t;
  }
  return Uint32Array.from(e);
}
var zl = (
  /** @class */
  function() {
    function e() {
      this.crc32c = new In();
    }
    return e.prototype.update = function(t) {
      It(t) || this.crc32c.update(je(t));
    }, e.prototype.digest = function() {
      return Ns(this, void 0, void 0, function() {
        return Is(this, function(t) {
          return [2, Wo(this.crc32c.digest())];
        });
      });
    }, e.prototype.reset = function() {
      this.crc32c = new In();
    }, e;
  }()
);
var In = (
  /** @class */
  function() {
    function e() {
      this.checksum = 4294967295;
    }
    return e.prototype.update = function(t) {
      var r, s;
      try {
        for (var n = Vo(t), i = n.next(); !i.done; i = n.next()) {
          var o = i.value;
          this.checksum = this.checksum >>> 8 ^ Vl[(this.checksum ^ o) & 255];
        }
      } catch (a) {
        r = { error: a };
      } finally {
        try {
          i && !i.done && (s = n.return) && s.call(n);
        } finally {
          if (r)
            throw r.error;
        }
      }
      return this;
    }, e.prototype.digest = function() {
      return (this.checksum ^ 4294967295) >>> 0;
    }, e;
  }()
);
var ql = [
  0,
  4067132163,
  3778769143,
  324072436,
  3348797215,
  904991772,
  648144872,
  3570033899,
  2329499855,
  2024987596,
  1809983544,
  2575936315,
  1296289744,
  3207089363,
  2893594407,
  1578318884,
  274646895,
  3795141740,
  4049975192,
  51262619,
  3619967088,
  632279923,
  922689671,
  3298075524,
  2592579488,
  1760304291,
  2075979607,
  2312596564,
  1562183871,
  2943781820,
  3156637768,
  1313733451,
  549293790,
  3537243613,
  3246849577,
  871202090,
  3878099393,
  357341890,
  102525238,
  4101499445,
  2858735121,
  1477399826,
  1264559846,
  3107202533,
  1845379342,
  2677391885,
  2361733625,
  2125378298,
  820201905,
  3263744690,
  3520608582,
  598981189,
  4151959214,
  85089709,
  373468761,
  3827903834,
  3124367742,
  1213305469,
  1526817161,
  2842354314,
  2107672161,
  2412447074,
  2627466902,
  1861252501,
  1098587580,
  3004210879,
  2688576843,
  1378610760,
  2262928035,
  1955203488,
  1742404180,
  2511436119,
  3416409459,
  969524848,
  714683780,
  3639785095,
  205050476,
  4266873199,
  3976438427,
  526918040,
  1361435347,
  2739821008,
  2954799652,
  1114974503,
  2529119692,
  1691668175,
  2005155131,
  2247081528,
  3690758684,
  697762079,
  986182379,
  3366744552,
  476452099,
  3993867776,
  4250756596,
  255256311,
  1640403810,
  2477592673,
  2164122517,
  1922457750,
  2791048317,
  1412925310,
  1197962378,
  3037525897,
  3944729517,
  427051182,
  170179418,
  4165941337,
  746937522,
  3740196785,
  3451792453,
  1070968646,
  1905808397,
  2213795598,
  2426610938,
  1657317369,
  3053634322,
  1147748369,
  1463399397,
  2773627110,
  4215344322,
  153784257,
  444234805,
  3893493558,
  1021025245,
  3467647198,
  3722505002,
  797665321,
  2197175160,
  1889384571,
  1674398607,
  2443626636,
  1164749927,
  3070701412,
  2757221520,
  1446797203,
  137323447,
  4198817972,
  3910406976,
  461344835,
  3484808360,
  1037989803,
  781091935,
  3705997148,
  2460548119,
  1623424788,
  1939049696,
  2180517859,
  1429367560,
  2807687179,
  3020495871,
  1180866812,
  410100952,
  3927582683,
  4182430767,
  186734380,
  3756733383,
  763408580,
  1053836080,
  3434856499,
  2722870694,
  1344288421,
  1131464017,
  2971354706,
  1708204729,
  2545590714,
  2229949006,
  1988219213,
  680717673,
  3673779818,
  3383336350,
  1002577565,
  4010310262,
  493091189,
  238226049,
  4233660802,
  2987750089,
  1082061258,
  1395524158,
  2705686845,
  1972364758,
  2279892693,
  2494862625,
  1725896226,
  952904198,
  3399985413,
  3656866545,
  731699698,
  4283874585,
  222117402,
  510512622,
  3959836397,
  3280807620,
  837199303,
  582374963,
  3504198960,
  68661723,
  4135334616,
  3844915500,
  390545967,
  1230274059,
  3141532936,
  2825850620,
  1510247935,
  2395924756,
  2091215383,
  1878366691,
  2644384480,
  3553878443,
  565732008,
  854102364,
  3229815391,
  340358836,
  3861050807,
  4117890627,
  119113024,
  1493875044,
  2875275879,
  3090270611,
  1247431312,
  2660249211,
  1828433272,
  2141937292,
  2378227087,
  3811616794,
  291187481,
  34330861,
  4032846830,
  615137029,
  3603020806,
  3314634738,
  939183345,
  1776939221,
  2609017814,
  2295496738,
  2058945313,
  2926798794,
  1545135305,
  1330124605,
  3173225534,
  4084100981,
  17165430,
  307568514,
  3762199681,
  888469610,
  3332340585,
  3587147933,
  665062302,
  2042050490,
  2346497209,
  2559330125,
  1793573966,
  3190661285,
  1279665062,
  1595330642,
  2910671697
];
var Vl = Go(ql);
var jl = (
  /** @class */
  function() {
    function e() {
      this.crc32 = new Zt();
    }
    return e.prototype.update = function(t) {
      It(t) || this.crc32.update(je(t));
    }, e.prototype.digest = function() {
      return Ns(this, void 0, void 0, function() {
        return Is(this, function(t) {
          return [2, Wo(this.crc32.digest())];
        });
      });
    }, e.prototype.reset = function() {
      this.crc32 = new Zt();
    }, e;
  }()
);
var Zt = (
  /** @class */
  function() {
    function e() {
      this.checksum = 4294967295;
    }
    return e.prototype.update = function(t) {
      var r, s;
      try {
        for (var n = Vo(t), i = n.next(); !i.done; i = n.next()) {
          var o = i.value;
          this.checksum = this.checksum >>> 8 ^ Gl[(this.checksum ^ o) & 255];
        }
      } catch (a) {
        r = { error: a };
      } finally {
        try {
          i && !i.done && (s = n.return) && s.call(n);
        } finally {
          if (r)
            throw r.error;
        }
      }
      return this;
    }, e.prototype.digest = function() {
      return (this.checksum ^ 4294967295) >>> 0;
    }, e;
  }()
);
var Wl = [
  0,
  1996959894,
  3993919788,
  2567524794,
  124634137,
  1886057615,
  3915621685,
  2657392035,
  249268274,
  2044508324,
  3772115230,
  2547177864,
  162941995,
  2125561021,
  3887607047,
  2428444049,
  498536548,
  1789927666,
  4089016648,
  2227061214,
  450548861,
  1843258603,
  4107580753,
  2211677639,
  325883990,
  1684777152,
  4251122042,
  2321926636,
  335633487,
  1661365465,
  4195302755,
  2366115317,
  997073096,
  1281953886,
  3579855332,
  2724688242,
  1006888145,
  1258607687,
  3524101629,
  2768942443,
  901097722,
  1119000684,
  3686517206,
  2898065728,
  853044451,
  1172266101,
  3705015759,
  2882616665,
  651767980,
  1373503546,
  3369554304,
  3218104598,
  565507253,
  1454621731,
  3485111705,
  3099436303,
  671266974,
  1594198024,
  3322730930,
  2970347812,
  795835527,
  1483230225,
  3244367275,
  3060149565,
  1994146192,
  31158534,
  2563907772,
  4023717930,
  1907459465,
  112637215,
  2680153253,
  3904427059,
  2013776290,
  251722036,
  2517215374,
  3775830040,
  2137656763,
  141376813,
  2439277719,
  3865271297,
  1802195444,
  476864866,
  2238001368,
  4066508878,
  1812370925,
  453092731,
  2181625025,
  4111451223,
  1706088902,
  314042704,
  2344532202,
  4240017532,
  1658658271,
  366619977,
  2362670323,
  4224994405,
  1303535960,
  984961486,
  2747007092,
  3569037538,
  1256170817,
  1037604311,
  2765210733,
  3554079995,
  1131014506,
  879679996,
  2909243462,
  3663771856,
  1141124467,
  855842277,
  2852801631,
  3708648649,
  1342533948,
  654459306,
  3188396048,
  3373015174,
  1466479909,
  544179635,
  3110523913,
  3462522015,
  1591671054,
  702138776,
  2966460450,
  3352799412,
  1504918807,
  783551873,
  3082640443,
  3233442989,
  3988292384,
  2596254646,
  62317068,
  1957810842,
  3939845945,
  2647816111,
  81470997,
  1943803523,
  3814918930,
  2489596804,
  225274430,
  2053790376,
  3826175755,
  2466906013,
  167816743,
  2097651377,
  4027552580,
  2265490386,
  503444072,
  1762050814,
  4150417245,
  2154129355,
  426522225,
  1852507879,
  4275313526,
  2312317920,
  282753626,
  1742555852,
  4189708143,
  2394877945,
  397917763,
  1622183637,
  3604390888,
  2714866558,
  953729732,
  1340076626,
  3518719985,
  2797360999,
  1068828381,
  1219638859,
  3624741850,
  2936675148,
  906185462,
  1090812512,
  3747672003,
  2825379669,
  829329135,
  1181335161,
  3412177804,
  3160834842,
  628085408,
  1382605366,
  3423369109,
  3138078467,
  570562233,
  1426400815,
  3317316542,
  2998733608,
  733239954,
  1555261956,
  3268935591,
  3050360625,
  752459403,
  1541320221,
  2607071920,
  3965973030,
  1969922972,
  40735498,
  2617837225,
  3943577151,
  1913087877,
  83908371,
  2512341634,
  3803740692,
  2075208622,
  213261112,
  2463272603,
  3855990285,
  2094854071,
  198958881,
  2262029012,
  4057260610,
  1759359992,
  534414190,
  2176718541,
  4139329115,
  1873836001,
  414664567,
  2282248934,
  4279200368,
  1711684554,
  285281116,
  2405801727,
  4167216745,
  1634467795,
  376229701,
  2685067896,
  3608007406,
  1308918612,
  956543938,
  2808555105,
  3495958263,
  1231636301,
  1047427035,
  2932959818,
  3654703836,
  1088359270,
  936918e3,
  2847714899,
  3736837829,
  1202900863,
  817233897,
  3183342108,
  3401237130,
  1404277552,
  615818150,
  3134207493,
  3453421203,
  1423857449,
  601450431,
  3009837614,
  3294710456,
  1567103746,
  711928724,
  3020668471,
  3272380065,
  1510334235,
  755167117
];
var Gl = Go(Wl);
var Kl = () => jl;
var Ko = (e, t) => {
  switch (e) {
    case L.MD5:
      return t.md5;
    case L.CRC32:
      return Kl();
    case L.CRC32C:
      return zl;
    case L.CRC64NVME:
      throw new Error(`Please check whether you have installed the "@aws-sdk/crc64-nvme-crt" package explicitly. 
You must also register the package by calling [require("@aws-sdk/crc64-nvme-crt");] or an ESM equivalent such as [import "@aws-sdk/crc64-nvme-crt";]. 
For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt`);
    case L.SHA1:
      return t.sha1;
    case L.SHA256:
      return t.sha256;
    default:
      throw new Error(`Unsupported checksum algorithm: ${e}`);
  }
};
var Xo = (e, t) => {
  const r = new e();
  return r.update(ct(t || "")), r.digest();
};
var Xl = {
  name: "flexibleChecksumsMiddleware",
  step: "build",
  tags: ["BODY_CHECKSUM"],
  override: true
};
var Zl = (e, t) => (r, s) => async (n) => {
  if (!J.isInstance(n.request) || Ll("x-amz-checksum-", n.request.headers))
    return r(n);
  const { request: i, input: o } = n, { body: a, headers: c } = i, { base64Encoder: u, streamHasher: l } = e, { requestChecksumRequired: f, requestAlgorithmMember: p } = t, g = await e.requestChecksumCalculation(), E = p == null ? void 0 : p.name, k = p == null ? void 0 : p.httpHeader;
  E && !o[E] && (g === ut.WHEN_SUPPORTED || f) && (o[E] = ss, k && (c[k] = ss));
  const S = Ul(o, {
    requestChecksumRequired: f,
    requestAlgorithmMember: p == null ? void 0 : p.name,
    requestChecksumCalculation: g
  });
  let N = a, P = c;
  if (S) {
    switch (S) {
      case L.CRC32:
        Q(s, "FLEXIBLE_CHECKSUMS_REQ_CRC32", "U");
        break;
      case L.CRC32C:
        Q(s, "FLEXIBLE_CHECKSUMS_REQ_CRC32C", "V");
        break;
      case L.CRC64NVME:
        Q(s, "FLEXIBLE_CHECKSUMS_REQ_CRC64", "W");
        break;
      case L.SHA1:
        Q(s, "FLEXIBLE_CHECKSUMS_REQ_SHA1", "X");
        break;
      case L.SHA256:
        Q(s, "FLEXIBLE_CHECKSUMS_REQ_SHA256", "Y");
        break;
    }
    const H = Bs(S), pe = Ko(S, e);
    if (qo(a)) {
      const { getAwsChunkedEncodingStream: be, bodyLengthChecker: mt } = e;
      N = be(typeof e.requestStreamBufferSize == "number" && e.requestStreamBufferSize >= 8 * 1024 ? zc(a, e.requestStreamBufferSize, s.logger) : a, {
        base64Encoder: u,
        bodyLengthChecker: mt,
        checksumLocationName: H,
        checksumAlgorithmFn: pe,
        streamHasher: l
      }), P = {
        ...c,
        "content-encoding": c["content-encoding"] ? `${c["content-encoding"]},aws-chunked` : "aws-chunked",
        "transfer-encoding": "chunked",
        "x-amz-decoded-content-length": c["content-length"],
        "x-amz-content-sha256": "STREAMING-UNSIGNED-PAYLOAD-TRAILER",
        "x-amz-trailer": H
      }, delete P["content-length"];
    } else if (!$l(H, c)) {
      const be = await Xo(pe, a);
      P = {
        ...c,
        [H]: u(be)
      };
    }
  }
  return await r({
    ...n,
    request: {
      ...i,
      headers: P,
      body: N
    }
  });
};
var Ql = {
  name: "flexibleChecksumsInputMiddleware",
  toMiddleware: "serializerMiddleware",
  relation: "before",
  tags: ["BODY_CHECKSUM"],
  override: true
};
var Yl = (e, t) => (r, s) => async (n) => {
  const i = n.input, { requestValidationModeMember: o } = t, a = await e.requestChecksumCalculation(), c = await e.responseChecksumValidation();
  switch (a) {
    case ut.WHEN_REQUIRED:
      Q(s, "FLEXIBLE_CHECKSUMS_REQ_WHEN_REQUIRED", "a");
      break;
    case ut.WHEN_SUPPORTED:
      Q(s, "FLEXIBLE_CHECKSUMS_REQ_WHEN_SUPPORTED", "Z");
      break;
  }
  switch (c) {
    case yr.WHEN_REQUIRED:
      Q(s, "FLEXIBLE_CHECKSUMS_RES_WHEN_REQUIRED", "c");
      break;
    case yr.WHEN_SUPPORTED:
      Q(s, "FLEXIBLE_CHECKSUMS_RES_WHEN_SUPPORTED", "b");
      break;
  }
  return o && !i[o] && c === yr.WHEN_SUPPORTED && (i[o] = "ENABLED"), r(n);
};
var Zo = (e = []) => {
  const t = [];
  for (const r of Dl)
    !e.includes(r) || !ls.includes(r) || t.push(r);
  return t;
};
var Jl = (e) => {
  const t = e.lastIndexOf("-");
  if (t !== -1) {
    const r = e.slice(t + 1);
    if (!r.startsWith("0")) {
      const s = parseInt(r, 10);
      if (!isNaN(s) && s >= 1 && s <= 1e4)
        return true;
    }
  }
  return false;
};
var ef = async (e, { checksumAlgorithmFn: t, base64Encoder: r }) => r(await Xo(t, e));
var tf = async (e, { config: t, responseAlgorithms: r, logger: s }) => {
  const n = Zo(r), { body: i, headers: o } = e;
  for (const a of n) {
    const c = Bs(a), u = o[c];
    if (u) {
      let l;
      try {
        l = Ko(a, t);
      } catch (g) {
        if (a === L.CRC64NVME) {
          s == null || s.warn(`Skipping ${L.CRC64NVME} checksum validation: ${g.message}`);
          continue;
        }
        throw g;
      }
      const { base64Encoder: f } = t;
      if (qo(i)) {
        e.body = $c({
          expectedChecksum: u,
          checksumSourceLocation: c,
          checksum: new l(),
          source: i,
          base64Encoder: f
        });
        return;
      }
      const p = await ef(i, { checksumAlgorithmFn: l, base64Encoder: f });
      if (p === u)
        break;
      throw new Error(`Checksum mismatch: expected "${p}" but received "${u}" in response header "${c}".`);
    }
  }
};
var rf = {
  name: "flexibleChecksumsResponseMiddleware",
  toMiddleware: "deserializerMiddleware",
  relation: "after",
  tags: ["BODY_CHECKSUM"],
  override: true
};
var sf = (e, t) => (r, s) => async (n) => {
  if (!J.isInstance(n.request))
    return r(n);
  const i = n.input, o = await r(n), a = o.response, { requestValidationModeMember: c, responseAlgorithms: u } = t;
  if (c && i[c] === "ENABLED") {
    const { clientName: l, commandName: f } = s;
    if (l === "S3Client" && f === "GetObjectCommand" && Zo(u).every((g) => {
      const E = Bs(g), k = a.headers[E];
      return !k || Jl(k);
    }))
      return o;
    await tf(a, {
      config: e,
      responseAlgorithms: u,
      logger: s.logger
    });
  }
  return o;
};
var nf = (e, t) => ({
  applyToStack: (r) => {
    r.add(Zl(e, t), Xl), r.addRelativeTo(Yl(e, t), Ql), r.addRelativeTo(sf(e, t), rf);
  }
});
var of = (e) => ({
  ...e,
  requestChecksumCalculation: Pe(e.requestChecksumCalculation ?? wc),
  responseChecksumValidation: Pe(e.responseChecksumValidation ?? bc),
  requestStreamBufferSize: Number(e.requestStreamBufferSize ?? 0)
});
var af = (e) => (t) => async (r) => {
  if (!J.isInstance(r.request))
    return t(r);
  const { request: s } = r, { handlerProtocol: n = "" } = e.requestHandler.metadata || {};
  if (n.indexOf("h2") >= 0 && !s.headers[":authority"])
    delete s.headers.host, s.headers[":authority"] = s.hostname + (s.port ? ":" + s.port : "");
  else if (!s.headers.host) {
    let i = s.hostname;
    s.port != null && (i += `:${s.port}`), s.headers.host = i;
  }
  return t(r);
};
var cf = {
  name: "hostHeaderMiddleware",
  step: "build",
  priority: "low",
  tags: ["HOST"],
  override: true
};
var uf = (e) => ({
  applyToStack: (t) => {
    t.add(af(e), cf);
  }
});
var df = () => (e, t) => async (r) => {
  var s, n;
  try {
    const i = await e(r), { clientName: o, commandName: a, logger: c, dynamoDbDocumentClientOptions: u = {} } = t, { overrideInputFilterSensitiveLog: l, overrideOutputFilterSensitiveLog: f } = u, p = l ?? t.inputFilterSensitiveLog, g = f ?? t.outputFilterSensitiveLog, { $metadata: E, ...k } = i.output;
    return (s = c == null ? void 0 : c.info) == null || s.call(c, {
      clientName: o,
      commandName: a,
      input: p(r.input),
      output: g(k),
      metadata: E
    }), i;
  } catch (i) {
    const { clientName: o, commandName: a, logger: c, dynamoDbDocumentClientOptions: u = {} } = t, { overrideInputFilterSensitiveLog: l } = u, f = l ?? t.inputFilterSensitiveLog;
    throw (n = c == null ? void 0 : c.error) == null || n.call(c, {
      clientName: o,
      commandName: a,
      input: f(r.input),
      error: i,
      metadata: i.$metadata
    }), i;
  }
};
var lf = {
  name: "loggerMiddleware",
  tags: ["LOGGER"],
  step: "initialize",
  override: true
};
var ff = (e) => ({
  applyToStack: (t) => {
    t.add(df(), lf);
  }
});
var Pn = "X-Amzn-Trace-Id";
var hf = "AWS_LAMBDA_FUNCTION_NAME";
var pf = "_X_AMZN_TRACE_ID";
var gf = (e) => (t) => async (r) => {
  const { request: s } = r;
  if (!J.isInstance(s) || e.runtime !== "node" || s.headers.hasOwnProperty(Pn))
    return t(r);
  const n = process.env[hf], i = process.env[pf], o = (a) => typeof a == "string" && a.length > 0;
  return o(n) && o(i) && (s.headers[Pn] = i), t({
    ...r,
    request: s
  });
};
var mf = {
  step: "build",
  tags: ["RECURSION_DETECTION"],
  name: "recursionDetectionMiddleware",
  override: true,
  priority: "low"
};
var yf = (e) => ({
  applyToStack: (t) => {
    t.add(gf(e), mf);
  }
});
var wf = "content-length";
var bf = "x-amz-decoded-content-length";
function Ef() {
  return (e, t) => async (r) => {
    var n;
    const { request: s } = r;
    if (J.isInstance(s) && !(wf in s.headers) && !(bf in s.headers)) {
      const i = "Are you using a Stream of unknown length as the Body of a PutObject request? Consider using Upload instead from @aws-sdk/lib-storage.";
      typeof ((n = t == null ? void 0 : t.logger) == null ? void 0 : n.warn) == "function" && !(t.logger instanceof vs) && t.logger.warn(i);
    }
    return e({ ...r });
  };
}
var xf = {
  step: "finalizeRequest",
  tags: ["CHECK_CONTENT_LENGTH_HEADER"],
  name: "getCheckContentLengthHeaderPlugin",
  override: true
};
var Sf = (e) => ({
  applyToStack: (t) => {
    t.add(Ef(), xf);
  }
});
var Af = (e) => (t, r) => async (s) => {
  const n = await e.region(), i = e.region;
  let o = () => {
  };
  r.__s3RegionRedirect && (Object.defineProperty(e, "region", {
    writable: false,
    value: async () => r.__s3RegionRedirect
  }), o = () => Object.defineProperty(e, "region", {
    writable: true,
    value: i
  }));
  try {
    const a = await t(s);
    if (r.__s3RegionRedirect) {
      o();
      const c = await e.region();
      if (n !== c)
        throw new Error("Region was not restored following S3 region redirect.");
    }
    return a;
  } catch (a) {
    throw o(), a;
  }
};
var Cf = {
  tags: ["REGION_REDIRECT", "S3"],
  name: "regionRedirectEndpointMiddleware",
  override: true,
  relation: "before",
  toMiddleware: "endpointV2Middleware"
};
function vf(e) {
  return (t, r) => async (s) => {
    var n, i, o;
    try {
      return await t(s);
    } catch (a) {
      if (e.followRegionRedirects && (((n = a == null ? void 0 : a.$metadata) == null ? void 0 : n.httpStatusCode) === 301 || ((i = a == null ? void 0 : a.$metadata) == null ? void 0 : i.httpStatusCode) === 400 && (a == null ? void 0 : a.name) === "IllegalLocationConstraintException")) {
        try {
          const c = a.$response.headers["x-amz-bucket-region"];
          (o = r.logger) == null || o.debug(`Redirecting from ${await e.region()} to ${c}`), r.__s3RegionRedirect = c;
        } catch (c) {
          throw new Error("Region redirect failed: " + c);
        }
        return t(s);
      }
      throw a;
    }
  };
}
var Rf = {
  step: "initialize",
  tags: ["REGION_REDIRECT", "S3"],
  name: "regionRedirectMiddleware",
  override: true
};
var kf = (e) => ({
  applyToStack: (t) => {
    t.add(vf(e), Rf), t.addRelativeTo(Af(e), Cf);
  }
});
var sr = class sr2 {
  constructor(t = {}) {
    _(this, "data");
    _(this, "lastPurgeTime", Date.now());
    this.data = t;
  }
  get(t) {
    const r = this.data[t];
    if (r)
      return r;
  }
  set(t, r) {
    return this.data[t] = r, r;
  }
  delete(t) {
    delete this.data[t];
  }
  async purgeExpired() {
    const t = Date.now();
    if (!(this.lastPurgeTime + sr2.EXPIRED_CREDENTIAL_PURGE_INTERVAL_MS > t))
      for (const r in this.data) {
        const s = this.data[r];
        if (!s.isRefreshing) {
          const n = await s.identity;
          n.expiration && n.expiration.getTime() < t && delete this.data[r];
        }
      }
  }
};
_(sr, "EXPIRED_CREDENTIAL_PURGE_INTERVAL_MS", 3e4);
var fs = sr;
var Cr = class {
  constructor(t, r = false, s = Date.now()) {
    _(this, "_identity");
    _(this, "isRefreshing");
    _(this, "accessed");
    this._identity = t, this.isRefreshing = r, this.accessed = s;
  }
  get identity() {
    return this.accessed = Date.now(), this._identity;
  }
};
var nr = class nr2 {
  constructor(t, r = new fs()) {
    _(this, "createSessionFn");
    _(this, "cache");
    this.createSessionFn = t, this.cache = r;
  }
  async getS3ExpressIdentity(t, r) {
    const s = r.Bucket, { cache: n } = this, i = n.get(s);
    return i ? i.identity.then((o) => {
      var u, l;
      return (((u = o.expiration) == null ? void 0 : u.getTime()) ?? 0) < Date.now() ? n.set(s, new Cr(this.getIdentity(s))).identity : ((((l = o.expiration) == null ? void 0 : l.getTime()) ?? 0) < Date.now() + nr2.REFRESH_WINDOW_MS && !i.isRefreshing && (i.isRefreshing = true, this.getIdentity(s).then((f) => {
        n.set(s, new Cr(Promise.resolve(f)));
      })), o);
    }) : n.set(s, new Cr(this.getIdentity(s))).identity;
  }
  async getIdentity(t) {
    var n, i;
    await this.cache.purgeExpired().catch((o) => {
    });
    const r = await this.createSessionFn(t);
    if (!((n = r.Credentials) != null && n.AccessKeyId) || !((i = r.Credentials) != null && i.SecretAccessKey))
      throw new Error("s3#createSession response credential missing AccessKeyId or SecretAccessKey.");
    return {
      accessKeyId: r.Credentials.AccessKeyId,
      secretAccessKey: r.Credentials.SecretAccessKey,
      sessionToken: r.Credentials.SessionToken,
      expiration: r.Credentials.Expiration ? new Date(r.Credentials.Expiration) : void 0
    };
  }
};
_(nr, "REFRESH_WINDOW_MS", 6e4);
var hs = nr;
var _n;
(function(e) {
  e.ENV = "env", e.CONFIG = "shared config entry";
})(_n || (_n = {}));
var Tf = "Directory";
var Bf = "S3Express";
var Nf = "sigv4-s3express";
var ps = "X-Amz-S3session-Token";
var gs = ps.toLowerCase();
var If = class extends as {
  async signWithCredentials(t, r, s) {
    const n = Mn(r);
    t.headers[gs] = r.sessionToken;
    const i = this;
    return On(i, n), i.signRequest(t, s ?? {});
  }
  async presignWithCredentials(t, r, s) {
    const n = Mn(r);
    return delete t.headers[gs], t.headers[ps] = r.sessionToken, t.query = t.query ?? {}, t.query[ps] = r.sessionToken, On(this, n), this.presign(t, s);
  }
};
function Mn(e) {
  return {
    accessKeyId: e.accessKeyId,
    secretAccessKey: e.secretAccessKey,
    expiration: e.expiration
  };
}
function On(e, t) {
  const r = setTimeout(() => {
    throw new Error("SignatureV4S3Express credential override was created but not called.");
  }, 10), s = e.credentialProvider, n = () => (clearTimeout(r), e.credentialProvider = s, Promise.resolve(t));
  e.credentialProvider = n;
}
var Pf = (e) => (t, r) => async (s) => {
  var n, i, o, a, c;
  if (r.endpointV2) {
    const u = r.endpointV2, l = ((o = (i = (n = u.properties) == null ? void 0 : n.authSchemes) == null ? void 0 : i[0]) == null ? void 0 : o.name) === Nf;
    if ((((a = u.properties) == null ? void 0 : a.backend) === Bf || ((c = u.properties) == null ? void 0 : c.bucketType) === Tf) && (Q(r, "S3_EXPRESS_BUCKET", "J"), r.isS3ExpressBucket = true), l) {
      const p = s.input.Bucket;
      if (p) {
        const g = await e.s3ExpressIdentityProvider.getS3ExpressIdentity(await e.credentials(), {
          Bucket: p
        });
        r.s3ExpressIdentity = g, J.isInstance(s.request) && g.sessionToken && (s.request.headers[gs] = g.sessionToken);
      }
    }
  }
  return t(s);
};
var _f = {
  name: "s3ExpressMiddleware",
  step: "build",
  tags: ["S3", "S3_EXPRESS"],
  override: true
};
var Mf = (e) => ({
  applyToStack: (t) => {
    t.add(Pf(e), _f);
  }
});
var Of = async (e, t, r, s) => {
  const n = await s.signWithCredentials(r, e, {});
  if (n.headers["X-Amz-Security-Token"] || n.headers["x-amz-security-token"])
    throw new Error("X-Amz-Security-Token must not be set for s3-express requests.");
  return n;
};
var Ff = (e) => (t) => {
  throw t;
};
var Df = (e, t) => {
};
var Uf = (e) => (t, r) => async (s) => {
  if (!J.isInstance(s.request))
    return t(s);
  const i = pt(r).selectedHttpAuthScheme;
  if (!i)
    throw new Error("No HttpAuthScheme was selected: unable to sign request");
  const { httpAuthOption: { signingProperties: o = {} }, identity: a, signer: c } = i;
  let u;
  r.s3ExpressIdentity ? u = await Of(r.s3ExpressIdentity, o, s.request, await e.signer()) : u = await c.sign(s.request, a, o);
  const l = await t({
    ...s,
    request: u
  }).catch((c.errorHandler || Ff)(o));
  return (c.successHandler || Df)(l.response, o), l;
};
var $f = (e) => ({
  applyToStack: (t) => {
    t.addRelativeTo(Uf(e), ho);
  }
});
var Lf = (e, { session: t }) => {
  const [r, s] = t;
  return {
    ...e,
    forcePathStyle: e.forcePathStyle ?? false,
    useAccelerateEndpoint: e.useAccelerateEndpoint ?? false,
    disableMultiregionAccessPoints: e.disableMultiregionAccessPoints ?? false,
    followRegionRedirects: e.followRegionRedirects ?? false,
    s3ExpressIdentityProvider: e.s3ExpressIdentityProvider ?? new hs(async (n) => r().send(new s({
      Bucket: n
    }))),
    bucketEndpoint: e.bucketEndpoint ?? false
  };
};
var Hf = {
  CopyObjectCommand: true,
  UploadPartCopyCommand: true,
  CompleteMultipartUploadCommand: true
};
var zf = 3e3;
var qf = (e) => (t, r) => async (s) => {
  const n = await t(s), { response: i } = n;
  if (!Nt.isInstance(i))
    return n;
  const { statusCode: o, body: a } = i;
  if (o < 200 || o >= 300 || !(typeof (a == null ? void 0 : a.stream) == "function" || typeof (a == null ? void 0 : a.pipe) == "function" || typeof (a == null ? void 0 : a.tee) == "function"))
    return n;
  let u = a, l = a;
  a && typeof a == "object" && !(a instanceof Uint8Array) && ([u, l] = await eu(a)), i.body = l;
  const f = await Vf(u, {
    streamCollector: async (g) => Wc(g, zf)
  });
  typeof (u == null ? void 0 : u.destroy) == "function" && u.destroy();
  const p = e.utf8Encoder(f.subarray(f.length - 16));
  if (f.length === 0 && Hf[r.commandName]) {
    const g = new Error("S3 aborted request");
    throw g.name = "InternalError", g;
  }
  return p && p.endsWith("</Error>") && (i.statusCode = 400), n;
};
var Vf = (e = new Uint8Array(), t) => e instanceof Uint8Array ? Promise.resolve(e) : t.streamCollector(e) || Promise.resolve(new Uint8Array());
var jf = {
  relation: "after",
  toMiddleware: "deserializerMiddleware",
  tags: ["THROW_200_EXCEPTIONS", "S3"],
  name: "throw200ExceptionsMiddleware",
  override: true
};
var Qo = (e) => ({
  applyToStack: (t) => {
    t.addRelativeTo(qf(e), jf);
  }
});
var Wf = (e) => typeof e == "string" && e.indexOf("arn:") === 0 && e.split(":").length >= 6;
function Gf(e) {
  return (t, r) => async (s) => {
    var n, i, o, a;
    if (e.bucketEndpoint) {
      const c = r.endpointV2;
      if (c) {
        const u = s.input.Bucket;
        if (typeof u == "string")
          try {
            const l = new URL(u);
            r.endpointV2 = {
              ...c,
              url: l
            };
          } catch (l) {
            const f = `@aws-sdk/middleware-sdk-s3: bucketEndpoint=true was set but Bucket=${u} could not be parsed as URL.`;
            throw ((i = (n = r.logger) == null ? void 0 : n.constructor) == null ? void 0 : i.name) === "NoOpLogger" || (a = (o = r.logger) == null ? void 0 : o.warn) == null || a.call(o, f), l;
          }
      }
    }
    return t(s);
  };
}
var Kf = {
  name: "bucketEndpointMiddleware",
  override: true,
  relation: "after",
  toMiddleware: "endpointV2Middleware"
};
function Xf({ bucketEndpoint: e }) {
  return (t) => async (r) => {
    const { input: { Bucket: s } } = r;
    if (!e && typeof s == "string" && !Wf(s) && s.indexOf("/") >= 0) {
      const n = new Error(`Bucket name shouldn't contain '/', received '${s}'`);
      throw n.name = "InvalidBucketName", n;
    }
    return t({ ...r });
  };
}
var Zf = {
  step: "initialize",
  tags: ["VALIDATE_BUCKET_NAME"],
  name: "validateBucketNameMiddleware",
  override: true
};
var Qf = (e) => ({
  applyToStack: (t) => {
    t.add(Xf(e), Zf), t.addRelativeTo(Gf(e), Kf);
  }
});
var Yf = void 0;
function Jf(e) {
  return e === void 0 ? true : typeof e == "string" && e.length <= 50;
}
function eh(e) {
  const t = it(e.userAgentAppId ?? Yf);
  return {
    ...e,
    customUserAgent: typeof e.customUserAgent == "string" ? [[e.customUserAgent]] : e.customUserAgent,
    userAgentAppId: async () => {
      var s, n;
      const r = await t();
      if (!Jf(r)) {
        const i = ((n = (s = e.logger) == null ? void 0 : s.constructor) == null ? void 0 : n.name) === "NoOpLogger" || !e.logger ? console : e.logger;
        typeof r != "string" ? i == null || i.warn("userAgentAppId must be a string or undefined.") : r.length > 50 && (i == null || i.warn("The provided userAgentAppId exceeds the maximum length of 50 characters."));
      }
      return r;
    }
  };
}
var th = class {
  constructor({ size: t, params: r }) {
    this.data = /* @__PURE__ */ new Map(), this.parameters = [], this.capacity = t ?? 50, r && (this.parameters = r);
  }
  get(t, r) {
    const s = this.hash(t);
    if (s === false)
      return r();
    if (!this.data.has(s)) {
      if (this.data.size > this.capacity + 10) {
        const n = this.data.keys();
        let i = 0;
        for (; ; ) {
          const { value: o, done: a } = n.next();
          if (this.data.delete(o), a || ++i > 10)
            break;
        }
      }
      this.data.set(s, r());
    }
    return this.data.get(s);
  }
  size() {
    return this.data.size;
  }
  hash(t) {
    let r = "";
    const { parameters: s } = this;
    if (s.length === 0)
      return false;
    for (const n of s) {
      const i = String(t[n] ?? "");
      if (i.includes("|;"))
        return false;
      r += i + "|;";
    }
    return r;
  }
};
var rh = new RegExp("^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$");
var Yo = (e) => rh.test(e) || e.startsWith("[") && e.endsWith("]");
var sh = new RegExp("^(?!.*-$)(?!-)[a-zA-Z0-9-]{1,63}$");
var Ps = (e, t = false) => {
  if (!t)
    return sh.test(e);
  const r = e.split(".");
  for (const s of r)
    if (!Ps(s))
      return false;
  return true;
};
var Qt = {};
var Pt = "endpoints";
function We(e) {
  return typeof e != "object" || e == null ? e : "ref" in e ? `$${We(e.ref)}` : "fn" in e ? `${e.fn}(${(e.argv || []).map(We).join(", ")})` : JSON.stringify(e, null, 2);
}
var Ae = class extends Error {
  constructor(t) {
    super(t), this.name = "EndpointError";
  }
};
var nh = (e, t) => e === t;
var ih = (e) => {
  const t = e.split("."), r = [];
  for (const s of t) {
    const n = s.indexOf("[");
    if (n !== -1) {
      if (s.indexOf("]") !== s.length - 1)
        throw new Ae(`Path: '${e}' does not end with ']'`);
      const i = s.slice(n + 1, -1);
      if (Number.isNaN(parseInt(i)))
        throw new Ae(`Invalid array index: '${i}' in path: '${e}'`);
      n !== 0 && r.push(s.slice(0, n)), r.push(i);
    } else
      r.push(s);
  }
  return r;
};
var Jo = (e, t) => ih(t).reduce((r, s) => {
  if (typeof r != "object")
    throw new Ae(`Index '${s}' in '${t}' not found in '${JSON.stringify(e)}'`);
  return Array.isArray(r) ? r[parseInt(s)] : r[s];
}, e);
var oh = (e) => e != null;
var ah = (e) => !e;
var vr = {
  [Bt.HTTP]: 80,
  [Bt.HTTPS]: 443
};
var ch = (e) => {
  const t = (() => {
    try {
      if (e instanceof URL)
        return e;
      if (typeof e == "object" && "hostname" in e) {
        const { hostname: p, port: g, protocol: E = "", path: k = "", query: S = {} } = e, N = new URL(`${E}//${p}${g ? `:${g}` : ""}${k}`);
        return N.search = Object.entries(S).map(([P, V]) => `${P}=${V}`).join("&"), N;
      }
      return new URL(e);
    } catch {
      return null;
    }
  })();
  if (!t)
    return null;
  const r = t.href, { host: s, hostname: n, pathname: i, protocol: o, search: a } = t;
  if (a)
    return null;
  const c = o.slice(0, -1);
  if (!Object.values(Bt).includes(c))
    return null;
  const u = Yo(n), l = r.includes(`${s}:${vr[c]}`) || typeof e == "string" && e.includes(`${s}:${vr[c]}`), f = `${s}${l ? `:${vr[c]}` : ""}`;
  return {
    scheme: c,
    authority: f,
    path: i,
    normalizedPath: i.endsWith("/") ? i : `${i}/`,
    isIp: u
  };
};
var uh = (e, t) => e === t;
var dh = (e, t, r, s) => t >= r || e.length < r ? null : s ? e.substring(e.length - r, e.length - t) : e.substring(t, r);
var lh = (e) => encodeURIComponent(e).replace(/[!*'()]/g, (t) => `%${t.charCodeAt(0).toString(16).toUpperCase()}`);
var fh = {
  booleanEquals: nh,
  getAttr: Jo,
  isSet: oh,
  isValidHostLabel: Ps,
  not: ah,
  parseURL: ch,
  stringEquals: uh,
  substring: dh,
  uriEncode: lh
};
var ea = (e, t) => {
  const r = [], s = {
    ...t.endpointParams,
    ...t.referenceRecord
  };
  let n = 0;
  for (; n < e.length; ) {
    const i = e.indexOf("{", n);
    if (i === -1) {
      r.push(e.slice(n));
      break;
    }
    r.push(e.slice(n, i));
    const o = e.indexOf("}", i);
    if (o === -1) {
      r.push(e.slice(i));
      break;
    }
    e[i + 1] === "{" && e[o + 1] === "}" && (r.push(e.slice(i + 1, o)), n = o + 2);
    const a = e.substring(i + 1, o);
    if (a.includes("#")) {
      const [c, u] = a.split("#");
      r.push(Jo(s[c], u));
    } else
      r.push(s[a]);
    n = o + 1;
  }
  return r.join("");
};
var hh = ({ ref: e }, t) => ({
  ...t.endpointParams,
  ...t.referenceRecord
})[e];
var cr = (e, t, r) => {
  if (typeof e == "string")
    return ea(e, r);
  if (e.fn)
    return ta(e, r);
  if (e.ref)
    return hh(e, r);
  throw new Ae(`'${t}': ${String(e)} is not a string, function or reference.`);
};
var ta = ({ fn: e, argv: t }, r) => {
  const s = t.map((i) => ["boolean", "number"].includes(typeof i) ? i : cr(i, "arg", r)), n = e.split(".");
  return n[0] in Qt && n[1] != null ? Qt[n[0]][n[1]](...s) : fh[e](...s);
};
var ph = ({ assign: e, ...t }, r) => {
  var n, i;
  if (e && e in r.referenceRecord)
    throw new Ae(`'${e}' is already defined in Reference Record.`);
  const s = ta(t, r);
  return (i = (n = r.logger) == null ? void 0 : n.debug) == null || i.call(n, `${Pt} evaluateCondition: ${We(t)} = ${We(s)}`), {
    result: s === "" ? true : !!s,
    ...e != null && { toAssign: { name: e, value: s } }
  };
};
var _s = (e = [], t) => {
  var s, n;
  const r = {};
  for (const i of e) {
    const { result: o, toAssign: a } = ph(i, {
      ...t,
      referenceRecord: {
        ...t.referenceRecord,
        ...r
      }
    });
    if (!o)
      return { result: o };
    a && (r[a.name] = a.value, (n = (s = t.logger) == null ? void 0 : s.debug) == null || n.call(s, `${Pt} assign: ${a.name} := ${We(a.value)}`));
  }
  return { result: true, referenceRecord: r };
};
var gh = (e, t) => Object.entries(e).reduce((r, [s, n]) => ({
  ...r,
  [s]: n.map((i) => {
    const o = cr(i, "Header value entry", t);
    if (typeof o != "string")
      throw new Ae(`Header '${s}' value '${o}' is not a string`);
    return o;
  })
}), {});
var ra = (e, t) => {
  if (Array.isArray(e))
    return e.map((r) => ra(r, t));
  switch (typeof e) {
    case "string":
      return ea(e, t);
    case "object":
      if (e === null)
        throw new Ae(`Unexpected endpoint property: ${e}`);
      return sa(e, t);
    case "boolean":
      return e;
    default:
      throw new Ae(`Unexpected endpoint property type: ${typeof e}`);
  }
};
var sa = (e, t) => Object.entries(e).reduce((r, [s, n]) => ({
  ...r,
  [s]: ra(n, t)
}), {});
var mh = (e, t) => {
  const r = cr(e, "Endpoint URL", t);
  if (typeof r == "string")
    try {
      return new URL(r);
    } catch (s) {
      throw s;
    }
  throw new Ae(`Endpoint URL must be a string, got ${typeof r}`);
};
var yh = (e, t) => {
  var l, f;
  const { conditions: r, endpoint: s } = e, { result: n, referenceRecord: i } = _s(r, t);
  if (!n)
    return;
  const o = {
    ...t,
    referenceRecord: { ...t.referenceRecord, ...i }
  }, { url: a, properties: c, headers: u } = s;
  return (f = (l = t.logger) == null ? void 0 : l.debug) == null || f.call(l, `${Pt} Resolving endpoint from template: ${We(s)}`), {
    ...u != null && {
      headers: gh(u, o)
    },
    ...c != null && {
      properties: sa(c, o)
    },
    url: mh(a, o)
  };
};
var wh = (e, t) => {
  const { conditions: r, error: s } = e, { result: n, referenceRecord: i } = _s(r, t);
  if (n)
    throw new Ae(cr(s, "Error", {
      ...t,
      referenceRecord: { ...t.referenceRecord, ...i }
    }));
};
var bh = (e, t) => {
  const { conditions: r, rules: s } = e, { result: n, referenceRecord: i } = _s(r, t);
  if (n)
    return na(s, {
      ...t,
      referenceRecord: { ...t.referenceRecord, ...i }
    });
};
var na = (e, t) => {
  for (const r of e)
    if (r.type === "endpoint") {
      const s = yh(r, t);
      if (s)
        return s;
    } else if (r.type === "error")
      wh(r, t);
    else if (r.type === "tree") {
      const s = bh(r, t);
      if (s)
        return s;
    } else
      throw new Ae(`Unknown endpoint rule: ${r}`);
  throw new Ae("Rules evaluation failed");
};
var Eh = (e, t) => {
  var u, l, f, p;
  const { endpointParams: r, logger: s } = t, { parameters: n, rules: i } = e;
  (l = (u = t.logger) == null ? void 0 : u.debug) == null || l.call(u, `${Pt} Initial EndpointParams: ${We(r)}`);
  const o = Object.entries(n).filter(([, g]) => g.default != null).map(([g, E]) => [g, E.default]);
  if (o.length > 0)
    for (const [g, E] of o)
      r[g] = r[g] ?? E;
  const a = Object.entries(n).filter(([, g]) => g.required).map(([g]) => g);
  for (const g of a)
    if (r[g] == null)
      throw new Ae(`Missing required parameter: '${g}'`);
  const c = na(i, { endpointParams: r, logger: s, referenceRecord: {} });
  return (p = (f = t.logger) == null ? void 0 : f.debug) == null || p.call(f, `${Pt} Resolved endpoint: ${We(c)}`), c;
};
var ia = (e, t = false) => {
  if (t) {
    for (const r of e.split("."))
      if (!ia(r))
        return false;
    return true;
  }
  return !(!Ps(e) || e.length < 3 || e.length > 63 || e !== e.toLowerCase() || Yo(e));
};
var Fn = ":";
var xh = "/";
var Sh = (e) => {
  const t = e.split(Fn);
  if (t.length < 6)
    return null;
  const [r, s, n, i, o, ...a] = t;
  if (r !== "arn" || s === "" || n === "" || a.join(Fn) === "")
    return null;
  const c = a.map((u) => u.split(xh)).flat();
  return {
    partition: s,
    service: n,
    region: i,
    accountId: o,
    resourceId: c
  };
};
var Ah = [
  {
    id: "aws",
    outputs: {
      dnsSuffix: "amazonaws.com",
      dualStackDnsSuffix: "api.aws",
      implicitGlobalRegion: "us-east-1",
      name: "aws",
      supportsDualStack: true,
      supportsFIPS: true
    },
    regionRegex: "^(us|eu|ap|sa|ca|me|af|il|mx)\\-\\w+\\-\\d+$",
    regions: {
      "af-south-1": {
        description: "Africa (Cape Town)"
      },
      "ap-east-1": {
        description: "Asia Pacific (Hong Kong)"
      },
      "ap-northeast-1": {
        description: "Asia Pacific (Tokyo)"
      },
      "ap-northeast-2": {
        description: "Asia Pacific (Seoul)"
      },
      "ap-northeast-3": {
        description: "Asia Pacific (Osaka)"
      },
      "ap-south-1": {
        description: "Asia Pacific (Mumbai)"
      },
      "ap-south-2": {
        description: "Asia Pacific (Hyderabad)"
      },
      "ap-southeast-1": {
        description: "Asia Pacific (Singapore)"
      },
      "ap-southeast-2": {
        description: "Asia Pacific (Sydney)"
      },
      "ap-southeast-3": {
        description: "Asia Pacific (Jakarta)"
      },
      "ap-southeast-4": {
        description: "Asia Pacific (Melbourne)"
      },
      "ap-southeast-5": {
        description: "Asia Pacific (Malaysia)"
      },
      "ap-southeast-7": {
        description: "Asia Pacific (Thailand)"
      },
      "aws-global": {
        description: "AWS Standard global region"
      },
      "ca-central-1": {
        description: "Canada (Central)"
      },
      "ca-west-1": {
        description: "Canada West (Calgary)"
      },
      "eu-central-1": {
        description: "Europe (Frankfurt)"
      },
      "eu-central-2": {
        description: "Europe (Zurich)"
      },
      "eu-north-1": {
        description: "Europe (Stockholm)"
      },
      "eu-south-1": {
        description: "Europe (Milan)"
      },
      "eu-south-2": {
        description: "Europe (Spain)"
      },
      "eu-west-1": {
        description: "Europe (Ireland)"
      },
      "eu-west-2": {
        description: "Europe (London)"
      },
      "eu-west-3": {
        description: "Europe (Paris)"
      },
      "il-central-1": {
        description: "Israel (Tel Aviv)"
      },
      "me-central-1": {
        description: "Middle East (UAE)"
      },
      "me-south-1": {
        description: "Middle East (Bahrain)"
      },
      "mx-central-1": {
        description: "Mexico (Central)"
      },
      "sa-east-1": {
        description: "South America (Sao Paulo)"
      },
      "us-east-1": {
        description: "US East (N. Virginia)"
      },
      "us-east-2": {
        description: "US East (Ohio)"
      },
      "us-west-1": {
        description: "US West (N. California)"
      },
      "us-west-2": {
        description: "US West (Oregon)"
      }
    }
  },
  {
    id: "aws-cn",
    outputs: {
      dnsSuffix: "amazonaws.com.cn",
      dualStackDnsSuffix: "api.amazonwebservices.com.cn",
      implicitGlobalRegion: "cn-northwest-1",
      name: "aws-cn",
      supportsDualStack: true,
      supportsFIPS: true
    },
    regionRegex: "^cn\\-\\w+\\-\\d+$",
    regions: {
      "aws-cn-global": {
        description: "AWS China global region"
      },
      "cn-north-1": {
        description: "China (Beijing)"
      },
      "cn-northwest-1": {
        description: "China (Ningxia)"
      }
    }
  },
  {
    id: "aws-us-gov",
    outputs: {
      dnsSuffix: "amazonaws.com",
      dualStackDnsSuffix: "api.aws",
      implicitGlobalRegion: "us-gov-west-1",
      name: "aws-us-gov",
      supportsDualStack: true,
      supportsFIPS: true
    },
    regionRegex: "^us\\-gov\\-\\w+\\-\\d+$",
    regions: {
      "aws-us-gov-global": {
        description: "AWS GovCloud (US) global region"
      },
      "us-gov-east-1": {
        description: "AWS GovCloud (US-East)"
      },
      "us-gov-west-1": {
        description: "AWS GovCloud (US-West)"
      }
    }
  },
  {
    id: "aws-iso",
    outputs: {
      dnsSuffix: "c2s.ic.gov",
      dualStackDnsSuffix: "c2s.ic.gov",
      implicitGlobalRegion: "us-iso-east-1",
      name: "aws-iso",
      supportsDualStack: false,
      supportsFIPS: true
    },
    regionRegex: "^us\\-iso\\-\\w+\\-\\d+$",
    regions: {
      "aws-iso-global": {
        description: "AWS ISO (US) global region"
      },
      "us-iso-east-1": {
        description: "US ISO East"
      },
      "us-iso-west-1": {
        description: "US ISO WEST"
      }
    }
  },
  {
    id: "aws-iso-b",
    outputs: {
      dnsSuffix: "sc2s.sgov.gov",
      dualStackDnsSuffix: "sc2s.sgov.gov",
      implicitGlobalRegion: "us-isob-east-1",
      name: "aws-iso-b",
      supportsDualStack: false,
      supportsFIPS: true
    },
    regionRegex: "^us\\-isob\\-\\w+\\-\\d+$",
    regions: {
      "aws-iso-b-global": {
        description: "AWS ISOB (US) global region"
      },
      "us-isob-east-1": {
        description: "US ISOB East (Ohio)"
      }
    }
  },
  {
    id: "aws-iso-e",
    outputs: {
      dnsSuffix: "cloud.adc-e.uk",
      dualStackDnsSuffix: "cloud.adc-e.uk",
      implicitGlobalRegion: "eu-isoe-west-1",
      name: "aws-iso-e",
      supportsDualStack: false,
      supportsFIPS: true
    },
    regionRegex: "^eu\\-isoe\\-\\w+\\-\\d+$",
    regions: {
      "eu-isoe-west-1": {
        description: "EU ISOE West"
      }
    }
  },
  {
    id: "aws-iso-f",
    outputs: {
      dnsSuffix: "csp.hci.ic.gov",
      dualStackDnsSuffix: "csp.hci.ic.gov",
      implicitGlobalRegion: "us-isof-south-1",
      name: "aws-iso-f",
      supportsDualStack: false,
      supportsFIPS: true
    },
    regionRegex: "^us\\-isof\\-\\w+\\-\\d+$",
    regions: {
      "aws-iso-f-global": {
        description: "AWS ISOF global region"
      },
      "us-isof-east-1": {
        description: "US ISOF EAST"
      },
      "us-isof-south-1": {
        description: "US ISOF SOUTH"
      }
    }
  }
];
var Ch = "1.1";
var vh = {
  partitions: Ah,
  version: Ch
};
var Rh = vh;
var kh = (e) => {
  const { partitions: t } = Rh;
  for (const s of t) {
    const { regions: n, outputs: i } = s;
    for (const [o, a] of Object.entries(n))
      if (o === e)
        return {
          ...i,
          ...a
        };
  }
  for (const s of t) {
    const { regionRegex: n, outputs: i } = s;
    if (new RegExp(n).test(e))
      return {
        ...i
      };
  }
  const r = t.find((s) => s.id === "aws");
  if (!r)
    throw new Error("Provided region was not found in the partition array or regex, and default partition with id 'aws' doesn't exist.");
  return {
    ...r.outputs
  };
};
var oa = {
  isVirtualHostableS3Bucket: ia,
  parseArn: Sh,
  partition: kh
};
Qt.aws = oa;
var Th = /\d{12}\.ddb/;
async function Bh(e, t, r) {
  var i, o, a, c, u, l, f;
  const s = r.request;
  if (((i = s == null ? void 0 : s.headers) == null ? void 0 : i["smithy-protocol"]) === "rpc-v2-cbor" && Q(e, "PROTOCOL_RPC_V2_CBOR", "M"), typeof t.retryStrategy == "function") {
    const p = await t.retryStrategy();
    typeof p.acquireInitialRetryToken == "function" ? (a = (o = p.constructor) == null ? void 0 : o.name) != null && a.includes("Adaptive") ? Q(e, "RETRY_MODE_ADAPTIVE", "F") : Q(e, "RETRY_MODE_STANDARD", "E") : Q(e, "RETRY_MODE_LEGACY", "D");
  }
  if (typeof t.accountIdEndpointMode == "function") {
    const p = e.endpointV2;
    switch (String((c = p == null ? void 0 : p.url) == null ? void 0 : c.hostname).match(Th) && Q(e, "ACCOUNT_ID_ENDPOINT", "O"), await ((u = t.accountIdEndpointMode) == null ? void 0 : u.call(t))) {
      case "disabled":
        Q(e, "ACCOUNT_ID_MODE_DISABLED", "Q");
        break;
      case "preferred":
        Q(e, "ACCOUNT_ID_MODE_PREFERRED", "P");
        break;
      case "required":
        Q(e, "ACCOUNT_ID_MODE_REQUIRED", "R");
        break;
    }
  }
  const n = (f = (l = e.__smithy_context) == null ? void 0 : l.selectedHttpAuthScheme) == null ? void 0 : f.identity;
  if (n != null && n.$source) {
    const p = n;
    p.accountId && Q(e, "RESOLVED_ACCOUNT_ID", "T");
    for (const [g, E] of Object.entries(p.$source ?? {}))
      Q(e, g, E);
  }
}
var Dn = "user-agent";
var Rr = "x-amz-user-agent";
var Un = " ";
var kr = "/";
var Nh = /[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w]/g;
var Ih = /[^\!\$\%\&\'\*\+\-\.\^\_\`\|\~\d\w\#]/g;
var $n = "-";
var Ph = 1024;
function _h(e) {
  let t = "";
  for (const r in e) {
    const s = e[r];
    if (t.length + s.length + 1 <= Ph) {
      t.length ? t += "," + s : t += s;
      continue;
    }
    break;
  }
  return t;
}
var Mh = (e) => (t, r) => async (s) => {
  var g, E, k, S;
  const { request: n } = s;
  if (!J.isInstance(n))
    return t(s);
  const { headers: i } = n, o = ((g = r == null ? void 0 : r.userAgent) == null ? void 0 : g.map(Ft)) || [], a = (await e.defaultUserAgentProvider()).map(Ft);
  await Bh(r, e, s);
  const c = r;
  a.push(`m/${_h(Object.assign({}, (E = r.__smithy_context) == null ? void 0 : E.features, (k = c.__aws_sdk_context) == null ? void 0 : k.features))}`);
  const u = ((S = e == null ? void 0 : e.customUserAgent) == null ? void 0 : S.map(Ft)) || [], l = await e.userAgentAppId();
  l && a.push(Ft([`app/${l}`]));
  const f = [].concat([...a, ...o, ...u]).join(Un), p = [
    ...a.filter((N) => N.startsWith("aws-sdk-")),
    ...u
  ].join(Un);
  return e.runtime !== "browser" ? (p && (i[Rr] = i[Rr] ? `${i[Dn]} ${p}` : p), i[Dn] = f) : i[Rr] = f, t({
    ...s,
    request: n
  });
};
var Ft = (e) => {
  var o;
  const t = e[0].split(kr).map((a) => a.replace(Nh, $n)).join(kr), r = (o = e[1]) == null ? void 0 : o.replace(Ih, $n), s = t.indexOf(kr), n = t.substring(0, s);
  let i = t.substring(s + 1);
  return n === "api" && (i = i.toLowerCase()), [n, i, r].filter((a) => a && a.length > 0).reduce((a, c, u) => {
    switch (u) {
      case 0:
        return c;
      case 1:
        return `${a}/${c}`;
      default:
        return `${a}#${c}`;
    }
  }, "");
};
var Oh = {
  name: "getUserAgentMiddleware",
  step: "build",
  priority: "low",
  tags: ["SET_USER_AGENT", "USER_AGENT"],
  override: true
};
var Fh = (e) => ({
  applyToStack: (t) => {
    t.add(Mh(e), Oh);
  }
});
var Dh = false;
var Uh = false;
var aa = (e) => typeof e == "string" && (e.startsWith("fips-") || e.endsWith("-fips"));
var Ln = (e) => aa(e) ? ["fips-aws-global", "aws-fips"].includes(e) ? "us-east-1" : e.replace(/fips-(dkr-|prod-)?|-fips/, "") : e;
var $h = (e) => {
  const { region: t, useFipsEndpoint: r } = e;
  if (!t)
    throw new Error("Region is missing");
  return {
    ...e,
    region: async () => {
      if (typeof t == "string")
        return Ln(t);
      const s = await t();
      return Ln(s);
    },
    useFipsEndpoint: async () => {
      const s = typeof t == "string" ? t : await t();
      return aa(s) ? true : typeof r != "function" ? Promise.resolve(!!r) : r();
    }
  };
};
var Lh = (e) => ({
  ...e,
  eventStreamMarshaller: e.eventStreamSerdeProvider(e)
});
var Hn = "content-length";
function Hh(e) {
  return (t) => async (r) => {
    const s = r.request;
    if (J.isInstance(s)) {
      const { body: n, headers: i } = s;
      if (n && Object.keys(i).map((o) => o.toLowerCase()).indexOf(Hn) === -1)
        try {
          const o = e(n);
          s.headers = {
            ...s.headers,
            [Hn]: String(o)
          };
        } catch {
        }
    }
    return t({
      ...r,
      request: s
    });
  };
}
var zh = {
  step: "build",
  tags: ["SET_CONTENT_LENGTH", "CONTENT_LENGTH"],
  name: "contentLengthMiddleware",
  override: true
};
var qh = (e) => ({
  applyToStack: (t) => {
    t.add(Hh(e.bodyLengthChecker), zh);
  }
});
var Vh = async (e) => {
  const t = (e == null ? void 0 : e.Bucket) || "";
  if (typeof e.Bucket == "string" && (e.Bucket = t.replace(/#/g, encodeURIComponent("#")).replace(/\?/g, encodeURIComponent("?"))), Xh(t)) {
    if (e.ForcePathStyle === true)
      throw new Error("Path-style addressing cannot be used with ARN buckets");
  } else
    (!Kh(t) || t.indexOf(".") !== -1 && !String(e.Endpoint).startsWith("http:") || t.toLowerCase() !== t || t.length < 3) && (e.ForcePathStyle = true);
  return e.DisableMultiRegionAccessPoints && (e.disableMultiRegionAccessPoints = true, e.DisableMRAP = true), e;
};
var jh = /^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/;
var Wh = /(\d+\.){3}\d+/;
var Gh = /\.\./;
var Kh = (e) => jh.test(e) && !Wh.test(e) && !Gh.test(e);
var Xh = (e) => {
  const [t, r, s, , , n] = e.split(":"), i = t === "arn" && e.split(":").length >= 6, o = !!(i && r && s && n);
  if (i && !o)
    throw new Error(`Invalid ARN: ${e} was an invalid ARN.`);
  return o;
};
var Zh = (e, t, r) => {
  const s = async () => {
    const n = r[e] ?? r[t];
    return typeof n == "function" ? n() : n;
  };
  return e === "credentialScope" || t === "CredentialScope" ? async () => {
    const n = typeof r.credentials == "function" ? await r.credentials() : r.credentials;
    return (n == null ? void 0 : n.credentialScope) ?? (n == null ? void 0 : n.CredentialScope);
  } : e === "accountId" || t === "AccountId" ? async () => {
    const n = typeof r.credentials == "function" ? await r.credentials() : r.credentials;
    return (n == null ? void 0 : n.accountId) ?? (n == null ? void 0 : n.AccountId);
  } : e === "endpoint" || t === "endpoint" ? async () => {
    const n = await s();
    if (n && typeof n == "object") {
      if ("url" in n)
        return n.url.href;
      if ("hostname" in n) {
        const { protocol: i, hostname: o, port: a, path: c } = n;
        return `${i}//${o}${a ? ":" + a : ""}${c}`;
      }
    }
    return n;
  } : s;
};
var ca = async (e) => {
};
function Qh(e) {
  const t = {};
  if (e = e.replace(/^\?/, ""), e)
    for (const r of e.split("&")) {
      let [s, n = null] = r.split("=");
      s = decodeURIComponent(s), n && (n = decodeURIComponent(n)), s in t ? Array.isArray(t[s]) ? t[s].push(n) : t[s] = [t[s], n] : t[s] = n;
    }
  return t;
}
var Yt = (e) => {
  if (typeof e == "string")
    return Yt(new URL(e));
  const { hostname: t, pathname: r, port: s, protocol: n, search: i } = e;
  let o;
  return i && (o = Qh(i)), {
    hostname: t,
    port: s ? parseInt(s) : void 0,
    protocol: n,
    path: r,
    query: o
  };
};
var ua = (e) => typeof e == "object" ? "url" in e ? Yt(e.url) : e : Yt(e);
var Yh = async (e, t, r, s) => {
  if (!r.endpoint) {
    let o;
    r.serviceConfiguredEndpoint ? o = await r.serviceConfiguredEndpoint() : o = await ca(r.serviceId), o && (r.endpoint = () => Promise.resolve(ua(o)));
  }
  const n = await da(e, t, r);
  if (typeof r.endpointProvider != "function")
    throw new Error("config.endpointProvider is not set.");
  return r.endpointProvider(n, s);
};
var da = async (e, t, r) => {
  var i;
  const s = {}, n = ((i = t == null ? void 0 : t.getEndpointParameterInstructions) == null ? void 0 : i.call(t)) || {};
  for (const [o, a] of Object.entries(n))
    switch (a.type) {
      case "staticContextParams":
        s[o] = a.value;
        break;
      case "contextParams":
        s[o] = e[a.name];
        break;
      case "clientContextParams":
      case "builtInParams":
        s[o] = await Zh(a.name, o, r)();
        break;
      case "operationContextParams":
        s[o] = a.get(e);
        break;
      default:
        throw new Error("Unrecognized endpoint parameter instruction: " + JSON.stringify(a));
    }
  return Object.keys(n).length === 0 && Object.assign(s, r), String(r.serviceId).toLowerCase() === "s3" && await Vh(s), s;
};
var Jh = ({ config: e, instructions: t }) => (r, s) => async (n) => {
  var a, c, u;
  e.endpoint && su(s, "ENDPOINT_OVERRIDE", "N");
  const i = await Yh(n.input, {
    getEndpointParameterInstructions() {
      return t;
    }
  }, { ...e }, s);
  s.endpointV2 = i, s.authSchemes = (a = i.properties) == null ? void 0 : a.authSchemes;
  const o = (c = s.authSchemes) == null ? void 0 : c[0];
  if (o) {
    s.signing_region = o.signingRegion, s.signing_service = o.signingName;
    const l = pt(s), f = (u = l == null ? void 0 : l.selectedHttpAuthScheme) == null ? void 0 : u.httpAuthOption;
    f && (f.signingProperties = Object.assign(f.signingProperties || {}, {
      signing_region: o.signingRegion,
      signingRegion: o.signingRegion,
      signing_service: o.signingName,
      signingName: o.signingName,
      signingRegionSet: o.signingRegionSet
    }, o.properties));
  }
  return r({
    ...n
  });
};
var ep = {
  step: "serialize",
  tags: ["ENDPOINT_PARAMETERS", "ENDPOINT_V2", "ENDPOINT"],
  name: "endpointV2Middleware",
  override: true,
  relation: "before",
  toMiddleware: Es.name
};
var la = (e, t) => ({
  applyToStack: (r) => {
    r.addRelativeTo(Jh({
      config: e,
      instructions: t
    }), ep);
  }
});
var tp = (e) => {
  const t = e.tls ?? true, { endpoint: r } = e, s = r != null ? async () => ua(await Pe(r)()) : void 0, i = {
    ...e,
    endpoint: s,
    tls: t,
    isCustomEndpoint: !!r,
    useDualstackEndpoint: Pe(e.useDualstackEndpoint ?? false),
    useFipsEndpoint: Pe(e.useFipsEndpoint ?? false)
  };
  let o;
  return i.serviceConfiguredEndpoint = async () => (e.serviceId && !o && (o = ca(e.serviceId)), o), i;
};
var dt;
(function(e) {
  e.STANDARD = "standard", e.ADAPTIVE = "adaptive";
})(dt || (dt = {}));
var Ms = 3;
var rp = dt.STANDARD;
var sp = [
  "BandwidthLimitExceeded",
  "EC2ThrottledException",
  "LimitExceededException",
  "PriorRequestNotComplete",
  "ProvisionedThroughputExceededException",
  "RequestLimitExceeded",
  "RequestThrottled",
  "RequestThrottledException",
  "SlowDown",
  "ThrottledException",
  "Throttling",
  "ThrottlingException",
  "TooManyRequestsException",
  "TransactionInProgressException"
];
var np = ["TimeoutError", "RequestTimeout", "RequestTimeoutException"];
var ip = [500, 502, 503, 504];
var op = ["ECONNRESET", "ECONNREFUSED", "EPIPE", "ETIMEDOUT"];
var ap = (e) => {
  var t;
  return (t = e.$metadata) == null ? void 0 : t.clockSkewCorrected;
};
var fa = (e) => {
  var t, r;
  return ((t = e.$metadata) == null ? void 0 : t.httpStatusCode) === 429 || sp.includes(e.name) || ((r = e.$retryable) == null ? void 0 : r.throttling) == true;
};
var Os = (e, t = 0) => {
  var r;
  return ap(e) || np.includes(e.name) || op.includes((e == null ? void 0 : e.code) || "") || ip.includes(((r = e.$metadata) == null ? void 0 : r.httpStatusCode) || 0) || e.cause !== void 0 && t <= 10 && Os(e.cause, t + 1);
};
var cp = (e) => {
  var t;
  if (((t = e.$metadata) == null ? void 0 : t.httpStatusCode) !== void 0) {
    const r = e.$metadata.httpStatusCode;
    return 500 <= r && r <= 599 && !Os(e);
  }
  return false;
};
var ur = class _ur {
  constructor(t) {
    this.currentCapacity = 0, this.enabled = false, this.lastMaxRate = 0, this.measuredTxRate = 0, this.requestCount = 0, this.lastTimestamp = 0, this.timeWindow = 0, this.beta = (t == null ? void 0 : t.beta) ?? 0.7, this.minCapacity = (t == null ? void 0 : t.minCapacity) ?? 1, this.minFillRate = (t == null ? void 0 : t.minFillRate) ?? 0.5, this.scaleConstant = (t == null ? void 0 : t.scaleConstant) ?? 0.4, this.smooth = (t == null ? void 0 : t.smooth) ?? 0.8;
    const r = this.getCurrentTimeInSeconds();
    this.lastThrottleTime = r, this.lastTxRateBucket = Math.floor(this.getCurrentTimeInSeconds()), this.fillRate = this.minFillRate, this.maxCapacity = this.minCapacity;
  }
  getCurrentTimeInSeconds() {
    return Date.now() / 1e3;
  }
  async getSendToken() {
    return this.acquireTokenBucket(1);
  }
  async acquireTokenBucket(t) {
    if (this.enabled) {
      if (this.refillTokenBucket(), t > this.currentCapacity) {
        const r = (t - this.currentCapacity) / this.fillRate * 1e3;
        await new Promise((s) => _ur.setTimeoutFn(s, r));
      }
      this.currentCapacity = this.currentCapacity - t;
    }
  }
  refillTokenBucket() {
    const t = this.getCurrentTimeInSeconds();
    if (!this.lastTimestamp) {
      this.lastTimestamp = t;
      return;
    }
    const r = (t - this.lastTimestamp) * this.fillRate;
    this.currentCapacity = Math.min(this.maxCapacity, this.currentCapacity + r), this.lastTimestamp = t;
  }
  updateClientSendingRate(t) {
    let r;
    if (this.updateMeasuredRate(), fa(t)) {
      const n = this.enabled ? Math.min(this.measuredTxRate, this.fillRate) : this.measuredTxRate;
      this.lastMaxRate = n, this.calculateTimeWindow(), this.lastThrottleTime = this.getCurrentTimeInSeconds(), r = this.cubicThrottle(n), this.enableTokenBucket();
    } else
      this.calculateTimeWindow(), r = this.cubicSuccess(this.getCurrentTimeInSeconds());
    const s = Math.min(r, 2 * this.measuredTxRate);
    this.updateTokenBucketRate(s);
  }
  calculateTimeWindow() {
    this.timeWindow = this.getPrecise(Math.pow(this.lastMaxRate * (1 - this.beta) / this.scaleConstant, 1 / 3));
  }
  cubicThrottle(t) {
    return this.getPrecise(t * this.beta);
  }
  cubicSuccess(t) {
    return this.getPrecise(this.scaleConstant * Math.pow(t - this.lastThrottleTime - this.timeWindow, 3) + this.lastMaxRate);
  }
  enableTokenBucket() {
    this.enabled = true;
  }
  updateTokenBucketRate(t) {
    this.refillTokenBucket(), this.fillRate = Math.max(t, this.minFillRate), this.maxCapacity = Math.max(t, this.minCapacity), this.currentCapacity = Math.min(this.currentCapacity, this.maxCapacity);
  }
  updateMeasuredRate() {
    const t = this.getCurrentTimeInSeconds(), r = Math.floor(t * 2) / 2;
    if (this.requestCount++, r > this.lastTxRateBucket) {
      const s = this.requestCount / (r - this.lastTxRateBucket);
      this.measuredTxRate = this.getPrecise(s * this.smooth + this.measuredTxRate * (1 - this.smooth)), this.requestCount = 0, this.lastTxRateBucket = r;
    }
  }
  getPrecise(t) {
    return parseFloat(t.toFixed(8));
  }
};
ur.setTimeoutFn = setTimeout;
var ms = 100;
var ha = 20 * 1e3;
var up = 500;
var zn = 500;
var dp = 5;
var lp = 10;
var fp = 1;
var hp = "amz-sdk-invocation-id";
var pp = "amz-sdk-request";
var gp = () => {
  let e = ms;
  return {
    computeNextBackoffDelay: (s) => Math.floor(Math.min(ha, Math.random() * 2 ** s * e)),
    setDelayBase: (s) => {
      e = s;
    }
  };
};
var qn = ({ retryDelay: e, retryCount: t, retryCost: r }) => ({
  getRetryCount: () => t,
  getRetryDelay: () => Math.min(ha, e),
  getRetryCost: () => r
});
var pa = class {
  constructor(t) {
    this.maxAttempts = t, this.mode = dt.STANDARD, this.capacity = zn, this.retryBackoffStrategy = gp(), this.maxAttemptsProvider = typeof t == "function" ? t : async () => t;
  }
  async acquireInitialRetryToken(t) {
    return qn({
      retryDelay: ms,
      retryCount: 0
    });
  }
  async refreshRetryTokenForRetry(t, r) {
    const s = await this.getMaxAttempts();
    if (this.shouldRetry(t, r, s)) {
      const n = r.errorType;
      this.retryBackoffStrategy.setDelayBase(n === "THROTTLING" ? up : ms);
      const i = this.retryBackoffStrategy.computeNextBackoffDelay(t.getRetryCount()), o = r.retryAfterHint ? Math.max(r.retryAfterHint.getTime() - Date.now() || 0, i) : i, a = this.getCapacityCost(n);
      return this.capacity -= a, qn({
        retryDelay: o,
        retryCount: t.getRetryCount() + 1,
        retryCost: a
      });
    }
    throw new Error("No retry token available");
  }
  recordSuccess(t) {
    this.capacity = Math.max(zn, this.capacity + (t.getRetryCost() ?? fp));
  }
  getCapacity() {
    return this.capacity;
  }
  async getMaxAttempts() {
    try {
      return await this.maxAttemptsProvider();
    } catch {
      return Ms;
    }
  }
  shouldRetry(t, r, s) {
    return t.getRetryCount() + 1 < s && this.capacity >= this.getCapacityCost(r.errorType) && this.isRetryableError(r.errorType);
  }
  getCapacityCost(t) {
    return t === "TRANSIENT" ? lp : dp;
  }
  isRetryableError(t) {
    return t === "THROTTLING" || t === "TRANSIENT";
  }
};
var mp = class {
  constructor(t, r) {
    this.maxAttemptsProvider = t, this.mode = dt.ADAPTIVE;
    const { rateLimiter: s } = r ?? {};
    this.rateLimiter = s ?? new ur(), this.standardRetryStrategy = new pa(t);
  }
  async acquireInitialRetryToken(t) {
    return await this.rateLimiter.getSendToken(), this.standardRetryStrategy.acquireInitialRetryToken(t);
  }
  async refreshRetryTokenForRetry(t, r) {
    return this.rateLimiter.updateClientSendingRate(r), this.standardRetryStrategy.refreshRetryTokenForRetry(t, r);
  }
  recordSuccess(t) {
    this.rateLimiter.updateClientSendingRate({}), this.standardRetryStrategy.recordSuccess(t);
  }
};
var Dt;
var yp = new Uint8Array(16);
function wp() {
  if (!Dt && (Dt = typeof crypto < "u" && crypto.getRandomValues && crypto.getRandomValues.bind(crypto), !Dt))
    throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
  return Dt(yp);
}
var oe = [];
for (let e = 0; e < 256; ++e)
  oe.push((e + 256).toString(16).slice(1));
function bp(e, t = 0) {
  return oe[e[t + 0]] + oe[e[t + 1]] + oe[e[t + 2]] + oe[e[t + 3]] + "-" + oe[e[t + 4]] + oe[e[t + 5]] + "-" + oe[e[t + 6]] + oe[e[t + 7]] + "-" + oe[e[t + 8]] + oe[e[t + 9]] + "-" + oe[e[t + 10]] + oe[e[t + 11]] + oe[e[t + 12]] + oe[e[t + 13]] + oe[e[t + 14]] + oe[e[t + 15]];
}
var Ep = typeof crypto < "u" && crypto.randomUUID && crypto.randomUUID.bind(crypto);
var Vn = {
  randomUUID: Ep
};
function xp(e, t, r) {
  if (Vn.randomUUID && !t && !e)
    return Vn.randomUUID();
  e = e || {};
  const s = e.random || (e.rng || wp)();
  if (s[6] = s[6] & 15 | 64, s[8] = s[8] & 63 | 128, t) {
    r = r || 0;
    for (let n = 0; n < 16; ++n)
      t[r + n] = s[n];
    return t;
  }
  return bp(s);
}
var Sp = (e) => e instanceof Error ? e : e instanceof Object ? Object.assign(new Error(), e) : typeof e == "string" ? new Error(e) : new Error(`AWS SDK error wrapper for ${e}`);
var Ap = (e) => {
  const { retryStrategy: t } = e, r = Pe(e.maxAttempts ?? Ms);
  return {
    ...e,
    maxAttempts: r,
    retryStrategy: async () => t || (await Pe(e.retryMode)() === dt.ADAPTIVE ? new mp(r) : new pa(r))
  };
};
var Cp = (e) => (e == null ? void 0 : e.body) instanceof ReadableStream;
var vp = (e) => (t, r) => async (s) => {
  var o;
  let n = await e.retryStrategy();
  const i = await e.maxAttempts();
  if (Rp(n)) {
    n = n;
    let a = await n.acquireInitialRetryToken(r.partition_id), c = new Error(), u = 0, l = 0;
    const { request: f } = s, p = J.isInstance(f);
    for (p && (f.headers[hp] = xp()); ; )
      try {
        p && (f.headers[pp] = `attempt=${u + 1}; max=${i}`);
        const { response: g, output: E } = await t(s);
        return n.recordSuccess(a), E.$metadata.attempts = u + 1, E.$metadata.totalRetryDelay = l, { response: g, output: E };
      } catch (g) {
        const E = kp(g);
        if (c = Sp(g), p && Cp(f))
          throw (o = r.logger instanceof vs ? console : r.logger) == null || o.warn("An error was encountered in a non-retryable streaming request."), c;
        try {
          a = await n.refreshRetryTokenForRetry(a, E);
        } catch {
          throw c.$metadata || (c.$metadata = {}), c.$metadata.attempts = u + 1, c.$metadata.totalRetryDelay = l, c;
        }
        u = a.getRetryCount();
        const k = a.getRetryDelay();
        l += k, await new Promise((S) => setTimeout(S, k));
      }
  } else
    return n = n, n != null && n.mode && (r.userAgent = [...r.userAgent || [], ["cfg/retry-mode", n.mode]]), n.retry(t, s);
};
var Rp = (e) => typeof e.acquireInitialRetryToken < "u" && typeof e.refreshRetryTokenForRetry < "u" && typeof e.recordSuccess < "u";
var kp = (e) => {
  const t = {
    error: e,
    errorType: Tp(e)
  }, r = Ip(e.$response);
  return r && (t.retryAfterHint = r), t;
};
var Tp = (e) => fa(e) ? "THROTTLING" : Os(e) ? "TRANSIENT" : cp(e) ? "SERVER_ERROR" : "CLIENT_ERROR";
var Bp = {
  name: "retryMiddleware",
  tags: ["RETRY"],
  step: "finalizeRequest",
  priority: "high",
  override: true
};
var Np = (e) => ({
  applyToStack: (t) => {
    t.add(vp(e), Bp);
  }
});
var Ip = (e) => {
  if (!Nt.isInstance(e))
    return;
  const t = Object.keys(e.headers).find((i) => i.toLowerCase() === "retry-after");
  if (!t)
    return;
  const r = e.headers[t], s = Number(r);
  return Number.isNaN(s) ? new Date(r) : new Date(s * 1e3);
};
var Pp = {
  CrtSignerV4: null
};
var _p = class {
  constructor(t) {
    _(this, "sigv4aSigner");
    _(this, "sigv4Signer");
    _(this, "signerOptions");
    this.sigv4Signer = new If(t), this.signerOptions = t;
  }
  async sign(t, r = {}) {
    if (r.signingRegion === "*") {
      if (this.signerOptions.runtime !== "node")
        throw new Error("This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js");
      return this.getSigv4aSigner().sign(t, r);
    }
    return this.sigv4Signer.sign(t, r);
  }
  async signWithCredentials(t, r, s = {}) {
    if (s.signingRegion === "*") {
      if (this.signerOptions.runtime !== "node")
        throw new Error("This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js");
      return this.getSigv4aSigner().signWithCredentials(t, r, s);
    }
    return this.sigv4Signer.signWithCredentials(t, r, s);
  }
  async presign(t, r = {}) {
    if (r.signingRegion === "*") {
      if (this.signerOptions.runtime !== "node")
        throw new Error("This request requires signing with SigV4Asymmetric algorithm. It's only available in Node.js");
      return this.getSigv4aSigner().presign(t, r);
    }
    return this.sigv4Signer.presign(t, r);
  }
  async presignWithCredentials(t, r, s = {}) {
    if (s.signingRegion === "*")
      throw new Error("Method presignWithCredentials is not supported for [signingRegion=*].");
    return this.sigv4Signer.presignWithCredentials(t, r, s);
  }
  getSigv4aSigner() {
    if (!this.sigv4aSigner) {
      let t = null;
      try {
        if (t = Pp.CrtSignerV4, typeof t != "function")
          throw new Error();
      } catch (r) {
        throw r.message = `${r.message}
Please check whether you have installed the "@aws-sdk/signature-v4-crt" package explicitly. 
You must also register the package by calling [require("@aws-sdk/signature-v4-crt");] or an ESM equivalent such as [import "@aws-sdk/signature-v4-crt";]. 
For more information please go to https://github.com/aws/aws-sdk-js-v3#functionality-requiring-aws-common-runtime-crt`, r;
      }
      this.sigv4aSigner = new t({
        ...this.signerOptions,
        signingAlgorithm: 1
      });
    }
    return this.sigv4aSigner;
  }
};
var Fs = "required";
var d = "type";
var h = "conditions";
var m = "fn";
var y = "argv";
var T = "ref";
var U = "assign";
var A = "url";
var C = "properties";
var ga = "backend";
var Ie = "authSchemes";
var Ce = "disableDoubleEncoding";
var ve = "signingName";
var Me = "signingRegion";
var v = "headers";
var Ds = "signingRegionSet";
var Mp = 6;
var Op = false;
var Te = true;
var ke = "isSet";
var ie = "booleanEquals";
var R = "error";
var Jt = "aws.partition";
var D = "stringEquals";
var G = "getAttr";
var fe = "name";
var le = "substring";
var jn = "bucketSuffix";
var ma = "parseURL";
var Wn = "{url#scheme}://{url#authority}/{uri_encoded_bucket}{url#path}";
var x = "endpoint";
var b = "tree";
var ya = "aws.isVirtualHostableS3Bucket";
var zt = "{url#scheme}://{Bucket}.{url#authority}{url#path}";
var _e = "not";
var qt = "{url#scheme}://{url#authority}{url#path}";
var wa = "hardwareType";
var ba = "regionPrefix";
var Gn = "bucketAliasSuffix";
var ys = "outpostId";
var tt = "isValidHostLabel";
var Us = "sigv4a";
var _t = "s3-outposts";
var lt = "s3";
var Ea = "{url#scheme}://{url#authority}{url#normalizedPath}{Bucket}";
var xa = "https://{Bucket}.s3-accelerate.{partitionResult#dnsSuffix}";
var Kn = "https://{Bucket}.s3.{partitionResult#dnsSuffix}";
var Sa = "aws.parseArn";
var Aa = "bucketArn";
var Ca = "arnType";
var er = "";
var $s = "s3-object-lambda";
var va = "accesspoint";
var Ls = "accessPointName";
var Xn = "{url#scheme}://{accessPointName}-{bucketArn#accountId}.{url#authority}{url#path}";
var Zn = "mrapPartition";
var Qn = "outpostType";
var Yn = "arnPrefix";
var Ra = "{url#scheme}://{url#authority}{url#normalizedPath}{uri_encoded_bucket}";
var Jn = "https://s3.{partitionResult#dnsSuffix}/{uri_encoded_bucket}";
var ei = "https://s3.{partitionResult#dnsSuffix}";
var rt = { [Fs]: false, [d]: "String" };
var st = { [Fs]: true, default: false, [d]: "Boolean" };
var bt2 = { [Fs]: false, [d]: "Boolean" };
var Be = { [m]: ie, [y]: [{ [T]: "Accelerate" }, true] };
var q = { [m]: ie, [y]: [{ [T]: "UseFIPS" }, true] };
var z = { [m]: ie, [y]: [{ [T]: "UseDualStack" }, true] };
var Z = { [m]: ke, [y]: [{ [T]: "Endpoint" }] };
var ka = { [m]: Jt, [y]: [{ [T]: "Region" }], [U]: "partitionResult" };
var ti = { [m]: D, [y]: [{ [m]: G, [y]: [{ [T]: "partitionResult" }, fe] }, "aws-cn"] };
var tr = { [m]: ke, [y]: [{ [T]: "Bucket" }] };
var K = { [T]: "Bucket" };
var me = { [m]: ma, [y]: [{ [T]: "Endpoint" }], [U]: "url" };
var Vt = { [m]: ie, [y]: [{ [m]: G, [y]: [{ [T]: "url" }, "isIp"] }, true] };
var Ta = { [T]: "url" };
var Ba = { [m]: "uriEncode", [y]: [K], [U]: "uri_encoded_bucket" };
var Oe = { [ga]: "S3Express", [Ie]: [{ [Ce]: true, [fe]: "sigv4", [ve]: "s3express", [Me]: "{Region}" }] };
var B = {};
var Na = { [m]: ya, [y]: [K, false] };
var Tr = { [R]: "S3Express bucket name is not a valid virtual hostable name.", [d]: R };
var rr = { [ga]: "S3Express", [Ie]: [{ [Ce]: true, [fe]: "sigv4-s3express", [ve]: "s3express", [Me]: "{Region}" }] };
var ri = { [m]: ke, [y]: [{ [T]: "UseS3ExpressControlEndpoint" }] };
var si = { [m]: ie, [y]: [{ [T]: "UseS3ExpressControlEndpoint" }, true] };
var I = { [m]: _e, [y]: [Z] };
var ni = { [R]: "Unrecognized S3Express bucket name format.", [d]: R };
var ii = { [m]: _e, [y]: [tr] };
var oi = { [T]: wa };
var ai = { [h]: [I], [R]: "Expected a endpoint to be specified but no endpoint was found", [d]: R };
var Ut = { [Ie]: [{ [Ce]: true, [fe]: Us, [ve]: _t, [Ds]: ["*"] }, { [Ce]: true, [fe]: "sigv4", [ve]: _t, [Me]: "{Region}" }] };
var Br = { [m]: ie, [y]: [{ [T]: "ForcePathStyle" }, false] };
var Fp = { [T]: "ForcePathStyle" };
var X = { [m]: ie, [y]: [{ [T]: "Accelerate" }, false] };
var te = { [m]: D, [y]: [{ [T]: "Region" }, "aws-global"] };
var re = { [Ie]: [{ [Ce]: true, [fe]: "sigv4", [ve]: lt, [Me]: "us-east-1" }] };
var F = { [m]: _e, [y]: [te] };
var se = { [m]: ie, [y]: [{ [T]: "UseGlobalEndpoint" }, true] };
var ci = { [A]: "https://{Bucket}.s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}", [C]: { [Ie]: [{ [Ce]: true, [fe]: "sigv4", [ve]: lt, [Me]: "{Region}" }] }, [v]: {} };
var Y = { [Ie]: [{ [Ce]: true, [fe]: "sigv4", [ve]: lt, [Me]: "{Region}" }] };
var ne = { [m]: ie, [y]: [{ [T]: "UseGlobalEndpoint" }, false] };
var O = { [m]: ie, [y]: [{ [T]: "UseDualStack" }, false] };
var ui = { [A]: "https://{Bucket}.s3-fips.{Region}.{partitionResult#dnsSuffix}", [C]: Y, [v]: {} };
var M = { [m]: ie, [y]: [{ [T]: "UseFIPS" }, false] };
var di = { [A]: "https://{Bucket}.s3-accelerate.dualstack.{partitionResult#dnsSuffix}", [C]: Y, [v]: {} };
var li = { [A]: "https://{Bucket}.s3.dualstack.{Region}.{partitionResult#dnsSuffix}", [C]: Y, [v]: {} };
var Nr = { [m]: ie, [y]: [{ [m]: G, [y]: [Ta, "isIp"] }, false] };
var Ir = { [A]: Ea, [C]: Y, [v]: {} };
var ws = { [A]: zt, [C]: Y, [v]: {} };
var fi = { [x]: ws, [d]: x };
var Pr = { [A]: xa, [C]: Y, [v]: {} };
var hi = { [A]: "https://{Bucket}.s3.{Region}.{partitionResult#dnsSuffix}", [C]: Y, [v]: {} };
var $t = { [R]: "Invalid region: region was not a valid DNS name.", [d]: R };
var xe = { [T]: Aa };
var Ia = { [T]: Ca };
var _r = { [m]: G, [y]: [xe, "service"] };
var Hs = { [T]: Ls };
var pi = { [h]: [z], [R]: "S3 Object Lambda does not support Dual-stack", [d]: R };
var gi = { [h]: [Be], [R]: "S3 Object Lambda does not support S3 Accelerate", [d]: R };
var mi = { [h]: [{ [m]: ke, [y]: [{ [T]: "DisableAccessPoints" }] }, { [m]: ie, [y]: [{ [T]: "DisableAccessPoints" }, true] }], [R]: "Access points are not supported for this operation", [d]: R };
var Mr = { [h]: [{ [m]: ke, [y]: [{ [T]: "UseArnRegion" }] }, { [m]: ie, [y]: [{ [T]: "UseArnRegion" }, false] }, { [m]: _e, [y]: [{ [m]: D, [y]: [{ [m]: G, [y]: [xe, "region"] }, "{Region}"] }] }], [R]: "Invalid configuration: region from ARN `{bucketArn#region}` does not match client region `{Region}` and UseArnRegion is `false`", [d]: R };
var Pa = { [m]: G, [y]: [{ [T]: "bucketPartition" }, fe] };
var _a = { [m]: G, [y]: [xe, "accountId"] };
var Or = { [Ie]: [{ [Ce]: true, [fe]: "sigv4", [ve]: $s, [Me]: "{bucketArn#region}" }] };
var yi = { [R]: "Invalid ARN: The access point name may only contain a-z, A-Z, 0-9 and `-`. Found: `{accessPointName}`", [d]: R };
var Fr = { [R]: "Invalid ARN: The account id may only contain a-z, A-Z, 0-9 and `-`. Found: `{bucketArn#accountId}`", [d]: R };
var Dr = { [R]: "Invalid region in ARN: `{bucketArn#region}` (invalid DNS name)", [d]: R };
var Ur = { [R]: "Client was configured for partition `{partitionResult#name}` but ARN (`{Bucket}`) has `{bucketPartition#name}`", [d]: R };
var wi = { [R]: "Invalid ARN: The ARN may only contain a single resource component after `accesspoint`.", [d]: R };
var bi = { [R]: "Invalid ARN: Expected a resource of the format `accesspoint:<accesspoint name>` but no name was provided", [d]: R };
var Et = { [Ie]: [{ [Ce]: true, [fe]: "sigv4", [ve]: lt, [Me]: "{bucketArn#region}" }] };
var Ei = { [Ie]: [{ [Ce]: true, [fe]: Us, [ve]: _t, [Ds]: ["*"] }, { [Ce]: true, [fe]: "sigv4", [ve]: _t, [Me]: "{bucketArn#region}" }] };
var xi = { [m]: Sa, [y]: [K] };
var Si = { [A]: "https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}", [C]: Y, [v]: {} };
var Ai = { [A]: "https://s3-fips.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}", [C]: Y, [v]: {} };
var Ci = { [A]: "https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}", [C]: Y, [v]: {} };
var $r = { [A]: Ra, [C]: Y, [v]: {} };
var vi = { [A]: "https://s3.{Region}.{partitionResult#dnsSuffix}/{uri_encoded_bucket}", [C]: Y, [v]: {} };
var Ri = { [T]: "UseObjectLambdaEndpoint" };
var Lr = { [Ie]: [{ [Ce]: true, [fe]: "sigv4", [ve]: $s, [Me]: "{Region}" }] };
var ki = { [A]: "https://s3-fips.dualstack.{Region}.{partitionResult#dnsSuffix}", [C]: Y, [v]: {} };
var Ti = { [A]: "https://s3-fips.{Region}.{partitionResult#dnsSuffix}", [C]: Y, [v]: {} };
var Bi = { [A]: "https://s3.dualstack.{Region}.{partitionResult#dnsSuffix}", [C]: Y, [v]: {} };
var Hr = { [A]: qt, [C]: Y, [v]: {} };
var Ni = { [A]: "https://s3.{Region}.{partitionResult#dnsSuffix}", [C]: Y, [v]: {} };
var zr = [{ [T]: "Region" }];
var Dp = [{ [T]: "Endpoint" }];
var Up = [K];
var qr = [z];
var Lt = [Be];
var Xe = [Z, me];
var Ii = [{ [m]: ke, [y]: [{ [T]: "DisableS3ExpressSessionAuth" }] }, { [m]: ie, [y]: [{ [T]: "DisableS3ExpressSessionAuth" }, true] }];
var Pi = [Vt];
var Vr = [Ba];
var jr = [Na];
var nt = [q];
var _i = [{ [m]: le, [y]: [K, 6, 14, true], [U]: "s3expressAvailabilityZoneId" }, { [m]: le, [y]: [K, 14, 16, true], [U]: "s3expressAvailabilityZoneDelim" }, { [m]: D, [y]: [{ [T]: "s3expressAvailabilityZoneDelim" }, "--"] }];
var xt = [{ [h]: [q], [x]: { [A]: "https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.amazonaws.com", [C]: Oe, [v]: {} }, [d]: x }, { [x]: { [A]: "https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.amazonaws.com", [C]: Oe, [v]: {} }, [d]: x }];
var Mi = [{ [m]: le, [y]: [K, 6, 15, true], [U]: "s3expressAvailabilityZoneId" }, { [m]: le, [y]: [K, 15, 17, true], [U]: "s3expressAvailabilityZoneDelim" }, { [m]: D, [y]: [{ [T]: "s3expressAvailabilityZoneDelim" }, "--"] }];
var Oi = [{ [m]: le, [y]: [K, 6, 19, true], [U]: "s3expressAvailabilityZoneId" }, { [m]: le, [y]: [K, 19, 21, true], [U]: "s3expressAvailabilityZoneDelim" }, { [m]: D, [y]: [{ [T]: "s3expressAvailabilityZoneDelim" }, "--"] }];
var Fi = [{ [m]: le, [y]: [K, 6, 20, true], [U]: "s3expressAvailabilityZoneId" }, { [m]: le, [y]: [K, 20, 22, true], [U]: "s3expressAvailabilityZoneDelim" }, { [m]: D, [y]: [{ [T]: "s3expressAvailabilityZoneDelim" }, "--"] }];
var Di = [{ [m]: le, [y]: [K, 6, 26, true], [U]: "s3expressAvailabilityZoneId" }, { [m]: le, [y]: [K, 26, 28, true], [U]: "s3expressAvailabilityZoneDelim" }, { [m]: D, [y]: [{ [T]: "s3expressAvailabilityZoneDelim" }, "--"] }];
var St = [{ [h]: [q], [x]: { [A]: "https://{Bucket}.s3express-fips-{s3expressAvailabilityZoneId}.{Region}.amazonaws.com", [C]: rr, [v]: {} }, [d]: x }, { [x]: { [A]: "https://{Bucket}.s3express-{s3expressAvailabilityZoneId}.{Region}.amazonaws.com", [C]: rr, [v]: {} }, [d]: x }];
var $p = [tr];
var Ui = [{ [m]: tt, [y]: [{ [T]: ys }, false] }];
var $i = [{ [m]: D, [y]: [{ [T]: ba }, "beta"] }];
var Lp = ["*"];
var De = [ka];
var Li = [{ [m]: tt, [y]: [{ [T]: "Region" }, false] }];
var Ue = [{ [m]: D, [y]: [{ [T]: "Region" }, "us-east-1"] }];
var Wr = [{ [m]: D, [y]: [Ia, va] }];
var Hi = [{ [m]: G, [y]: [xe, "resourceId[1]"], [U]: Ls }, { [m]: _e, [y]: [{ [m]: D, [y]: [Hs, er] }] }];
var Hp = [xe, "resourceId[1]"];
var Gr = [{ [m]: _e, [y]: [{ [m]: D, [y]: [{ [m]: G, [y]: [xe, "region"] }, er] }] }];
var zi = [{ [m]: _e, [y]: [{ [m]: ke, [y]: [{ [m]: G, [y]: [xe, "resourceId[2]"] }] }] }];
var zp = [xe, "resourceId[2]"];
var Kr = [{ [m]: Jt, [y]: [{ [m]: G, [y]: [xe, "region"] }], [U]: "bucketPartition" }];
var qi = [{ [m]: D, [y]: [Pa, { [m]: G, [y]: [{ [T]: "partitionResult" }, fe] }] }];
var Xr = [{ [m]: tt, [y]: [{ [m]: G, [y]: [xe, "region"] }, true] }];
var Zr = [{ [m]: tt, [y]: [_a, false] }];
var Vi = [{ [m]: tt, [y]: [Hs, false] }];
var ji = [{ [m]: tt, [y]: [{ [T]: "Region" }, true] }];
var qp = { version: "1.0", parameters: { Bucket: rt, Region: rt, UseFIPS: st, UseDualStack: st, Endpoint: rt, ForcePathStyle: st, Accelerate: st, UseGlobalEndpoint: st, UseObjectLambdaEndpoint: bt2, Key: rt, Prefix: rt, CopySource: rt, DisableAccessPoints: bt2, DisableMultiRegionAccessPoints: st, UseArnRegion: bt2, UseS3ExpressControlEndpoint: bt2, DisableS3ExpressSessionAuth: bt2 }, rules: [{ [h]: [{ [m]: ke, [y]: zr }], rules: [{ [h]: [Be, q], error: "Accelerate cannot be used with FIPS", [d]: R }, { [h]: [z, Z], error: "Cannot set dual-stack in combination with a custom endpoint.", [d]: R }, { [h]: [Z, q], error: "A custom endpoint cannot be combined with FIPS", [d]: R }, { [h]: [Z, Be], error: "A custom endpoint cannot be combined with S3 Accelerate", [d]: R }, { [h]: [q, ka, ti], error: "Partition does not support FIPS", [d]: R }, { [h]: [tr, { [m]: le, [y]: [K, 0, Mp, Te], [U]: jn }, { [m]: D, [y]: [{ [T]: jn }, "--x-s3"] }], rules: [{ [h]: qr, error: "S3Express does not support Dual-stack.", [d]: R }, { [h]: Lt, error: "S3Express does not support S3 Accelerate.", [d]: R }, { [h]: Xe, rules: [{ [h]: Ii, rules: [{ [h]: Pi, rules: [{ [h]: Vr, rules: [{ endpoint: { [A]: Wn, [C]: Oe, [v]: B }, [d]: x }], [d]: b }], [d]: b }, { [h]: jr, rules: [{ endpoint: { [A]: zt, [C]: Oe, [v]: B }, [d]: x }], [d]: b }, Tr], [d]: b }, { [h]: Pi, rules: [{ [h]: Vr, rules: [{ endpoint: { [A]: Wn, [C]: rr, [v]: B }, [d]: x }], [d]: b }], [d]: b }, { [h]: jr, rules: [{ endpoint: { [A]: zt, [C]: rr, [v]: B }, [d]: x }], [d]: b }, Tr], [d]: b }, { [h]: [ri, si], rules: [{ [h]: [Ba, I], rules: [{ [h]: nt, endpoint: { [A]: "https://s3express-control-fips.{Region}.amazonaws.com/{uri_encoded_bucket}", [C]: Oe, [v]: B }, [d]: x }, { endpoint: { [A]: "https://s3express-control.{Region}.amazonaws.com/{uri_encoded_bucket}", [C]: Oe, [v]: B }, [d]: x }], [d]: b }], [d]: b }, { [h]: jr, rules: [{ [h]: Ii, rules: [{ [h]: _i, rules: xt, [d]: b }, { [h]: Mi, rules: xt, [d]: b }, { [h]: Oi, rules: xt, [d]: b }, { [h]: Fi, rules: xt, [d]: b }, { [h]: Di, rules: xt, [d]: b }, ni], [d]: b }, { [h]: _i, rules: St, [d]: b }, { [h]: Mi, rules: St, [d]: b }, { [h]: Oi, rules: St, [d]: b }, { [h]: Fi, rules: St, [d]: b }, { [h]: Di, rules: St, [d]: b }, ni], [d]: b }, Tr], [d]: b }, { [h]: [ii, ri, si], rules: [{ [h]: Xe, endpoint: { [A]: qt, [C]: Oe, [v]: B }, [d]: x }, { [h]: nt, endpoint: { [A]: "https://s3express-control-fips.{Region}.amazonaws.com", [C]: Oe, [v]: B }, [d]: x }, { endpoint: { [A]: "https://s3express-control.{Region}.amazonaws.com", [C]: Oe, [v]: B }, [d]: x }], [d]: b }, { [h]: [tr, { [m]: le, [y]: [K, 49, 50, Te], [U]: wa }, { [m]: le, [y]: [K, 8, 12, Te], [U]: ba }, { [m]: le, [y]: [K, 0, 7, Te], [U]: Gn }, { [m]: le, [y]: [K, 32, 49, Te], [U]: ys }, { [m]: Jt, [y]: zr, [U]: "regionPartition" }, { [m]: D, [y]: [{ [T]: Gn }, "--op-s3"] }], rules: [{ [h]: Ui, rules: [{ [h]: [{ [m]: D, [y]: [oi, "e"] }], rules: [{ [h]: $i, rules: [ai, { [h]: Xe, endpoint: { [A]: "https://{Bucket}.ec2.{url#authority}", [C]: Ut, [v]: B }, [d]: x }], [d]: b }, { endpoint: { [A]: "https://{Bucket}.ec2.s3-outposts.{Region}.{regionPartition#dnsSuffix}", [C]: Ut, [v]: B }, [d]: x }], [d]: b }, { [h]: [{ [m]: D, [y]: [oi, "o"] }], rules: [{ [h]: $i, rules: [ai, { [h]: Xe, endpoint: { [A]: "https://{Bucket}.op-{outpostId}.{url#authority}", [C]: Ut, [v]: B }, [d]: x }], [d]: b }, { endpoint: { [A]: "https://{Bucket}.op-{outpostId}.s3-outposts.{Region}.{regionPartition#dnsSuffix}", [C]: Ut, [v]: B }, [d]: x }], [d]: b }, { error: 'Unrecognized hardware type: "Expected hardware type o or e but got {hardwareType}"', [d]: R }], [d]: b }, { error: "Invalid ARN: The outpost Id must only contain a-z, A-Z, 0-9 and `-`.", [d]: R }], [d]: b }, { [h]: $p, rules: [{ [h]: [Z, { [m]: _e, [y]: [{ [m]: ke, [y]: [{ [m]: ma, [y]: Dp }] }] }], error: "Custom endpoint `{Endpoint}` was not a valid URI", [d]: R }, { [h]: [Br, Na], rules: [{ [h]: De, rules: [{ [h]: Li, rules: [{ [h]: [Be, ti], error: "S3 Accelerate cannot be used in this region", [d]: R }, { [h]: [z, q, X, I, te], endpoint: { [A]: "https://{Bucket}.s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}", [C]: re, [v]: B }, [d]: x }, { [h]: [z, q, X, I, F, se], rules: [{ endpoint: ci, [d]: x }], [d]: b }, { [h]: [z, q, X, I, F, ne], endpoint: ci, [d]: x }, { [h]: [O, q, X, I, te], endpoint: { [A]: "https://{Bucket}.s3-fips.us-east-1.{partitionResult#dnsSuffix}", [C]: re, [v]: B }, [d]: x }, { [h]: [O, q, X, I, F, se], rules: [{ endpoint: ui, [d]: x }], [d]: b }, { [h]: [O, q, X, I, F, ne], endpoint: ui, [d]: x }, { [h]: [z, M, Be, I, te], endpoint: { [A]: "https://{Bucket}.s3-accelerate.dualstack.us-east-1.{partitionResult#dnsSuffix}", [C]: re, [v]: B }, [d]: x }, { [h]: [z, M, Be, I, F, se], rules: [{ endpoint: di, [d]: x }], [d]: b }, { [h]: [z, M, Be, I, F, ne], endpoint: di, [d]: x }, { [h]: [z, M, X, I, te], endpoint: { [A]: "https://{Bucket}.s3.dualstack.us-east-1.{partitionResult#dnsSuffix}", [C]: re, [v]: B }, [d]: x }, { [h]: [z, M, X, I, F, se], rules: [{ endpoint: li, [d]: x }], [d]: b }, { [h]: [z, M, X, I, F, ne], endpoint: li, [d]: x }, { [h]: [O, M, X, Z, me, Vt, te], endpoint: { [A]: Ea, [C]: re, [v]: B }, [d]: x }, { [h]: [O, M, X, Z, me, Nr, te], endpoint: { [A]: zt, [C]: re, [v]: B }, [d]: x }, { [h]: [O, M, X, Z, me, Vt, F, se], rules: [{ [h]: Ue, endpoint: Ir, [d]: x }, { endpoint: Ir, [d]: x }], [d]: b }, { [h]: [O, M, X, Z, me, Nr, F, se], rules: [{ [h]: Ue, endpoint: ws, [d]: x }, fi], [d]: b }, { [h]: [O, M, X, Z, me, Vt, F, ne], endpoint: Ir, [d]: x }, { [h]: [O, M, X, Z, me, Nr, F, ne], endpoint: ws, [d]: x }, { [h]: [O, M, Be, I, te], endpoint: { [A]: xa, [C]: re, [v]: B }, [d]: x }, { [h]: [O, M, Be, I, F, se], rules: [{ [h]: Ue, endpoint: Pr, [d]: x }, { endpoint: Pr, [d]: x }], [d]: b }, { [h]: [O, M, Be, I, F, ne], endpoint: Pr, [d]: x }, { [h]: [O, M, X, I, te], endpoint: { [A]: Kn, [C]: re, [v]: B }, [d]: x }, { [h]: [O, M, X, I, F, se], rules: [{ [h]: Ue, endpoint: { [A]: Kn, [C]: Y, [v]: B }, [d]: x }, { endpoint: hi, [d]: x }], [d]: b }, { [h]: [O, M, X, I, F, ne], endpoint: hi, [d]: x }], [d]: b }, $t], [d]: b }], [d]: b }, { [h]: [Z, me, { [m]: D, [y]: [{ [m]: G, [y]: [Ta, "scheme"] }, "http"] }, { [m]: ya, [y]: [K, Te] }, Br, M, O, X], rules: [{ [h]: De, rules: [{ [h]: Li, rules: [fi], [d]: b }, $t], [d]: b }], [d]: b }, { [h]: [Br, { [m]: Sa, [y]: Up, [U]: Aa }], rules: [{ [h]: [{ [m]: G, [y]: [xe, "resourceId[0]"], [U]: Ca }, { [m]: _e, [y]: [{ [m]: D, [y]: [Ia, er] }] }], rules: [{ [h]: [{ [m]: D, [y]: [_r, $s] }], rules: [{ [h]: Wr, rules: [{ [h]: Hi, rules: [pi, gi, { [h]: Gr, rules: [mi, { [h]: zi, rules: [Mr, { [h]: Kr, rules: [{ [h]: De, rules: [{ [h]: qi, rules: [{ [h]: Xr, rules: [{ [h]: [{ [m]: D, [y]: [_a, er] }], error: "Invalid ARN: Missing account id", [d]: R }, { [h]: Zr, rules: [{ [h]: Vi, rules: [{ [h]: Xe, endpoint: { [A]: Xn, [C]: Or, [v]: B }, [d]: x }, { [h]: nt, endpoint: { [A]: "https://{accessPointName}-{bucketArn#accountId}.s3-object-lambda-fips.{bucketArn#region}.{bucketPartition#dnsSuffix}", [C]: Or, [v]: B }, [d]: x }, { endpoint: { [A]: "https://{accessPointName}-{bucketArn#accountId}.s3-object-lambda.{bucketArn#region}.{bucketPartition#dnsSuffix}", [C]: Or, [v]: B }, [d]: x }], [d]: b }, yi], [d]: b }, Fr], [d]: b }, Dr], [d]: b }, Ur], [d]: b }], [d]: b }], [d]: b }, wi], [d]: b }, { error: "Invalid ARN: bucket ARN is missing a region", [d]: R }], [d]: b }, bi], [d]: b }, { error: "Invalid ARN: Object Lambda ARNs only support `accesspoint` arn types, but found: `{arnType}`", [d]: R }], [d]: b }, { [h]: Wr, rules: [{ [h]: Hi, rules: [{ [h]: Gr, rules: [{ [h]: Wr, rules: [{ [h]: Gr, rules: [mi, { [h]: zi, rules: [Mr, { [h]: Kr, rules: [{ [h]: De, rules: [{ [h]: [{ [m]: D, [y]: [Pa, "{partitionResult#name}"] }], rules: [{ [h]: Xr, rules: [{ [h]: [{ [m]: D, [y]: [_r, lt] }], rules: [{ [h]: Zr, rules: [{ [h]: Vi, rules: [{ [h]: Lt, error: "Access Points do not support S3 Accelerate", [d]: R }, { [h]: [q, z], endpoint: { [A]: "https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint-fips.dualstack.{bucketArn#region}.{bucketPartition#dnsSuffix}", [C]: Et, [v]: B }, [d]: x }, { [h]: [q, O], endpoint: { [A]: "https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint-fips.{bucketArn#region}.{bucketPartition#dnsSuffix}", [C]: Et, [v]: B }, [d]: x }, { [h]: [M, z], endpoint: { [A]: "https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint.dualstack.{bucketArn#region}.{bucketPartition#dnsSuffix}", [C]: Et, [v]: B }, [d]: x }, { [h]: [M, O, Z, me], endpoint: { [A]: Xn, [C]: Et, [v]: B }, [d]: x }, { [h]: [M, O], endpoint: { [A]: "https://{accessPointName}-{bucketArn#accountId}.s3-accesspoint.{bucketArn#region}.{bucketPartition#dnsSuffix}", [C]: Et, [v]: B }, [d]: x }], [d]: b }, yi], [d]: b }, Fr], [d]: b }, { error: "Invalid ARN: The ARN was not for the S3 service, found: {bucketArn#service}", [d]: R }], [d]: b }, Dr], [d]: b }, Ur], [d]: b }], [d]: b }], [d]: b }, wi], [d]: b }], [d]: b }], [d]: b }, { [h]: [{ [m]: tt, [y]: [Hs, Te] }], rules: [{ [h]: qr, error: "S3 MRAP does not support dual-stack", [d]: R }, { [h]: nt, error: "S3 MRAP does not support FIPS", [d]: R }, { [h]: Lt, error: "S3 MRAP does not support S3 Accelerate", [d]: R }, { [h]: [{ [m]: ie, [y]: [{ [T]: "DisableMultiRegionAccessPoints" }, Te] }], error: "Invalid configuration: Multi-Region Access Point ARNs are disabled.", [d]: R }, { [h]: [{ [m]: Jt, [y]: zr, [U]: Zn }], rules: [{ [h]: [{ [m]: D, [y]: [{ [m]: G, [y]: [{ [T]: Zn }, fe] }, { [m]: G, [y]: [xe, "partition"] }] }], rules: [{ endpoint: { [A]: "https://{accessPointName}.accesspoint.s3-global.{mrapPartition#dnsSuffix}", [C]: { [Ie]: [{ [Ce]: Te, name: Us, [ve]: lt, [Ds]: Lp }] }, [v]: B }, [d]: x }], [d]: b }, { error: "Client was configured for partition `{mrapPartition#name}` but bucket referred to partition `{bucketArn#partition}`", [d]: R }], [d]: b }], [d]: b }, { error: "Invalid Access Point Name", [d]: R }], [d]: b }, bi], [d]: b }, { [h]: [{ [m]: D, [y]: [_r, _t] }], rules: [{ [h]: qr, error: "S3 Outposts does not support Dual-stack", [d]: R }, { [h]: nt, error: "S3 Outposts does not support FIPS", [d]: R }, { [h]: Lt, error: "S3 Outposts does not support S3 Accelerate", [d]: R }, { [h]: [{ [m]: ke, [y]: [{ [m]: G, [y]: [xe, "resourceId[4]"] }] }], error: "Invalid Arn: Outpost Access Point ARN contains sub resources", [d]: R }, { [h]: [{ [m]: G, [y]: Hp, [U]: ys }], rules: [{ [h]: Ui, rules: [Mr, { [h]: Kr, rules: [{ [h]: De, rules: [{ [h]: qi, rules: [{ [h]: Xr, rules: [{ [h]: Zr, rules: [{ [h]: [{ [m]: G, [y]: zp, [U]: Qn }], rules: [{ [h]: [{ [m]: G, [y]: [xe, "resourceId[3]"], [U]: Ls }], rules: [{ [h]: [{ [m]: D, [y]: [{ [T]: Qn }, va] }], rules: [{ [h]: Xe, endpoint: { [A]: "https://{accessPointName}-{bucketArn#accountId}.{outpostId}.{url#authority}", [C]: Ei, [v]: B }, [d]: x }, { endpoint: { [A]: "https://{accessPointName}-{bucketArn#accountId}.{outpostId}.s3-outposts.{bucketArn#region}.{bucketPartition#dnsSuffix}", [C]: Ei, [v]: B }, [d]: x }], [d]: b }, { error: "Expected an outpost type `accesspoint`, found {outpostType}", [d]: R }], [d]: b }, { error: "Invalid ARN: expected an access point name", [d]: R }], [d]: b }, { error: "Invalid ARN: Expected a 4-component resource", [d]: R }], [d]: b }, Fr], [d]: b }, Dr], [d]: b }, Ur], [d]: b }], [d]: b }], [d]: b }, { error: "Invalid ARN: The outpost Id may only contain a-z, A-Z, 0-9 and `-`. Found: `{outpostId}`", [d]: R }], [d]: b }, { error: "Invalid ARN: The Outpost Id was not set", [d]: R }], [d]: b }, { error: "Invalid ARN: Unrecognized format: {Bucket} (type: {arnType})", [d]: R }], [d]: b }, { error: "Invalid ARN: No ARN type specified", [d]: R }], [d]: b }, { [h]: [{ [m]: le, [y]: [K, 0, 4, Op], [U]: Yn }, { [m]: D, [y]: [{ [T]: Yn }, "arn:"] }, { [m]: _e, [y]: [{ [m]: ke, [y]: [xi] }] }], error: "Invalid ARN: `{Bucket}` was not a valid ARN", [d]: R }, { [h]: [{ [m]: ie, [y]: [Fp, Te] }, xi], error: "Path-style addressing cannot be used with ARN buckets", [d]: R }, { [h]: Vr, rules: [{ [h]: De, rules: [{ [h]: [X], rules: [{ [h]: [z, I, q, te], endpoint: { [A]: "https://s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}", [C]: re, [v]: B }, [d]: x }, { [h]: [z, I, q, F, se], rules: [{ endpoint: Si, [d]: x }], [d]: b }, { [h]: [z, I, q, F, ne], endpoint: Si, [d]: x }, { [h]: [O, I, q, te], endpoint: { [A]: "https://s3-fips.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}", [C]: re, [v]: B }, [d]: x }, { [h]: [O, I, q, F, se], rules: [{ endpoint: Ai, [d]: x }], [d]: b }, { [h]: [O, I, q, F, ne], endpoint: Ai, [d]: x }, { [h]: [z, I, M, te], endpoint: { [A]: "https://s3.dualstack.us-east-1.{partitionResult#dnsSuffix}/{uri_encoded_bucket}", [C]: re, [v]: B }, [d]: x }, { [h]: [z, I, M, F, se], rules: [{ endpoint: Ci, [d]: x }], [d]: b }, { [h]: [z, I, M, F, ne], endpoint: Ci, [d]: x }, { [h]: [O, Z, me, M, te], endpoint: { [A]: Ra, [C]: re, [v]: B }, [d]: x }, { [h]: [O, Z, me, M, F, se], rules: [{ [h]: Ue, endpoint: $r, [d]: x }, { endpoint: $r, [d]: x }], [d]: b }, { [h]: [O, Z, me, M, F, ne], endpoint: $r, [d]: x }, { [h]: [O, I, M, te], endpoint: { [A]: Jn, [C]: re, [v]: B }, [d]: x }, { [h]: [O, I, M, F, se], rules: [{ [h]: Ue, endpoint: { [A]: Jn, [C]: Y, [v]: B }, [d]: x }, { endpoint: vi, [d]: x }], [d]: b }, { [h]: [O, I, M, F, ne], endpoint: vi, [d]: x }], [d]: b }, { error: "Path-style addressing cannot be used with S3 Accelerate", [d]: R }], [d]: b }], [d]: b }], [d]: b }, { [h]: [{ [m]: ke, [y]: [Ri] }, { [m]: ie, [y]: [Ri, Te] }], rules: [{ [h]: De, rules: [{ [h]: ji, rules: [pi, gi, { [h]: Xe, endpoint: { [A]: qt, [C]: Lr, [v]: B }, [d]: x }, { [h]: nt, endpoint: { [A]: "https://s3-object-lambda-fips.{Region}.{partitionResult#dnsSuffix}", [C]: Lr, [v]: B }, [d]: x }, { endpoint: { [A]: "https://s3-object-lambda.{Region}.{partitionResult#dnsSuffix}", [C]: Lr, [v]: B }, [d]: x }], [d]: b }, $t], [d]: b }], [d]: b }, { [h]: [ii], rules: [{ [h]: De, rules: [{ [h]: ji, rules: [{ [h]: [q, z, I, te], endpoint: { [A]: "https://s3-fips.dualstack.us-east-1.{partitionResult#dnsSuffix}", [C]: re, [v]: B }, [d]: x }, { [h]: [q, z, I, F, se], rules: [{ endpoint: ki, [d]: x }], [d]: b }, { [h]: [q, z, I, F, ne], endpoint: ki, [d]: x }, { [h]: [q, O, I, te], endpoint: { [A]: "https://s3-fips.us-east-1.{partitionResult#dnsSuffix}", [C]: re, [v]: B }, [d]: x }, { [h]: [q, O, I, F, se], rules: [{ endpoint: Ti, [d]: x }], [d]: b }, { [h]: [q, O, I, F, ne], endpoint: Ti, [d]: x }, { [h]: [M, z, I, te], endpoint: { [A]: "https://s3.dualstack.us-east-1.{partitionResult#dnsSuffix}", [C]: re, [v]: B }, [d]: x }, { [h]: [M, z, I, F, se], rules: [{ endpoint: Bi, [d]: x }], [d]: b }, { [h]: [M, z, I, F, ne], endpoint: Bi, [d]: x }, { [h]: [M, O, Z, me, te], endpoint: { [A]: qt, [C]: re, [v]: B }, [d]: x }, { [h]: [M, O, Z, me, F, se], rules: [{ [h]: Ue, endpoint: Hr, [d]: x }, { endpoint: Hr, [d]: x }], [d]: b }, { [h]: [M, O, Z, me, F, ne], endpoint: Hr, [d]: x }, { [h]: [M, O, I, te], endpoint: { [A]: ei, [C]: re, [v]: B }, [d]: x }, { [h]: [M, O, I, F, se], rules: [{ [h]: Ue, endpoint: { [A]: ei, [C]: Y, [v]: B }, [d]: x }, { endpoint: Ni, [d]: x }], [d]: b }, { [h]: [M, O, I, F, ne], endpoint: Ni, [d]: x }], [d]: b }, $t], [d]: b }], [d]: b }], [d]: b }, { error: "A region must be set when sending requests to S3.", [d]: R }] };
var Vp = qp;
var jp = new th({
  size: 50,
  params: [
    "Accelerate",
    "Bucket",
    "DisableAccessPoints",
    "DisableMultiRegionAccessPoints",
    "DisableS3ExpressSessionAuth",
    "Endpoint",
    "ForcePathStyle",
    "Region",
    "UseArnRegion",
    "UseDualStack",
    "UseFIPS",
    "UseGlobalEndpoint",
    "UseObjectLambdaEndpoint",
    "UseS3ExpressControlEndpoint"
  ]
});
var Ma = (e, t = {}) => jp.get(e, () => Eh(Vp, {
  endpointParams: e,
  logger: t.logger
}));
Qt.aws = oa;
var Wp = (e) => async (t, r, s) => {
  var a, c, u;
  if (!s)
    throw new Error("Could not find `input` for `defaultEndpointRuleSetHttpAuthSchemeParametersProvider`");
  const n = await e(t, r, s), i = (u = (c = (a = pt(r)) == null ? void 0 : a.commandInstance) == null ? void 0 : c.constructor) == null ? void 0 : u.getEndpointParameterInstructions;
  if (!i)
    throw new Error(`getEndpointParameterInstructions() is not defined on \`${r.commandName}\``);
  const o = await da(s, { getEndpointParameterInstructions: i }, t);
  return Object.assign(n, o);
};
var Gp = async (e, t, r) => ({
  operation: pt(t).operation,
  region: await Pe(e.region)() || (() => {
    throw new Error("expected `region` to be configured for `aws.auth#sigv4`");
  })()
});
var Kp = Wp(Gp);
function Oa(e) {
  return {
    schemeId: "aws.auth#sigv4",
    signingProperties: {
      name: "s3",
      region: e.region
    },
    propertiesExtractor: (t, r) => ({
      signingProperties: {
        config: t,
        context: r
      }
    })
  };
}
function Fa(e) {
  return {
    schemeId: "aws.auth#sigv4a",
    signingProperties: {
      name: "s3",
      region: e.region
    },
    propertiesExtractor: (t, r) => ({
      signingProperties: {
        config: t,
        context: r
      }
    })
  };
}
var Xp = (e, t, r) => (n) => {
  var c;
  const o = (c = e(n).properties) == null ? void 0 : c.authSchemes;
  if (!o)
    return t(n);
  const a = [];
  for (const u of o) {
    const { name: l, properties: f = {}, ...p } = u, g = l.toLowerCase();
    let E;
    if (g === "sigv4a") {
      if (E = "aws.auth#sigv4a", o.find((P) => {
        const V = P.name.toLowerCase();
        return V !== "sigv4a" && V.startsWith("sigv4");
      }))
        continue;
    } else if (g.startsWith("sigv4"))
      E = "aws.auth#sigv4";
    else
      throw new Error(`Unknown HttpAuthScheme found in \`@smithy.rules#endpointRuleSet\`: \`${g}\``);
    const k = r[E];
    if (!k)
      throw new Error(`Could not find HttpAuthOption create function for \`${E}\``);
    const S = k(n);
    S.schemeId = E, S.signingProperties = { ...S.signingProperties || {}, ...p, ...f }, a.push(S);
  }
  return a;
};
var Zp = (e) => {
  const t = [];
  switch (e.operation) {
    default:
      t.push(Oa(e)), t.push(Fa(e));
  }
  return t;
};
var Qp = Xp(Ma, Zp, {
  "aws.auth#sigv4": Oa,
  "aws.auth#sigv4a": Fa
});
var Yp = (e) => {
  const t = Ou(e);
  return {
    ...du(t)
  };
};
var Jp = (e) => ({
  ...e,
  useFipsEndpoint: e.useFipsEndpoint ?? false,
  useDualstackEndpoint: e.useDualstackEndpoint ?? false,
  forcePathStyle: e.forcePathStyle ?? false,
  useAccelerateEndpoint: e.useAccelerateEndpoint ?? false,
  useGlobalEndpoint: e.useGlobalEndpoint ?? false,
  disableMultiregionAccessPoints: e.disableMultiregionAccessPoints ?? false,
  defaultSigningName: "s3"
});
var Da = {
  ForcePathStyle: { type: "clientContextParams", name: "forcePathStyle" },
  UseArnRegion: { type: "clientContextParams", name: "useArnRegion" },
  DisableMultiRegionAccessPoints: { type: "clientContextParams", name: "disableMultiregionAccessPoints" },
  Accelerate: { type: "clientContextParams", name: "useAccelerateEndpoint" },
  DisableS3ExpressSessionAuth: { type: "clientContextParams", name: "disableS3ExpressSessionAuth" },
  UseGlobalEndpoint: { type: "builtInParams", name: "useGlobalEndpoint" },
  UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
  Endpoint: { type: "builtInParams", name: "endpoint" },
  Region: { type: "builtInParams", name: "region" },
  UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" }
};
var he = class _he extends ot {
  constructor(t) {
    super(t), Object.setPrototypeOf(this, _he.prototype);
  }
};
var zs = class _zs extends he {
  constructor(r) {
    super({
      name: "NoSuchUpload",
      $fault: "client",
      ...r
    });
    _(this, "name", "NoSuchUpload");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _zs.prototype);
  }
};
var qs = class _qs extends he {
  constructor(r) {
    super({
      name: "ObjectNotInActiveTierError",
      $fault: "client",
      ...r
    });
    _(this, "name", "ObjectNotInActiveTierError");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _qs.prototype);
  }
};
var Vs = class _Vs extends he {
  constructor(r) {
    super({
      name: "BucketAlreadyExists",
      $fault: "client",
      ...r
    });
    _(this, "name", "BucketAlreadyExists");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _Vs.prototype);
  }
};
var js = class _js extends he {
  constructor(r) {
    super({
      name: "BucketAlreadyOwnedByYou",
      $fault: "client",
      ...r
    });
    _(this, "name", "BucketAlreadyOwnedByYou");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _js.prototype);
  }
};
var Ws = class _Ws extends he {
  constructor(r) {
    super({
      name: "NoSuchBucket",
      $fault: "client",
      ...r
    });
    _(this, "name", "NoSuchBucket");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _Ws.prototype);
  }
};
var Wi;
(function(e) {
  e.visit = (t, r) => t.Prefix !== void 0 ? r.Prefix(t.Prefix) : t.Tag !== void 0 ? r.Tag(t.Tag) : t.And !== void 0 ? r.And(t.And) : r._(t.$unknown[0], t.$unknown[1]);
})(Wi || (Wi = {}));
var Gi;
(function(e) {
  e.visit = (t, r) => t.Prefix !== void 0 ? r.Prefix(t.Prefix) : t.Tag !== void 0 ? r.Tag(t.Tag) : t.AccessPointArn !== void 0 ? r.AccessPointArn(t.AccessPointArn) : t.And !== void 0 ? r.And(t.And) : r._(t.$unknown[0], t.$unknown[1]);
})(Gi || (Gi = {}));
var Gs = class _Gs extends he {
  constructor(r) {
    super({
      name: "InvalidObjectState",
      $fault: "client",
      ...r
    });
    _(this, "name", "InvalidObjectState");
    _(this, "$fault", "client");
    _(this, "StorageClass");
    _(this, "AccessTier");
    Object.setPrototypeOf(this, _Gs.prototype), this.StorageClass = r.StorageClass, this.AccessTier = r.AccessTier;
  }
};
var Ks = class _Ks extends he {
  constructor(r) {
    super({
      name: "NoSuchKey",
      $fault: "client",
      ...r
    });
    _(this, "name", "NoSuchKey");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _Ks.prototype);
  }
};
var Xs = class _Xs extends he {
  constructor(r) {
    super({
      name: "NotFound",
      $fault: "client",
      ...r
    });
    _(this, "name", "NotFound");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _Xs.prototype);
  }
};
var eg = (e) => ({
  ...e,
  ...e.SecretAccessKey && { SecretAccessKey: Ne },
  ...e.SessionToken && { SessionToken: Ne }
});
var tg = (e) => ({
  ...e,
  ...e.SSEKMSKeyId && { SSEKMSKeyId: Ne },
  ...e.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: Ne },
  ...e.Credentials && { Credentials: eg(e.Credentials) }
});
var rg = (e) => ({
  ...e,
  ...e.SSEKMSKeyId && { SSEKMSKeyId: Ne },
  ...e.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: Ne }
});
var Zs = class _Zs extends he {
  constructor(r) {
    super({
      name: "EncryptionTypeMismatch",
      $fault: "client",
      ...r
    });
    _(this, "name", "EncryptionTypeMismatch");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _Zs.prototype);
  }
};
var Qs = class _Qs extends he {
  constructor(r) {
    super({
      name: "InvalidRequest",
      $fault: "client",
      ...r
    });
    _(this, "name", "InvalidRequest");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _Qs.prototype);
  }
};
var Ys = class _Ys extends he {
  constructor(r) {
    super({
      name: "InvalidWriteOffset",
      $fault: "client",
      ...r
    });
    _(this, "name", "InvalidWriteOffset");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _Ys.prototype);
  }
};
var Js = class _Js extends he {
  constructor(r) {
    super({
      name: "TooManyParts",
      $fault: "client",
      ...r
    });
    _(this, "name", "TooManyParts");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _Js.prototype);
  }
};
var en = class _en extends he {
  constructor(r) {
    super({
      name: "ObjectAlreadyInActiveTierError",
      $fault: "client",
      ...r
    });
    _(this, "name", "ObjectAlreadyInActiveTierError");
    _(this, "$fault", "client");
    Object.setPrototypeOf(this, _en.prototype);
  }
};
var Ki;
(function(e) {
  e.visit = (t, r) => t.Records !== void 0 ? r.Records(t.Records) : t.Stats !== void 0 ? r.Stats(t.Stats) : t.Progress !== void 0 ? r.Progress(t.Progress) : t.Cont !== void 0 ? r.Cont(t.Cont) : t.End !== void 0 ? r.End(t.End) : r._(t.$unknown[0], t.$unknown[1]);
})(Ki || (Ki = {}));
var sg = (e) => ({
  ...e,
  ...e.SSEKMSKeyId && { SSEKMSKeyId: Ne },
  ...e.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: Ne }
});
var ng = (e) => ({
  ...e,
  ...e.SSECustomerKey && { SSECustomerKey: Ne },
  ...e.SSEKMSKeyId && { SSEKMSKeyId: Ne },
  ...e.SSEKMSEncryptionContext && { SSEKMSEncryptionContext: Ne }
});
var ig = async (e, t) => {
  const r = wo(e, t), s = ee({}, ze, {
    [dm]: e[jg],
    [hr]: e[dr],
    [pr]: e[fr],
    [gr]: e[lr],
    [ht]: [() => ze(e[ft]), () => e[ft].toString()]
  });
  r.bp("/"), r.p("Bucket", () => e.Bucket, "{Bucket}", false);
  const n = ee({
    [cm]: [, ""]
  });
  let i;
  return r.m("GET").h(s).q(n).b(i), r.build();
};
var og = async (e, t) => {
  const r = wo(e, t), s = ee({}, ze, {
    [sm]: e[Pg] || "application/octet-stream",
    [um]: e[Cg],
    [Qg]: e[Rg],
    [Yg]: e[kg],
    [Jg]: e[Tg],
    [em]: e[Bg],
    [tm]: [() => ze(e[Xi]), () => e[Xi].toString()],
    [rm]: e[Ng],
    [Cm]: e[vg],
    [Wa]: e[$a],
    [Ga]: e[La],
    [Ka]: e[Ha],
    [Xa]: e[za],
    [Za]: e[qa],
    [nm]: [() => ze(e[Zi]), () => Ku(e[Zi]).toString()],
    [om]: e[$g],
    [am]: e[Lg],
    [pm]: e[Og],
    [gm]: e[Fg],
    [mm]: e[Dg],
    [ym]: e[Ug],
    [Tm]: [() => ze(e[Yi]), () => e[Yi].toString()],
    [hr]: e[dr],
    [Am]: e[Wt],
    [Bm]: e[Zg],
    [Qa]: e[Va],
    [vm]: e[Wg],
    [Ya]: e[ja],
    [pr]: e[fr],
    [gr]: e[lr],
    [ht]: [() => ze(e[ft]), () => e[ft].toString()],
    [Sm]: e[Vg],
    [Rm]: e[Kg],
    [bm]: e[zg],
    [Em]: [() => ze(e[Qi]), () => yd(e[Qi]).toString()],
    [wm]: e[Hg],
    [hm]: e[_g],
    ...e.Metadata !== void 0 && Object.keys(e.Metadata).reduce((a, c) => (a[`x-amz-meta-${c.toLowerCase()}`] = e.Metadata[c], a), {})
  });
  r.bp("/{Key+}"), r.p("Bucket", () => e.Bucket, "{Bucket}", false), r.p("Key", () => e.Key, "{Key+}", true);
  const n = ee({
    [Nm]: [, "PutObject"]
  });
  let i, o;
  return e.Body !== void 0 && (o = e.Body, i = o), r.m("PUT").h(s).q(n).b(i), r.build();
};
var ag = async (e, t) => {
  if (e.statusCode !== 200 && e.statusCode >= 300)
    return Ua(e, t);
  const r = ee({
    $metadata: we(e),
    [dr]: [, e.headers[hr]],
    [fr]: [, e.headers[pr]],
    [lr]: [, e.headers[gr]],
    [ft]: [() => e.headers[ht] !== void 0, () => To(e.headers[ht])]
  }), s = No(Hu(await zo(e.body, t)), "body");
  return s[Jr] != null && (r[Jr] = Ag(s[Jr])), r;
};
var cg = async (e, t) => {
  if (e.statusCode !== 200 && e.statusCode >= 300)
    return Ua(e, t);
  const r = ee({
    $metadata: we(e),
    [jt]: [, e.headers[fm]],
    [Mg]: [, e.headers[im]],
    [$a]: [, e.headers[Wa]],
    [La]: [, e.headers[Ga]],
    [Ha]: [, e.headers[Ka]],
    [za]: [, e.headers[Xa]],
    [qa]: [, e.headers[Za]],
    [Ig]: [, e.headers[lm]],
    [dr]: [, e.headers[hr]],
    [Xg]: [, e.headers[km]],
    [Va]: [, e.headers[Qa]],
    [ja]: [, e.headers[Ya]],
    [fr]: [, e.headers[pr]],
    [lr]: [, e.headers[gr]],
    [ft]: [() => e.headers[ht] !== void 0, () => To(e.headers[ht])],
    [Gg]: [() => e.headers[Ji] !== void 0, () => Vu(e.headers[Ji])],
    [qg]: [, e.headers[xm]]
  });
  return await yo(e.body, t), r;
};
var Ua = async (e, t) => {
  const r = {
    ...e,
    body: await Ol(e.body, t)
  }, s = Fl(e, r.body);
  switch (s) {
    case "NoSuchUpload":
    case "com.amazonaws.s3#NoSuchUpload":
      throw await wg(r);
    case "ObjectNotInActiveTierError":
    case "com.amazonaws.s3#ObjectNotInActiveTierError":
      throw await xg(r);
    case "BucketAlreadyExists":
    case "com.amazonaws.s3#BucketAlreadyExists":
      throw await dg(r);
    case "BucketAlreadyOwnedByYou":
    case "com.amazonaws.s3#BucketAlreadyOwnedByYou":
      throw await lg(r);
    case "NoSuchBucket":
    case "com.amazonaws.s3#NoSuchBucket":
      throw await mg(r);
    case "InvalidObjectState":
    case "com.amazonaws.s3#InvalidObjectState":
      throw await hg(r);
    case "NoSuchKey":
    case "com.amazonaws.s3#NoSuchKey":
      throw await yg(r);
    case "NotFound":
    case "com.amazonaws.s3#NotFound":
      throw await bg(r);
    case "EncryptionTypeMismatch":
    case "com.amazonaws.s3#EncryptionTypeMismatch":
      throw await fg(r);
    case "InvalidRequest":
    case "com.amazonaws.s3#InvalidRequest":
      throw await pg(r);
    case "InvalidWriteOffset":
    case "com.amazonaws.s3#InvalidWriteOffset":
      throw await gg(r);
    case "TooManyParts":
    case "com.amazonaws.s3#TooManyParts":
      throw await Sg(r);
    case "ObjectAlreadyInActiveTierError":
    case "com.amazonaws.s3#ObjectAlreadyInActiveTierError":
      throw await Eg(r);
    default:
      const n = r.body;
      return ug({
        output: e,
        parsedBody: n,
        errorCode: s
      });
  }
};
var ug = nd(he);
var dg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new Vs({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var lg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new js({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var fg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new Zs({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var hg = async (e, t) => {
  const r = ee({}), s = e.body;
  s[Yr] != null && (r[Yr] = Rt(s[Yr])), s[Wt] != null && (r[Wt] = Rt(s[Wt]));
  const n = new Gs({
    $metadata: we(e),
    ...r
  });
  return Se(n, e.body);
};
var pg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new Qs({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var gg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new Ys({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var mg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new Ws({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var yg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new Ks({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var wg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new zs({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var bg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new Xs({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var Eg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new en({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var xg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new qs({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var Sg = async (e, t) => {
  const r = ee({});
  e.body;
  const s = new Js({
    $metadata: we(e),
    ...r
  });
  return Se(s, e.body);
};
var Ag = (e, t) => {
  const r = {};
  return e[Qr] != null && (r[Qr] = Rt(e[Qr])), e[es] != null && (r[es] = Rt(e[es])), e[ts] != null && (r[ts] = Rt(e[ts])), e[jt] != null && (r[jt] = No(Zu(e[jt]))), r;
};
var we = (e) => ({
  httpStatusCode: e.statusCode,
  requestId: e.headers["x-amzn-requestid"] ?? e.headers["x-amzn-request-id"] ?? e.headers["x-amz-request-id"],
  extendedRequestId: e.headers["x-amz-id-2"],
  cfId: e.headers["x-amz-cf-id"]
});
var Cg = "ACL";
var Qr = "AccessKeyId";
var Yr = "AccessTier";
var ft = "BucketKeyEnabled";
var Jr = "Credentials";
var vg = "ChecksumAlgorithm";
var Rg = "CacheControl";
var $a = "ChecksumCRC32";
var La = "ChecksumCRC32C";
var Ha = "ChecksumCRC64NVME";
var kg = "ContentDisposition";
var Tg = "ContentEncoding";
var Bg = "ContentLanguage";
var Xi = "ContentLength";
var Ng = "ContentMD5";
var za = "ChecksumSHA1";
var qa = "ChecksumSHA256";
var Ig = "ChecksumType";
var Pg = "ContentType";
var Zi = "Expires";
var _g = "ExpectedBucketOwner";
var Mg = "ETag";
var jt = "Expiration";
var Og = "GrantFullControl";
var Fg = "GrantRead";
var Dg = "GrantReadACP";
var Ug = "GrantWriteACP";
var $g = "IfMatch";
var Lg = "IfNoneMatch";
var Hg = "ObjectLockLegalHoldStatus";
var zg = "ObjectLockMode";
var Qi = "ObjectLockRetainUntilDate";
var qg = "RequestCharged";
var Vg = "RequestPayer";
var es = "SecretAccessKey";
var Wt = "StorageClass";
var jg = "SessionMode";
var dr = "ServerSideEncryption";
var Va = "SSECustomerAlgorithm";
var Wg = "SSECustomerKey";
var ja = "SSECustomerKeyMD5";
var lr = "SSEKMSEncryptionContext";
var fr = "SSEKMSKeyId";
var ts = "SessionToken";
var Gg = "Size";
var Kg = "Tagging";
var Xg = "VersionId";
var Yi = "WriteOffsetBytes";
var Zg = "WebsiteRedirectLocation";
var Qg = "cache-control";
var Yg = "content-disposition";
var Jg = "content-encoding";
var em = "content-language";
var tm = "content-length";
var rm = "content-md5";
var sm = "content-type";
var nm = "expires";
var im = "etag";
var om = "if-match";
var am = "if-none-match";
var cm = "session";
var um = "x-amz-acl";
var Wa = "x-amz-checksum-crc32";
var Ga = "x-amz-checksum-crc32c";
var Ka = "x-amz-checksum-crc64nvme";
var Xa = "x-amz-checksum-sha1";
var Za = "x-amz-checksum-sha256";
var dm = "x-amz-create-session-mode";
var lm = "x-amz-checksum-type";
var fm = "x-amz-expiration";
var hm = "x-amz-expected-bucket-owner";
var pm = "x-amz-grant-full-control";
var gm = "x-amz-grant-read";
var mm = "x-amz-grant-read-acp";
var ym = "x-amz-grant-write-acp";
var wm = "x-amz-object-lock-legal-hold";
var bm = "x-amz-object-lock-mode";
var Em = "x-amz-object-lock-retain-until-date";
var Ji = "x-amz-object-size";
var xm = "x-amz-request-charged";
var Sm = "x-amz-request-payer";
var Am = "x-amz-storage-class";
var Cm = "x-amz-sdk-checksum-algorithm";
var hr = "x-amz-server-side-encryption";
var pr = "x-amz-server-side-encryption-aws-kms-key-id";
var ht = "x-amz-server-side-encryption-bucket-key-enabled";
var gr = "x-amz-server-side-encryption-context";
var Qa = "x-amz-server-side-encryption-customer-algorithm";
var vm = "x-amz-server-side-encryption-customer-key";
var Ya = "x-amz-server-side-encryption-customer-key-md5";
var Rm = "x-amz-tagging";
var km = "x-amz-version-id";
var Tm = "x-amz-write-offset-bytes";
var Bm = "x-amz-website-redirect-location";
var Nm = "x-id";
var Im = class extends Cs.classBuilder().ep({
  ...Da,
  DisableS3ExpressSessionAuth: { type: "staticContextParams", value: true },
  Bucket: { type: "contextParams", name: "Bucket" }
}).m(function(t, r, s, n) {
  return [
    fo(s, this.serialize, this.deserialize),
    la(s, t.getEndpointParameterInstructions()),
    Qo(s)
  ];
}).s("AmazonS3", "CreateSession", {}).n("S3Client", "CreateSessionCommand").f(rg, tg).ser(ig).de(ag).build() {
};
var Pm = "@aws-sdk/client-s3";
var _m = "AWS SDK for JavaScript S3 Client for Node.js, Browser and React Native";
var Mm = "3.758.0";
var Om = {
  build: "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types'",
  "build:cjs": "node ../../scripts/compilation/inline client-s3",
  "build:es": "tsc -p tsconfig.es.json",
  "build:include:deps": "lerna run --scope $npm_package_name --include-dependencies build",
  "build:types": "tsc -p tsconfig.types.json",
  "build:types:downlevel": "downlevel-dts dist-types dist-types/ts3.4",
  clean: "rimraf ./dist-* && rimraf *.tsbuildinfo",
  "extract:docs": "api-extractor run --local",
  "generate:client": "node ../../scripts/generate-clients/single-service --solo s3",
  test: "yarn g:vitest run",
  "test:browser": "node ./test/browser-build/esbuild && yarn g:vitest run -c vitest.config.browser.ts",
  "test:browser:watch": "node ./test/browser-build/esbuild && yarn g:vitest watch -c vitest.config.browser.ts",
  "test:e2e": "yarn g:vitest run -c vitest.config.e2e.ts && yarn test:browser",
  "test:e2e:watch": "yarn g:vitest watch -c vitest.config.e2e.ts",
  "test:watch": "yarn g:vitest watch"
};
var Fm = "./dist-cjs/index.js";
var Dm = "./dist-types/index.d.ts";
var Um = "./dist-es/index.js";
var $m = false;
var Lm = {
  "@aws-crypto/sha1-browser": "5.2.0",
  "@aws-crypto/sha256-browser": "5.2.0",
  "@aws-crypto/sha256-js": "5.2.0",
  "@aws-sdk/core": "3.758.0",
  "@aws-sdk/credential-provider-node": "3.758.0",
  "@aws-sdk/middleware-bucket-endpoint": "3.734.0",
  "@aws-sdk/middleware-expect-continue": "3.734.0",
  "@aws-sdk/middleware-flexible-checksums": "3.758.0",
  "@aws-sdk/middleware-host-header": "3.734.0",
  "@aws-sdk/middleware-location-constraint": "3.734.0",
  "@aws-sdk/middleware-logger": "3.734.0",
  "@aws-sdk/middleware-recursion-detection": "3.734.0",
  "@aws-sdk/middleware-sdk-s3": "3.758.0",
  "@aws-sdk/middleware-ssec": "3.734.0",
  "@aws-sdk/middleware-user-agent": "3.758.0",
  "@aws-sdk/region-config-resolver": "3.734.0",
  "@aws-sdk/signature-v4-multi-region": "3.758.0",
  "@aws-sdk/types": "3.734.0",
  "@aws-sdk/util-endpoints": "3.743.0",
  "@aws-sdk/util-user-agent-browser": "3.734.0",
  "@aws-sdk/util-user-agent-node": "3.758.0",
  "@aws-sdk/xml-builder": "3.734.0",
  "@smithy/config-resolver": "^4.0.1",
  "@smithy/core": "^3.1.5",
  "@smithy/eventstream-serde-browser": "^4.0.1",
  "@smithy/eventstream-serde-config-resolver": "^4.0.1",
  "@smithy/eventstream-serde-node": "^4.0.1",
  "@smithy/fetch-http-handler": "^5.0.1",
  "@smithy/hash-blob-browser": "^4.0.1",
  "@smithy/hash-node": "^4.0.1",
  "@smithy/hash-stream-node": "^4.0.1",
  "@smithy/invalid-dependency": "^4.0.1",
  "@smithy/md5-js": "^4.0.1",
  "@smithy/middleware-content-length": "^4.0.1",
  "@smithy/middleware-endpoint": "^4.0.6",
  "@smithy/middleware-retry": "^4.0.7",
  "@smithy/middleware-serde": "^4.0.2",
  "@smithy/middleware-stack": "^4.0.1",
  "@smithy/node-config-provider": "^4.0.1",
  "@smithy/node-http-handler": "^4.0.3",
  "@smithy/protocol-http": "^5.0.1",
  "@smithy/smithy-client": "^4.1.6",
  "@smithy/types": "^4.1.0",
  "@smithy/url-parser": "^4.0.1",
  "@smithy/util-base64": "^4.0.0",
  "@smithy/util-body-length-browser": "^4.0.0",
  "@smithy/util-body-length-node": "^4.0.0",
  "@smithy/util-defaults-mode-browser": "^4.0.7",
  "@smithy/util-defaults-mode-node": "^4.0.7",
  "@smithy/util-endpoints": "^3.0.1",
  "@smithy/util-middleware": "^4.0.1",
  "@smithy/util-retry": "^4.0.1",
  "@smithy/util-stream": "^4.1.2",
  "@smithy/util-utf8": "^4.0.0",
  "@smithy/util-waiter": "^4.0.2",
  tslib: "^2.6.2"
};
var Hm = {
  "@aws-sdk/signature-v4-crt": "3.758.0",
  "@tsconfig/node18": "18.2.4",
  "@types/node": "^18.19.69",
  concurrently: "7.0.0",
  "downlevel-dts": "0.10.1",
  rimraf: "3.0.2",
  typescript: "~5.2.2"
};
var zm = {
  node: ">=18.0.0"
};
var qm = {
  "<4.0": {
    "dist-types/*": [
      "dist-types/ts3.4/*"
    ]
  }
};
var Vm = [
  "dist-*/**"
];
var jm = {
  name: "AWS SDK for JavaScript Team",
  url: "https://aws.amazon.com/javascript/"
};
var Wm = "Apache-2.0";
var Gm = {
  "./dist-es/runtimeConfig": "./dist-es/runtimeConfig.browser"
};
var Km = "https://github.com/aws/aws-sdk-js-v3/tree/main/clients/client-s3";
var Xm = {
  type: "git",
  url: "https://github.com/aws/aws-sdk-js-v3.git",
  directory: "clients/client-s3"
};
var Zm = {
  name: Pm,
  description: _m,
  version: Mm,
  scripts: Om,
  main: Fm,
  types: Dm,
  module: Um,
  sideEffects: $m,
  dependencies: Lm,
  devDependencies: Hm,
  engines: zm,
  typesVersions: qm,
  files: Vm,
  author: jm,
  license: Wm,
  browser: Gm,
  "react-native": {
    "./dist-es/runtimeConfig": "./dist-es/runtimeConfig.native"
  },
  homepage: Km,
  repository: Xm
};
function eo(e) {
  return typeof e == "string" ? e.length === 0 : e.byteLength === 0;
}
var Ja = { name: "SHA-1" };
var to = {
  name: "HMAC",
  hash: Ja
};
var Qm = new Uint8Array([
  218,
  57,
  163,
  238,
  94,
  107,
  75,
  13,
  50,
  85,
  191,
  239,
  149,
  96,
  24,
  144,
  175,
  216,
  7,
  9
]);
var Ym = {};
function qe() {
  return typeof window < "u" ? window : typeof self < "u" ? self : Ym;
}
var Jm = (
  /** @class */
  function() {
    function e(t) {
      this.toHash = new Uint8Array(0), t !== void 0 && (this.key = new Promise(function(r, s) {
        qe().crypto.subtle.importKey("raw", ro(t), to, false, ["sign"]).then(r, s);
      }), this.key.catch(function() {
      }));
    }
    return e.prototype.update = function(t) {
      if (!eo(t)) {
        var r = ro(t), s = new Uint8Array(this.toHash.byteLength + r.byteLength);
        s.set(this.toHash, 0), s.set(r, this.toHash.byteLength), this.toHash = s;
      }
    }, e.prototype.digest = function() {
      var t = this;
      return this.key ? this.key.then(function(r) {
        return qe().crypto.subtle.sign(to, r, t.toHash).then(function(s) {
          return new Uint8Array(s);
        });
      }) : eo(this.toHash) ? Promise.resolve(Qm) : Promise.resolve().then(function() {
        return qe().crypto.subtle.digest(Ja, t.toHash);
      }).then(function(r) {
        return Promise.resolve(new Uint8Array(r));
      });
    }, e.prototype.reset = function() {
      this.toHash = new Uint8Array(0);
    }, e;
  }()
);
function ro(e) {
  return typeof e == "string" ? jo(e) : ArrayBuffer.isView(e) ? new Uint8Array(e.buffer, e.byteOffset, e.byteLength / Uint8Array.BYTES_PER_ELEMENT) : new Uint8Array(e);
}
var e0 = [
  "decrypt",
  "digest",
  "encrypt",
  "exportKey",
  "generateKey",
  "importKey",
  "sign",
  "verify"
];
function ec(e) {
  if (t0(e) && typeof e.crypto.subtle == "object") {
    var t = e.crypto.subtle;
    return r0(t);
  }
  return false;
}
function t0(e) {
  if (typeof e == "object" && typeof e.crypto == "object") {
    var t = e.crypto.getRandomValues;
    return typeof t == "function";
  }
  return false;
}
function r0(e) {
  return e && e0.every(function(t) {
    return typeof e[t] == "function";
  });
}
var s0 = (
  /** @class */
  function() {
    function e(t) {
      if (ec(qe()))
        this.hash = new Jm(t);
      else
        throw new Error("SHA1 not supported");
    }
    return e.prototype.update = function(t, r) {
      this.hash.update(je(t));
    }, e.prototype.digest = function() {
      return this.hash.digest();
    }, e.prototype.reset = function() {
      this.hash.reset();
    }, e;
  }()
);
var tc = { name: "SHA-256" };
var so = {
  name: "HMAC",
  hash: tc
};
var n0 = new Uint8Array([
  227,
  176,
  196,
  66,
  152,
  252,
  28,
  20,
  154,
  251,
  244,
  200,
  153,
  111,
  185,
  36,
  39,
  174,
  65,
  228,
  100,
  155,
  147,
  76,
  164,
  149,
  153,
  27,
  120,
  82,
  184,
  85
]);
var i0 = (
  /** @class */
  function() {
    function e(t) {
      this.toHash = new Uint8Array(0), this.secret = t, this.reset();
    }
    return e.prototype.update = function(t) {
      if (!It(t)) {
        var r = je(t), s = new Uint8Array(this.toHash.byteLength + r.byteLength);
        s.set(this.toHash, 0), s.set(r, this.toHash.byteLength), this.toHash = s;
      }
    }, e.prototype.digest = function() {
      var t = this;
      return this.key ? this.key.then(function(r) {
        return qe().crypto.subtle.sign(so, r, t.toHash).then(function(s) {
          return new Uint8Array(s);
        });
      }) : It(this.toHash) ? Promise.resolve(n0) : Promise.resolve().then(function() {
        return qe().crypto.subtle.digest(tc, t.toHash);
      }).then(function(r) {
        return Promise.resolve(new Uint8Array(r));
      });
    }, e.prototype.reset = function() {
      var t = this;
      this.toHash = new Uint8Array(0), this.secret && this.secret !== void 0 && (this.key = new Promise(function(r, s) {
        qe().crypto.subtle.importKey("raw", je(t.secret), so, false, ["sign"]).then(r, s);
      }), this.key.catch(function() {
      }));
    }, e;
  }()
);
var Re = 64;
var o0 = 32;
var a0 = new Uint32Array([
  1116352408,
  1899447441,
  3049323471,
  3921009573,
  961987163,
  1508970993,
  2453635748,
  2870763221,
  3624381080,
  310598401,
  607225278,
  1426881987,
  1925078388,
  2162078206,
  2614888103,
  3248222580,
  3835390401,
  4022224774,
  264347078,
  604807628,
  770255983,
  1249150122,
  1555081692,
  1996064986,
  2554220882,
  2821834349,
  2952996808,
  3210313671,
  3336571891,
  3584528711,
  113926993,
  338241895,
  666307205,
  773529912,
  1294757372,
  1396182291,
  1695183700,
  1986661051,
  2177026350,
  2456956037,
  2730485921,
  2820302411,
  3259730800,
  3345764771,
  3516065817,
  3600352804,
  4094571909,
  275423344,
  430227734,
  506948616,
  659060556,
  883997877,
  958139571,
  1322822218,
  1537002063,
  1747873779,
  1955562222,
  2024104815,
  2227730452,
  2361852424,
  2428436474,
  2756734187,
  3204031479,
  3329325298
]);
var c0 = [
  1779033703,
  3144134277,
  1013904242,
  2773480762,
  1359893119,
  2600822924,
  528734635,
  1541459225
];
var u0 = Math.pow(2, 53) - 1;
var Gt = (
  /** @class */
  function() {
    function e() {
      this.state = Int32Array.from(c0), this.temp = new Int32Array(64), this.buffer = new Uint8Array(64), this.bufferLength = 0, this.bytesHashed = 0, this.finished = false;
    }
    return e.prototype.update = function(t) {
      if (this.finished)
        throw new Error("Attempted to update an already finished hash.");
      var r = 0, s = t.byteLength;
      if (this.bytesHashed += s, this.bytesHashed * 8 > u0)
        throw new Error("Cannot hash more than 2^53 - 1 bits");
      for (; s > 0; )
        this.buffer[this.bufferLength++] = t[r++], s--, this.bufferLength === Re && (this.hashBuffer(), this.bufferLength = 0);
    }, e.prototype.digest = function() {
      if (!this.finished) {
        var t = this.bytesHashed * 8, r = new DataView(this.buffer.buffer, this.buffer.byteOffset, this.buffer.byteLength), s = this.bufferLength;
        if (r.setUint8(this.bufferLength++, 128), s % Re >= Re - 8) {
          for (var n = this.bufferLength; n < Re; n++)
            r.setUint8(n, 0);
          this.hashBuffer(), this.bufferLength = 0;
        }
        for (var n = this.bufferLength; n < Re - 8; n++)
          r.setUint8(n, 0);
        r.setUint32(Re - 8, Math.floor(t / 4294967296), true), r.setUint32(Re - 4, t), this.hashBuffer(), this.finished = true;
      }
      for (var i = new Uint8Array(o0), n = 0; n < 8; n++)
        i[n * 4] = this.state[n] >>> 24 & 255, i[n * 4 + 1] = this.state[n] >>> 16 & 255, i[n * 4 + 2] = this.state[n] >>> 8 & 255, i[n * 4 + 3] = this.state[n] >>> 0 & 255;
      return i;
    }, e.prototype.hashBuffer = function() {
      for (var t = this, r = t.buffer, s = t.state, n = s[0], i = s[1], o = s[2], a = s[3], c = s[4], u = s[5], l = s[6], f = s[7], p = 0; p < Re; p++) {
        if (p < 16)
          this.temp[p] = (r[p * 4] & 255) << 24 | (r[p * 4 + 1] & 255) << 16 | (r[p * 4 + 2] & 255) << 8 | r[p * 4 + 3] & 255;
        else {
          var g = this.temp[p - 2], E = (g >>> 17 | g << 15) ^ (g >>> 19 | g << 13) ^ g >>> 10;
          g = this.temp[p - 15];
          var k = (g >>> 7 | g << 25) ^ (g >>> 18 | g << 14) ^ g >>> 3;
          this.temp[p] = (E + this.temp[p - 7] | 0) + (k + this.temp[p - 16] | 0);
        }
        var S = (((c >>> 6 | c << 26) ^ (c >>> 11 | c << 21) ^ (c >>> 25 | c << 7)) + (c & u ^ ~c & l) | 0) + (f + (a0[p] + this.temp[p] | 0) | 0) | 0, N = ((n >>> 2 | n << 30) ^ (n >>> 13 | n << 19) ^ (n >>> 22 | n << 10)) + (n & i ^ n & o ^ i & o) | 0;
        f = l, l = u, u = c, c = a + S | 0, a = o, o = i, i = n, n = S + N | 0;
      }
      s[0] += n, s[1] += i, s[2] += o, s[3] += a, s[4] += c, s[5] += u, s[6] += l, s[7] += f;
    }, e;
  }()
);
var d0 = (
  /** @class */
  function() {
    function e(t) {
      this.secret = t, this.hash = new Gt(), this.reset();
    }
    return e.prototype.update = function(t) {
      if (!(It(t) || this.error))
        try {
          this.hash.update(je(t));
        } catch (r) {
          this.error = r;
        }
    }, e.prototype.digestSync = function() {
      if (this.error)
        throw this.error;
      return this.outer ? (this.outer.finished || this.outer.update(this.hash.digest()), this.outer.digest()) : this.hash.digest();
    }, e.prototype.digest = function() {
      return Ns(this, void 0, void 0, function() {
        return Is(this, function(t) {
          return [2, this.digestSync()];
        });
      });
    }, e.prototype.reset = function() {
      if (this.hash = new Gt(), this.secret) {
        this.outer = new Gt();
        var t = l0(this.secret), r = new Uint8Array(Re);
        r.set(t);
        for (var s = 0; s < Re; s++)
          t[s] ^= 54, r[s] ^= 92;
        this.hash.update(t), this.outer.update(r);
        for (var s = 0; s < t.byteLength; s++)
          t[s] = 0;
      }
    }, e;
  }()
);
function l0(e) {
  var t = je(e);
  if (t.byteLength > Re) {
    var r = new Gt();
    r.update(t), t = r.digest();
  }
  var s = new Uint8Array(Re);
  return s.set(t), s;
}
var f0 = (
  /** @class */
  function() {
    function e(t) {
      ec(qe()) ? this.hash = new i0(t) : this.hash = new d0(t);
    }
    return e.prototype.update = function(t, r) {
      this.hash.update(je(t));
    }, e.prototype.digest = function() {
      return this.hash.digest();
    }, e.prototype.reset = function() {
      this.hash.reset();
    }, e;
  }()
);
var h0 = {
  "Amazon Silk": "amazon_silk",
  "Android Browser": "android",
  Bada: "bada",
  BlackBerry: "blackberry",
  Chrome: "chrome",
  Chromium: "chromium",
  Electron: "electron",
  Epiphany: "epiphany",
  Firefox: "firefox",
  Focus: "focus",
  Generic: "generic",
  "Google Search": "google_search",
  Googlebot: "googlebot",
  "Internet Explorer": "ie",
  "K-Meleon": "k_meleon",
  Maxthon: "maxthon",
  "Microsoft Edge": "edge",
  "MZ Browser": "mz",
  "NAVER Whale Browser": "naver",
  Opera: "opera",
  "Opera Coast": "opera_coast",
  PhantomJS: "phantomjs",
  Puffin: "puffin",
  QupZilla: "qupzilla",
  QQ: "qq",
  QQLite: "qqlite",
  Safari: "safari",
  Sailfish: "sailfish",
  "Samsung Internet for Android": "samsung_internet",
  SeaMonkey: "seamonkey",
  Sleipnir: "sleipnir",
  Swing: "swing",
  Tizen: "tizen",
  "UC Browser": "uc",
  Vivaldi: "vivaldi",
  "WebOS Browser": "webos",
  WeChat: "wechat",
  "Yandex Browser": "yandex",
  Roku: "roku"
};
var rc = {
  amazon_silk: "Amazon Silk",
  android: "Android Browser",
  bada: "Bada",
  blackberry: "BlackBerry",
  chrome: "Chrome",
  chromium: "Chromium",
  electron: "Electron",
  epiphany: "Epiphany",
  firefox: "Firefox",
  focus: "Focus",
  generic: "Generic",
  googlebot: "Googlebot",
  google_search: "Google Search",
  ie: "Internet Explorer",
  k_meleon: "K-Meleon",
  maxthon: "Maxthon",
  edge: "Microsoft Edge",
  mz: "MZ Browser",
  naver: "NAVER Whale Browser",
  opera: "Opera",
  opera_coast: "Opera Coast",
  phantomjs: "PhantomJS",
  puffin: "Puffin",
  qupzilla: "QupZilla",
  qq: "QQ Browser",
  qqlite: "QQ Browser Lite",
  safari: "Safari",
  sailfish: "Sailfish",
  samsung_internet: "Samsung Internet for Android",
  seamonkey: "SeaMonkey",
  sleipnir: "Sleipnir",
  swing: "Swing",
  tizen: "Tizen",
  uc: "UC Browser",
  vivaldi: "Vivaldi",
  webos: "WebOS Browser",
  wechat: "WeChat",
  yandex: "Yandex Browser"
};
var j = {
  tablet: "tablet",
  mobile: "mobile",
  desktop: "desktop",
  tv: "tv"
};
var ye = {
  WindowsPhone: "Windows Phone",
  Windows: "Windows",
  MacOS: "macOS",
  iOS: "iOS",
  Android: "Android",
  WebOS: "WebOS",
  BlackBerry: "BlackBerry",
  Bada: "Bada",
  Tizen: "Tizen",
  Linux: "Linux",
  ChromeOS: "Chrome OS",
  PlayStation4: "PlayStation 4",
  Roku: "Roku"
};
var Le = {
  EdgeHTML: "EdgeHTML",
  Blink: "Blink",
  Trident: "Trident",
  Presto: "Presto",
  Gecko: "Gecko",
  WebKit: "WebKit"
};
var w = class _w {
  /**
   * Get first matched item for a string
   * @param {RegExp} regexp
   * @param {String} ua
   * @return {Array|{index: number, input: string}|*|boolean|string}
   */
  static getFirstMatch(t, r) {
    const s = r.match(t);
    return s && s.length > 0 && s[1] || "";
  }
  /**
   * Get second matched item for a string
   * @param regexp
   * @param {String} ua
   * @return {Array|{index: number, input: string}|*|boolean|string}
   */
  static getSecondMatch(t, r) {
    const s = r.match(t);
    return s && s.length > 1 && s[2] || "";
  }
  /**
   * Match a regexp and return a constant or undefined
   * @param {RegExp} regexp
   * @param {String} ua
   * @param {*} _const Any const that will be returned if regexp matches the string
   * @return {*}
   */
  static matchAndReturnConst(t, r, s) {
    if (t.test(r))
      return s;
  }
  static getWindowsVersionName(t) {
    switch (t) {
      case "NT":
        return "NT";
      case "XP":
        return "XP";
      case "NT 5.0":
        return "2000";
      case "NT 5.1":
        return "XP";
      case "NT 5.2":
        return "2003";
      case "NT 6.0":
        return "Vista";
      case "NT 6.1":
        return "7";
      case "NT 6.2":
        return "8";
      case "NT 6.3":
        return "8.1";
      case "NT 10.0":
        return "10";
      default:
        return;
    }
  }
  /**
   * Get macOS version name
   *    10.5 - Leopard
   *    10.6 - Snow Leopard
   *    10.7 - Lion
   *    10.8 - Mountain Lion
   *    10.9 - Mavericks
   *    10.10 - Yosemite
   *    10.11 - El Capitan
   *    10.12 - Sierra
   *    10.13 - High Sierra
   *    10.14 - Mojave
   *    10.15 - Catalina
   *
   * @example
   *   getMacOSVersionName("10.14") // 'Mojave'
   *
   * @param  {string} version
   * @return {string} versionName
   */
  static getMacOSVersionName(t) {
    const r = t.split(".").splice(0, 2).map((s) => parseInt(s, 10) || 0);
    if (r.push(0), r[0] === 10)
      switch (r[1]) {
        case 5:
          return "Leopard";
        case 6:
          return "Snow Leopard";
        case 7:
          return "Lion";
        case 8:
          return "Mountain Lion";
        case 9:
          return "Mavericks";
        case 10:
          return "Yosemite";
        case 11:
          return "El Capitan";
        case 12:
          return "Sierra";
        case 13:
          return "High Sierra";
        case 14:
          return "Mojave";
        case 15:
          return "Catalina";
        default:
          return;
      }
  }
  /**
   * Get Android version name
   *    1.5 - Cupcake
   *    1.6 - Donut
   *    2.0 - Eclair
   *    2.1 - Eclair
   *    2.2 - Froyo
   *    2.x - Gingerbread
   *    3.x - Honeycomb
   *    4.0 - Ice Cream Sandwich
   *    4.1 - Jelly Bean
   *    4.4 - KitKat
   *    5.x - Lollipop
   *    6.x - Marshmallow
   *    7.x - Nougat
   *    8.x - Oreo
   *    9.x - Pie
   *
   * @example
   *   getAndroidVersionName("7.0") // 'Nougat'
   *
   * @param  {string} version
   * @return {string} versionName
   */
  static getAndroidVersionName(t) {
    const r = t.split(".").splice(0, 2).map((s) => parseInt(s, 10) || 0);
    if (r.push(0), !(r[0] === 1 && r[1] < 5)) {
      if (r[0] === 1 && r[1] < 6)
        return "Cupcake";
      if (r[0] === 1 && r[1] >= 6)
        return "Donut";
      if (r[0] === 2 && r[1] < 2)
        return "Eclair";
      if (r[0] === 2 && r[1] === 2)
        return "Froyo";
      if (r[0] === 2 && r[1] > 2)
        return "Gingerbread";
      if (r[0] === 3)
        return "Honeycomb";
      if (r[0] === 4 && r[1] < 1)
        return "Ice Cream Sandwich";
      if (r[0] === 4 && r[1] < 4)
        return "Jelly Bean";
      if (r[0] === 4 && r[1] >= 4)
        return "KitKat";
      if (r[0] === 5)
        return "Lollipop";
      if (r[0] === 6)
        return "Marshmallow";
      if (r[0] === 7)
        return "Nougat";
      if (r[0] === 8)
        return "Oreo";
      if (r[0] === 9)
        return "Pie";
    }
  }
  /**
   * Get version precisions count
   *
   * @example
   *   getVersionPrecision("1.10.3") // 3
   *
   * @param  {string} version
   * @return {number}
   */
  static getVersionPrecision(t) {
    return t.split(".").length;
  }
  /**
   * Calculate browser version weight
   *
   * @example
   *   compareVersions('********',  '*******.90')    // 1
   *   compareVersions('*********', '********.90');  // 1
   *   compareVersions('********',  '********');     // 0
   *   compareVersions('********',  '1.0800.2');     // -1
   *   compareVersions('********',  '1.10',  true);  // 0
   *
   * @param {String} versionA versions versions to compare
   * @param {String} versionB versions versions to compare
   * @param {boolean} [isLoose] enable loose comparison
   * @return {Number} comparison result: -1 when versionA is lower,
   * 1 when versionA is bigger, 0 when both equal
   */
  /* eslint consistent-return: 1 */
  static compareVersions(t, r, s = false) {
    const n = _w.getVersionPrecision(t), i = _w.getVersionPrecision(r);
    let o = Math.max(n, i), a = 0;
    const c = _w.map([t, r], (u) => {
      const l = o - _w.getVersionPrecision(u), f = u + new Array(l + 1).join(".0");
      return _w.map(f.split("."), (p) => new Array(20 - p.length).join("0") + p).reverse();
    });
    for (s && (a = o - Math.min(n, i)), o -= 1; o >= a; ) {
      if (c[0][o] > c[1][o])
        return 1;
      if (c[0][o] === c[1][o]) {
        if (o === a)
          return 0;
        o -= 1;
      } else if (c[0][o] < c[1][o])
        return -1;
    }
  }
  /**
   * Array::map polyfill
   *
   * @param  {Array} arr
   * @param  {Function} iterator
   * @return {Array}
   */
  static map(t, r) {
    const s = [];
    let n;
    if (Array.prototype.map)
      return Array.prototype.map.call(t, r);
    for (n = 0; n < t.length; n += 1)
      s.push(r(t[n]));
    return s;
  }
  /**
   * Array::find polyfill
   *
   * @param  {Array} arr
   * @param  {Function} predicate
   * @return {Array}
   */
  static find(t, r) {
    let s, n;
    if (Array.prototype.find)
      return Array.prototype.find.call(t, r);
    for (s = 0, n = t.length; s < n; s += 1) {
      const i = t[s];
      if (r(i, s))
        return i;
    }
  }
  /**
   * Object::assign polyfill
   *
   * @param  {Object} obj
   * @param  {Object} ...objs
   * @return {Object}
   */
  static assign(t, ...r) {
    const s = t;
    let n, i;
    if (Object.assign)
      return Object.assign(t, ...r);
    for (n = 0, i = r.length; n < i; n += 1) {
      const o = r[n];
      typeof o == "object" && o !== null && Object.keys(o).forEach((c) => {
        s[c] = o[c];
      });
    }
    return t;
  }
  /**
   * Get short version/alias for a browser name
   *
   * @example
   *   getBrowserAlias('Microsoft Edge') // edge
   *
   * @param  {string} browserName
   * @return {string}
   */
  static getBrowserAlias(t) {
    return h0[t];
  }
  /**
   * Get short version/alias for a browser name
   *
   * @example
   *   getBrowserAlias('edge') // Microsoft Edge
   *
   * @param  {string} browserAlias
   * @return {string}
   */
  static getBrowserTypeByAlias(t) {
    return rc[t] || "";
  }
};
var $ = /version\/(\d+(\.?_?\d+)+)/i;
var p0 = [
  /* Googlebot */
  {
    test: [/googlebot/i],
    describe(e) {
      const t = {
        name: "Googlebot"
      }, r = w.getFirstMatch(/googlebot\/(\d+(\.\d+))/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  /* Opera < 13.0 */
  {
    test: [/opera/i],
    describe(e) {
      const t = {
        name: "Opera"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  /* Opera > 13.0 */
  {
    test: [/opr\/|opios/i],
    describe(e) {
      const t = {
        name: "Opera"
      }, r = w.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/SamsungBrowser/i],
    describe(e) {
      const t = {
        name: "Samsung Internet for Android"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/Whale/i],
    describe(e) {
      const t = {
        name: "NAVER Whale Browser"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/MZBrowser/i],
    describe(e) {
      const t = {
        name: "MZ Browser"
      }, r = w.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/focus/i],
    describe(e) {
      const t = {
        name: "Focus"
      }, r = w.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/swing/i],
    describe(e) {
      const t = {
        name: "Swing"
      }, r = w.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/coast/i],
    describe(e) {
      const t = {
        name: "Opera Coast"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/opt\/\d+(?:.?_?\d+)+/i],
    describe(e) {
      const t = {
        name: "Opera Touch"
      }, r = w.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/yabrowser/i],
    describe(e) {
      const t = {
        name: "Yandex Browser"
      }, r = w.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/ucbrowser/i],
    describe(e) {
      const t = {
        name: "UC Browser"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/Maxthon|mxios/i],
    describe(e) {
      const t = {
        name: "Maxthon"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/epiphany/i],
    describe(e) {
      const t = {
        name: "Epiphany"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/puffin/i],
    describe(e) {
      const t = {
        name: "Puffin"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/sleipnir/i],
    describe(e) {
      const t = {
        name: "Sleipnir"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/k-meleon/i],
    describe(e) {
      const t = {
        name: "K-Meleon"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/micromessenger/i],
    describe(e) {
      const t = {
        name: "WeChat"
      }, r = w.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/qqbrowser/i],
    describe(e) {
      const t = {
        name: /qqbrowserlite/i.test(e) ? "QQ Browser Lite" : "QQ Browser"
      }, r = w.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/msie|trident/i],
    describe(e) {
      const t = {
        name: "Internet Explorer"
      }, r = w.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/\sedg\//i],
    describe(e) {
      const t = {
        name: "Microsoft Edge"
      }, r = w.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/edg([ea]|ios)/i],
    describe(e) {
      const t = {
        name: "Microsoft Edge"
      }, r = w.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/vivaldi/i],
    describe(e) {
      const t = {
        name: "Vivaldi"
      }, r = w.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/seamonkey/i],
    describe(e) {
      const t = {
        name: "SeaMonkey"
      }, r = w.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/sailfish/i],
    describe(e) {
      const t = {
        name: "Sailfish"
      }, r = w.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/silk/i],
    describe(e) {
      const t = {
        name: "Amazon Silk"
      }, r = w.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/phantom/i],
    describe(e) {
      const t = {
        name: "PhantomJS"
      }, r = w.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/slimerjs/i],
    describe(e) {
      const t = {
        name: "SlimerJS"
      }, r = w.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/blackberry|\bbb\d+/i, /rim\stablet/i],
    describe(e) {
      const t = {
        name: "BlackBerry"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/(web|hpw)[o0]s/i],
    describe(e) {
      const t = {
        name: "WebOS Browser"
      }, r = w.getFirstMatch($, e) || w.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/bada/i],
    describe(e) {
      const t = {
        name: "Bada"
      }, r = w.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/tizen/i],
    describe(e) {
      const t = {
        name: "Tizen"
      }, r = w.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/qupzilla/i],
    describe(e) {
      const t = {
        name: "QupZilla"
      }, r = w.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/firefox|iceweasel|fxios/i],
    describe(e) {
      const t = {
        name: "Firefox"
      }, r = w.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/electron/i],
    describe(e) {
      const t = {
        name: "Electron"
      }, r = w.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/MiuiBrowser/i],
    describe(e) {
      const t = {
        name: "Miui"
      }, r = w.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/chromium/i],
    describe(e) {
      const t = {
        name: "Chromium"
      }, r = w.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i, e) || w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/chrome|crios|crmo/i],
    describe(e) {
      const t = {
        name: "Chrome"
      }, r = w.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  {
    test: [/GSA/i],
    describe(e) {
      const t = {
        name: "Google Search"
      }, r = w.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  /* Android Browser */
  {
    test(e) {
      const t = !e.test(/like android/i), r = e.test(/android/i);
      return t && r;
    },
    describe(e) {
      const t = {
        name: "Android Browser"
      }, r = w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  /* PlayStation 4 */
  {
    test: [/playstation 4/i],
    describe(e) {
      const t = {
        name: "PlayStation 4"
      }, r = w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  /* Safari */
  {
    test: [/safari|applewebkit/i],
    describe(e) {
      const t = {
        name: "Safari"
      }, r = w.getFirstMatch($, e);
      return r && (t.version = r), t;
    }
  },
  /* Something else */
  {
    test: [/.*/i],
    describe(e) {
      const t = /^(.*)\/(.*) /, r = /^(.*)\/(.*)[ \t]\((.*)/, n = e.search("\\(") !== -1 ? r : t;
      return {
        name: w.getFirstMatch(n, e),
        version: w.getSecondMatch(n, e)
      };
    }
  }
];
var g0 = [
  /* Roku */
  {
    test: [/Roku\/DVP/],
    describe(e) {
      const t = w.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i, e);
      return {
        name: ye.Roku,
        version: t
      };
    }
  },
  /* Windows Phone */
  {
    test: [/windows phone/i],
    describe(e) {
      const t = w.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i, e);
      return {
        name: ye.WindowsPhone,
        version: t
      };
    }
  },
  /* Windows */
  {
    test: [/windows /i],
    describe(e) {
      const t = w.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i, e), r = w.getWindowsVersionName(t);
      return {
        name: ye.Windows,
        version: t,
        versionName: r
      };
    }
  },
  /* Firefox on iPad */
  {
    test: [/Macintosh(.*?) FxiOS(.*?)\//],
    describe(e) {
      const t = {
        name: ye.iOS
      }, r = w.getSecondMatch(/(Version\/)(\d[\d.]+)/, e);
      return r && (t.version = r), t;
    }
  },
  /* macOS */
  {
    test: [/macintosh/i],
    describe(e) {
      const t = w.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i, e).replace(/[_\s]/g, "."), r = w.getMacOSVersionName(t), s = {
        name: ye.MacOS,
        version: t
      };
      return r && (s.versionName = r), s;
    }
  },
  /* iOS */
  {
    test: [/(ipod|iphone|ipad)/i],
    describe(e) {
      const t = w.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i, e).replace(/[_\s]/g, ".");
      return {
        name: ye.iOS,
        version: t
      };
    }
  },
  /* Android */
  {
    test(e) {
      const t = !e.test(/like android/i), r = e.test(/android/i);
      return t && r;
    },
    describe(e) {
      const t = w.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i, e), r = w.getAndroidVersionName(t), s = {
        name: ye.Android,
        version: t
      };
      return r && (s.versionName = r), s;
    }
  },
  /* WebOS */
  {
    test: [/(web|hpw)[o0]s/i],
    describe(e) {
      const t = w.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i, e), r = {
        name: ye.WebOS
      };
      return t && t.length && (r.version = t), r;
    }
  },
  /* BlackBerry */
  {
    test: [/blackberry|\bbb\d+/i, /rim\stablet/i],
    describe(e) {
      const t = w.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i, e) || w.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i, e) || w.getFirstMatch(/\bbb(\d+)/i, e);
      return {
        name: ye.BlackBerry,
        version: t
      };
    }
  },
  /* Bada */
  {
    test: [/bada/i],
    describe(e) {
      const t = w.getFirstMatch(/bada\/(\d+(\.\d+)*)/i, e);
      return {
        name: ye.Bada,
        version: t
      };
    }
  },
  /* Tizen */
  {
    test: [/tizen/i],
    describe(e) {
      const t = w.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i, e);
      return {
        name: ye.Tizen,
        version: t
      };
    }
  },
  /* Linux */
  {
    test: [/linux/i],
    describe() {
      return {
        name: ye.Linux
      };
    }
  },
  /* Chrome OS */
  {
    test: [/CrOS/],
    describe() {
      return {
        name: ye.ChromeOS
      };
    }
  },
  /* Playstation 4 */
  {
    test: [/PlayStation 4/],
    describe(e) {
      const t = w.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i, e);
      return {
        name: ye.PlayStation4,
        version: t
      };
    }
  }
];
var m0 = [
  /* Googlebot */
  {
    test: [/googlebot/i],
    describe() {
      return {
        type: "bot",
        vendor: "Google"
      };
    }
  },
  /* Huawei */
  {
    test: [/huawei/i],
    describe(e) {
      const t = w.getFirstMatch(/(can-l01)/i, e) && "Nova", r = {
        type: j.mobile,
        vendor: "Huawei"
      };
      return t && (r.model = t), r;
    }
  },
  /* Nexus Tablet */
  {
    test: [/nexus\s*(?:7|8|9|10).*/i],
    describe() {
      return {
        type: j.tablet,
        vendor: "Nexus"
      };
    }
  },
  /* iPad */
  {
    test: [/ipad/i],
    describe() {
      return {
        type: j.tablet,
        vendor: "Apple",
        model: "iPad"
      };
    }
  },
  /* Firefox on iPad */
  {
    test: [/Macintosh(.*?) FxiOS(.*?)\//],
    describe() {
      return {
        type: j.tablet,
        vendor: "Apple",
        model: "iPad"
      };
    }
  },
  /* Amazon Kindle Fire */
  {
    test: [/kftt build/i],
    describe() {
      return {
        type: j.tablet,
        vendor: "Amazon",
        model: "Kindle Fire HD 7"
      };
    }
  },
  /* Another Amazon Tablet with Silk */
  {
    test: [/silk/i],
    describe() {
      return {
        type: j.tablet,
        vendor: "Amazon"
      };
    }
  },
  /* Tablet */
  {
    test: [/tablet(?! pc)/i],
    describe() {
      return {
        type: j.tablet
      };
    }
  },
  /* iPod/iPhone */
  {
    test(e) {
      const t = e.test(/ipod|iphone/i), r = e.test(/like (ipod|iphone)/i);
      return t && !r;
    },
    describe(e) {
      const t = w.getFirstMatch(/(ipod|iphone)/i, e);
      return {
        type: j.mobile,
        vendor: "Apple",
        model: t
      };
    }
  },
  /* Nexus Mobile */
  {
    test: [/nexus\s*[0-6].*/i, /galaxy nexus/i],
    describe() {
      return {
        type: j.mobile,
        vendor: "Nexus"
      };
    }
  },
  /* Mobile */
  {
    test: [/[^-]mobi/i],
    describe() {
      return {
        type: j.mobile
      };
    }
  },
  /* BlackBerry */
  {
    test(e) {
      return e.getBrowserName(true) === "blackberry";
    },
    describe() {
      return {
        type: j.mobile,
        vendor: "BlackBerry"
      };
    }
  },
  /* Bada */
  {
    test(e) {
      return e.getBrowserName(true) === "bada";
    },
    describe() {
      return {
        type: j.mobile
      };
    }
  },
  /* Windows Phone */
  {
    test(e) {
      return e.getBrowserName() === "windows phone";
    },
    describe() {
      return {
        type: j.mobile,
        vendor: "Microsoft"
      };
    }
  },
  /* Android Tablet */
  {
    test(e) {
      const t = Number(String(e.getOSVersion()).split(".")[0]);
      return e.getOSName(true) === "android" && t >= 3;
    },
    describe() {
      return {
        type: j.tablet
      };
    }
  },
  /* Android Mobile */
  {
    test(e) {
      return e.getOSName(true) === "android";
    },
    describe() {
      return {
        type: j.mobile
      };
    }
  },
  /* desktop */
  {
    test(e) {
      return e.getOSName(true) === "macos";
    },
    describe() {
      return {
        type: j.desktop,
        vendor: "Apple"
      };
    }
  },
  /* Windows */
  {
    test(e) {
      return e.getOSName(true) === "windows";
    },
    describe() {
      return {
        type: j.desktop
      };
    }
  },
  /* Linux */
  {
    test(e) {
      return e.getOSName(true) === "linux";
    },
    describe() {
      return {
        type: j.desktop
      };
    }
  },
  /* PlayStation 4 */
  {
    test(e) {
      return e.getOSName(true) === "playstation 4";
    },
    describe() {
      return {
        type: j.tv
      };
    }
  },
  /* Roku */
  {
    test(e) {
      return e.getOSName(true) === "roku";
    },
    describe() {
      return {
        type: j.tv
      };
    }
  }
];
var y0 = [
  /* EdgeHTML */
  {
    test(e) {
      return e.getBrowserName(true) === "microsoft edge";
    },
    describe(e) {
      if (/\sedg\//i.test(e))
        return {
          name: Le.Blink
        };
      const r = w.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i, e);
      return {
        name: Le.EdgeHTML,
        version: r
      };
    }
  },
  /* Trident */
  {
    test: [/trident/i],
    describe(e) {
      const t = {
        name: Le.Trident
      }, r = w.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  /* Presto */
  {
    test(e) {
      return e.test(/presto/i);
    },
    describe(e) {
      const t = {
        name: Le.Presto
      }, r = w.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  /* Gecko */
  {
    test(e) {
      const t = e.test(/gecko/i), r = e.test(/like gecko/i);
      return t && !r;
    },
    describe(e) {
      const t = {
        name: Le.Gecko
      }, r = w.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  },
  /* Blink */
  {
    test: [/(apple)?webkit\/537\.36/i],
    describe() {
      return {
        name: Le.Blink
      };
    }
  },
  /* WebKit */
  {
    test: [/(apple)?webkit/i],
    describe(e) {
      const t = {
        name: Le.WebKit
      }, r = w.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i, e);
      return r && (t.version = r), t;
    }
  }
];
var no = class {
  /**
   * Create instance of Parser
   *
   * @param {String} UA User-Agent string
   * @param {Boolean} [skipParsing=false] parser can skip parsing in purpose of performance
   * improvements if you need to make a more particular parsing
   * like {@link Parser#parseBrowser} or {@link Parser#parsePlatform}
   *
   * @throw {Error} in case of empty UA String
   *
   * @constructor
   */
  constructor(t, r = false) {
    if (t == null || t === "")
      throw new Error("UserAgent parameter can't be empty");
    this._ua = t, this.parsedResult = {}, r !== true && this.parse();
  }
  /**
   * Get UserAgent string of current Parser instance
   * @return {String} User-Agent String of the current <Parser> object
   *
   * @public
   */
  getUA() {
    return this._ua;
  }
  /**
   * Test a UA string for a regexp
   * @param {RegExp} regex
   * @return {Boolean}
   */
  test(t) {
    return t.test(this._ua);
  }
  /**
   * Get parsed browser object
   * @return {Object}
   */
  parseBrowser() {
    this.parsedResult.browser = {};
    const t = w.find(p0, (r) => {
      if (typeof r.test == "function")
        return r.test(this);
      if (r.test instanceof Array)
        return r.test.some((s) => this.test(s));
      throw new Error("Browser's test function is not valid");
    });
    return t && (this.parsedResult.browser = t.describe(this.getUA())), this.parsedResult.browser;
  }
  /**
   * Get parsed browser object
   * @return {Object}
   *
   * @public
   */
  getBrowser() {
    return this.parsedResult.browser ? this.parsedResult.browser : this.parseBrowser();
  }
  /**
   * Get browser's name
   * @return {String} Browser's name or an empty string
   *
   * @public
   */
  getBrowserName(t) {
    return t ? String(this.getBrowser().name).toLowerCase() || "" : this.getBrowser().name || "";
  }
  /**
   * Get browser's version
   * @return {String} version of browser
   *
   * @public
   */
  getBrowserVersion() {
    return this.getBrowser().version;
  }
  /**
   * Get OS
   * @return {Object}
   *
   * @example
   * this.getOS();
   * {
   *   name: 'macOS',
   *   version: '10.11.12'
   * }
   */
  getOS() {
    return this.parsedResult.os ? this.parsedResult.os : this.parseOS();
  }
  /**
   * Parse OS and save it to this.parsedResult.os
   * @return {*|{}}
   */
  parseOS() {
    this.parsedResult.os = {};
    const t = w.find(g0, (r) => {
      if (typeof r.test == "function")
        return r.test(this);
      if (r.test instanceof Array)
        return r.test.some((s) => this.test(s));
      throw new Error("Browser's test function is not valid");
    });
    return t && (this.parsedResult.os = t.describe(this.getUA())), this.parsedResult.os;
  }
  /**
   * Get OS name
   * @param {Boolean} [toLowerCase] return lower-cased value
   * @return {String} name of the OS — macOS, Windows, Linux, etc.
   */
  getOSName(t) {
    const { name: r } = this.getOS();
    return t ? String(r).toLowerCase() || "" : r || "";
  }
  /**
   * Get OS version
   * @return {String} full version with dots ('10.11.12', '5.6', etc)
   */
  getOSVersion() {
    return this.getOS().version;
  }
  /**
   * Get parsed platform
   * @return {{}}
   */
  getPlatform() {
    return this.parsedResult.platform ? this.parsedResult.platform : this.parsePlatform();
  }
  /**
   * Get platform name
   * @param {Boolean} [toLowerCase=false]
   * @return {*}
   */
  getPlatformType(t = false) {
    const { type: r } = this.getPlatform();
    return t ? String(r).toLowerCase() || "" : r || "";
  }
  /**
   * Get parsed platform
   * @return {{}}
   */
  parsePlatform() {
    this.parsedResult.platform = {};
    const t = w.find(m0, (r) => {
      if (typeof r.test == "function")
        return r.test(this);
      if (r.test instanceof Array)
        return r.test.some((s) => this.test(s));
      throw new Error("Browser's test function is not valid");
    });
    return t && (this.parsedResult.platform = t.describe(this.getUA())), this.parsedResult.platform;
  }
  /**
   * Get parsed engine
   * @return {{}}
   */
  getEngine() {
    return this.parsedResult.engine ? this.parsedResult.engine : this.parseEngine();
  }
  /**
   * Get engines's name
   * @return {String} Engines's name or an empty string
   *
   * @public
   */
  getEngineName(t) {
    return t ? String(this.getEngine().name).toLowerCase() || "" : this.getEngine().name || "";
  }
  /**
   * Get parsed platform
   * @return {{}}
   */
  parseEngine() {
    this.parsedResult.engine = {};
    const t = w.find(y0, (r) => {
      if (typeof r.test == "function")
        return r.test(this);
      if (r.test instanceof Array)
        return r.test.some((s) => this.test(s));
      throw new Error("Browser's test function is not valid");
    });
    return t && (this.parsedResult.engine = t.describe(this.getUA())), this.parsedResult.engine;
  }
  /**
   * Parse full information about the browser
   * @returns {Parser}
   */
  parse() {
    return this.parseBrowser(), this.parseOS(), this.parsePlatform(), this.parseEngine(), this;
  }
  /**
   * Get parsed result
   * @return {ParsedResult}
   */
  getResult() {
    return w.assign({}, this.parsedResult);
  }
  /**
   * Check if parsed browser matches certain conditions
   *
   * @param {Object} checkTree It's one or two layered object,
   * which can include a platform or an OS on the first layer
   * and should have browsers specs on the bottom-laying layer
   *
   * @returns {Boolean|undefined} Whether the browser satisfies the set conditions or not.
   * Returns `undefined` when the browser is no described in the checkTree object.
   *
   * @example
   * const browser = Bowser.getParser(window.navigator.userAgent);
   * if (browser.satisfies({chrome: '>118.01.1322' }))
   * // or with os
   * if (browser.satisfies({windows: { chrome: '>118.01.1322' } }))
   * // or with platforms
   * if (browser.satisfies({desktop: { chrome: '>118.01.1322' } }))
   */
  satisfies(t) {
    const r = {};
    let s = 0;
    const n = {};
    let i = 0;
    if (Object.keys(t).forEach((a) => {
      const c = t[a];
      typeof c == "string" ? (n[a] = c, i += 1) : typeof c == "object" && (r[a] = c, s += 1);
    }), s > 0) {
      const a = Object.keys(r), c = w.find(a, (l) => this.isOS(l));
      if (c) {
        const l = this.satisfies(r[c]);
        if (l !== void 0)
          return l;
      }
      const u = w.find(
        a,
        (l) => this.isPlatform(l)
      );
      if (u) {
        const l = this.satisfies(r[u]);
        if (l !== void 0)
          return l;
      }
    }
    if (i > 0) {
      const a = Object.keys(n), c = w.find(a, (u) => this.isBrowser(u, true));
      if (c !== void 0)
        return this.compareVersion(n[c]);
    }
  }
  /**
   * Check if the browser name equals the passed string
   * @param browserName The string to compare with the browser name
   * @param [includingAlias=false] The flag showing whether alias will be included into comparison
   * @returns {boolean}
   */
  isBrowser(t, r = false) {
    const s = this.getBrowserName().toLowerCase();
    let n = t.toLowerCase();
    const i = w.getBrowserTypeByAlias(n);
    return r && i && (n = i.toLowerCase()), n === s;
  }
  compareVersion(t) {
    let r = [0], s = t, n = false;
    const i = this.getBrowserVersion();
    if (typeof i == "string")
      return t[0] === ">" || t[0] === "<" ? (s = t.substr(1), t[1] === "=" ? (n = true, s = t.substr(2)) : r = [], t[0] === ">" ? r.push(1) : r.push(-1)) : t[0] === "=" ? s = t.substr(1) : t[0] === "~" && (n = true, s = t.substr(1)), r.indexOf(
        w.compareVersions(i, s, n)
      ) > -1;
  }
  isOS(t) {
    return this.getOSName(true) === String(t).toLowerCase();
  }
  isPlatform(t) {
    return this.getPlatformType(true) === String(t).toLowerCase();
  }
  isEngine(t) {
    return this.getEngineName(true) === String(t).toLowerCase();
  }
  /**
   * Is anything? Check if the browser is called "anything",
   * the OS called "anything" or the platform called "anything"
   * @param {String} anything
   * @param [includingAlias=false] The flag showing whether alias will be included into comparison
   * @returns {Boolean}
   */
  is(t, r = false) {
    return this.isBrowser(t, r) || this.isOS(t) || this.isPlatform(t);
  }
  /**
   * Check if any of the given values satisfies this.is(anything)
   * @param {String[]} anythings
   * @returns {Boolean}
   */
  some(t = []) {
    return t.some((r) => this.is(r));
  }
};
var sc = class {
  /**
   * Creates a {@link Parser} instance
   *
   * @param {String} UA UserAgent string
   * @param {Boolean} [skipParsing=false] Will make the Parser postpone parsing until you ask it
   * explicitly. Same as `skipParsing` for {@link Parser}.
   * @returns {Parser}
   * @throws {Error} when UA is not a String
   *
   * @example
   * const parser = Bowser.getParser(window.navigator.userAgent);
   * const result = parser.getResult();
   */
  static getParser(t, r = false) {
    if (typeof t != "string")
      throw new Error("UserAgent should be a string");
    return new no(t, r);
  }
  /**
   * Creates a {@link Parser} instance and runs {@link Parser.getResult} immediately
   *
   * @param UA
   * @return {ParsedResult}
   *
   * @example
   * const result = Bowser.parse(window.navigator.userAgent);
   */
  static parse(t) {
    return new no(t).getResult();
  }
  static get BROWSER_MAP() {
    return rc;
  }
  static get ENGINE_MAP() {
    return Le;
  }
  static get OS_MAP() {
    return ye;
  }
  static get PLATFORMS_MAP() {
    return j;
  }
};
var w0 = ({ serviceId: e, clientVersion: t }) => async (r) => {
  var o, a, c, u, l, f;
  const s = typeof window < "u" && ((o = window == null ? void 0 : window.navigator) != null && o.userAgent) ? sc.parse(window.navigator.userAgent) : void 0, n = [
    ["aws-sdk-js", t],
    ["ua", "2.1"],
    [`os/${((a = s == null ? void 0 : s.os) == null ? void 0 : a.name) || "other"}`, (c = s == null ? void 0 : s.os) == null ? void 0 : c.version],
    ["lang/js"],
    ["md/browser", `${((u = s == null ? void 0 : s.browser) == null ? void 0 : u.name) ?? "unknown"}_${((l = s == null ? void 0 : s.browser) == null ? void 0 : l.version) ?? "unknown"}`]
  ];
  e && n.push([`api/${e}`, t]);
  const i = await ((f = r == null ? void 0 : r.userAgentAppId) == null ? void 0 : f.call(r));
  return i && n.push([`app/${i}`]), n;
};
var Tt = class _Tt {
  constructor(t) {
    if (this.bytes = t, t.byteLength !== 8)
      throw new Error("Int64 buffers must be exactly 8 bytes");
  }
  static fromNumber(t) {
    if (t > 9223372036854776e3 || t < -9223372036854776e3)
      throw new Error(`${t} is too large (or, if negative, too small) to represent as an Int64`);
    const r = new Uint8Array(8);
    for (let s = 7, n = Math.abs(Math.round(t)); s > -1 && n > 0; s--, n /= 256)
      r[s] = n;
    return t < 0 && io(r), new _Tt(r);
  }
  valueOf() {
    const t = this.bytes.slice(0), r = t[0] & 128;
    return r && io(t), parseInt(Ee(t), 16) * (r ? -1 : 1);
  }
  toString() {
    return String(this.valueOf());
  }
};
function io(e) {
  for (let t = 0; t < 8; t++)
    e[t] ^= 255;
  for (let t = 7; t > -1 && (e[t]++, e[t] === 0); t--)
    ;
}
var b0 = class {
  constructor(t, r) {
    this.toUtf8 = t, this.fromUtf8 = r;
  }
  format(t) {
    const r = [];
    for (const i of Object.keys(t)) {
      const o = this.fromUtf8(i);
      r.push(Uint8Array.from([o.byteLength]), o, this.formatHeaderValue(t[i]));
    }
    const s = new Uint8Array(r.reduce((i, o) => i + o.byteLength, 0));
    let n = 0;
    for (const i of r)
      s.set(i, n), n += i.byteLength;
    return s;
  }
  formatHeaderValue(t) {
    switch (t.type) {
      case "boolean":
        return Uint8Array.from([t.value ? 0 : 1]);
      case "byte":
        return Uint8Array.from([2, t.value]);
      case "short":
        const r = new DataView(new ArrayBuffer(3));
        return r.setUint8(0, 3), r.setInt16(1, t.value, false), new Uint8Array(r.buffer);
      case "integer":
        const s = new DataView(new ArrayBuffer(5));
        return s.setUint8(0, 4), s.setInt32(1, t.value, false), new Uint8Array(s.buffer);
      case "long":
        const n = new Uint8Array(9);
        return n[0] = 5, n.set(t.value.bytes, 1), n;
      case "binary":
        const i = new DataView(new ArrayBuffer(3 + t.value.byteLength));
        i.setUint8(0, 6), i.setUint16(1, t.value.byteLength, false);
        const o = new Uint8Array(i.buffer);
        return o.set(t.value, 3), o;
      case "string":
        const a = this.fromUtf8(t.value), c = new DataView(new ArrayBuffer(3 + a.byteLength));
        c.setUint8(0, 7), c.setUint16(1, a.byteLength, false);
        const u = new Uint8Array(c.buffer);
        return u.set(a, 3), u;
      case "timestamp":
        const l = new Uint8Array(9);
        return l[0] = 8, l.set(Tt.fromNumber(t.value.valueOf()).bytes, 1), l;
      case "uuid":
        if (!T0.test(t.value))
          throw new Error(`Invalid UUID received: ${t.value}`);
        const f = new Uint8Array(17);
        return f[0] = 9, f.set(mo(t.value.replace(/\-/g, "")), 1), f;
    }
  }
  parse(t) {
    const r = {};
    let s = 0;
    for (; s < t.byteLength; ) {
      const n = t.getUint8(s++), i = this.toUtf8(new Uint8Array(t.buffer, t.byteOffset + s, n));
      switch (s += n, t.getUint8(s++)) {
        case 0:
          r[i] = {
            type: ao,
            value: true
          };
          break;
        case 1:
          r[i] = {
            type: ao,
            value: false
          };
          break;
        case 2:
          r[i] = {
            type: E0,
            value: t.getInt8(s++)
          };
          break;
        case 3:
          r[i] = {
            type: x0,
            value: t.getInt16(s, false)
          }, s += 2;
          break;
        case 4:
          r[i] = {
            type: S0,
            value: t.getInt32(s, false)
          }, s += 4;
          break;
        case 5:
          r[i] = {
            type: A0,
            value: new Tt(new Uint8Array(t.buffer, t.byteOffset + s, 8))
          }, s += 8;
          break;
        case 6:
          const o = t.getUint16(s, false);
          s += 2, r[i] = {
            type: C0,
            value: new Uint8Array(t.buffer, t.byteOffset + s, o)
          }, s += o;
          break;
        case 7:
          const a = t.getUint16(s, false);
          s += 2, r[i] = {
            type: v0,
            value: this.toUtf8(new Uint8Array(t.buffer, t.byteOffset + s, a))
          }, s += a;
          break;
        case 8:
          r[i] = {
            type: R0,
            value: new Date(new Tt(new Uint8Array(t.buffer, t.byteOffset + s, 8)).valueOf())
          }, s += 8;
          break;
        case 9:
          const c = new Uint8Array(t.buffer, t.byteOffset + s, 16);
          s += 16, r[i] = {
            type: k0,
            value: `${Ee(c.subarray(0, 4))}-${Ee(c.subarray(4, 6))}-${Ee(c.subarray(6, 8))}-${Ee(c.subarray(8, 10))}-${Ee(c.subarray(10))}`
          };
          break;
        default:
          throw new Error("Unrecognized header type tag");
      }
    }
    return r;
  }
};
var oo;
(function(e) {
  e[e.boolTrue = 0] = "boolTrue", e[e.boolFalse = 1] = "boolFalse", e[e.byte = 2] = "byte", e[e.short = 3] = "short", e[e.integer = 4] = "integer", e[e.long = 5] = "long", e[e.byteArray = 6] = "byteArray", e[e.string = 7] = "string", e[e.timestamp = 8] = "timestamp", e[e.uuid = 9] = "uuid";
})(oo || (oo = {}));
var ao = "boolean";
var E0 = "byte";
var x0 = "short";
var S0 = "integer";
var A0 = "long";
var C0 = "binary";
var v0 = "string";
var R0 = "timestamp";
var k0 = "uuid";
var T0 = /^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/;
var nc = 4;
var He = nc * 2;
var Ze = 4;
var B0 = He + Ze * 2;
function N0({ byteLength: e, byteOffset: t, buffer: r }) {
  if (e < B0)
    throw new Error("Provided message too short to accommodate event stream message overhead");
  const s = new DataView(r, t, e), n = s.getUint32(0, false);
  if (e !== n)
    throw new Error("Reported message length does not match received message length");
  const i = s.getUint32(nc, false), o = s.getUint32(He, false), a = s.getUint32(e - Ze, false), c = new Zt().update(new Uint8Array(r, t, He));
  if (o !== c.digest())
    throw new Error(`The prelude checksum specified in the message (${o}) does not match the calculated CRC32 checksum (${c.digest()})`);
  if (c.update(new Uint8Array(r, t + He, e - (He + Ze))), a !== c.digest())
    throw new Error(`The message checksum (${c.digest()}) did not match the expected value of ${a}`);
  return {
    headers: new DataView(r, t + He + Ze, i),
    body: new Uint8Array(r, t + He + Ze + i, n - i - (He + Ze + Ze))
  };
}
var I0 = class {
  constructor(t, r) {
    this.headerMarshaller = new b0(t, r), this.messageBuffer = [], this.isEndOfStream = false;
  }
  feed(t) {
    this.messageBuffer.push(this.decode(t));
  }
  endOfStream() {
    this.isEndOfStream = true;
  }
  getMessage() {
    const t = this.messageBuffer.pop(), r = this.isEndOfStream;
    return {
      getMessage() {
        return t;
      },
      isEndOfStream() {
        return r;
      }
    };
  }
  getAvailableMessages() {
    const t = this.messageBuffer;
    this.messageBuffer = [];
    const r = this.isEndOfStream;
    return {
      getMessages() {
        return t;
      },
      isEndOfStream() {
        return r;
      }
    };
  }
  encode({ headers: t, body: r }) {
    const s = this.headerMarshaller.format(t), n = s.byteLength + r.byteLength + 16, i = new Uint8Array(n), o = new DataView(i.buffer, i.byteOffset, i.byteLength), a = new Zt();
    return o.setUint32(0, n, false), o.setUint32(4, s.byteLength, false), o.setUint32(8, a.update(i.subarray(0, 8)).digest(), false), i.set(s, 12), i.set(r, s.byteLength + 12), o.setUint32(n - 4, a.update(i.subarray(8, n - 4)).digest(), false), i;
  }
  decode(t) {
    const { headers: r, body: s } = N0(t);
    return { headers: this.headerMarshaller.parse(r), body: s };
  }
  formatHeaders(t) {
    return this.headerMarshaller.format(t);
  }
};
var P0 = class {
  constructor(t) {
    this.options = t;
  }
  [Symbol.asyncIterator]() {
    return this.asyncIterator();
  }
  async *asyncIterator() {
    for await (const t of this.options.inputStream)
      yield this.options.decoder.decode(t);
  }
};
var _0 = class {
  constructor(t) {
    this.options = t;
  }
  [Symbol.asyncIterator]() {
    return this.asyncIterator();
  }
  async *asyncIterator() {
    for await (const t of this.options.messageStream)
      yield this.options.encoder.encode(t);
    this.options.includeEndFrame && (yield new Uint8Array(0));
  }
};
var M0 = class {
  constructor(t) {
    this.options = t;
  }
  [Symbol.asyncIterator]() {
    return this.asyncIterator();
  }
  async *asyncIterator() {
    for await (const t of this.options.messageStream) {
      const r = await this.options.deserializer(t);
      r !== void 0 && (yield r);
    }
  }
};
var O0 = class {
  constructor(t) {
    this.options = t;
  }
  [Symbol.asyncIterator]() {
    return this.asyncIterator();
  }
  async *asyncIterator() {
    for await (const t of this.options.inputStream)
      yield this.options.serializer(t);
  }
};
function F0(e) {
  let t = 0, r = 0, s = null, n = null;
  const i = (a) => {
    if (typeof a != "number")
      throw new Error("Attempted to allocate an event message where size was not a number: " + a);
    t = a, r = 4, s = new Uint8Array(a), new DataView(s.buffer).setUint32(0, a, false);
  }, o = async function* () {
    const a = e[Symbol.asyncIterator]();
    for (; ; ) {
      const { value: c, done: u } = await a.next();
      if (u) {
        if (t)
          if (t === r)
            yield s;
          else
            throw new Error("Truncated event message received.");
        else
          return;
        return;
      }
      const l = c.length;
      let f = 0;
      for (; f < l; ) {
        if (!s) {
          const g = l - f;
          n || (n = new Uint8Array(4));
          const E = Math.min(4 - r, g);
          if (n.set(c.slice(f, f + E), r), r += E, f += E, r < 4)
            break;
          i(new DataView(n.buffer).getUint32(0, false)), n = null;
        }
        const p = Math.min(t - r, l - f);
        s.set(c.slice(f, f + p), r), r += p, f += p, t && t === r && (yield s, s = null, t = 0, r = 0);
      }
    }
  };
  return {
    [Symbol.asyncIterator]: o
  };
}
function D0(e, t) {
  return async function(r) {
    const { value: s } = r.headers[":message-type"];
    if (s === "error") {
      const n = new Error(r.headers[":error-message"].value || "UnknownError");
      throw n.name = r.headers[":error-code"].value, n;
    } else if (s === "exception") {
      const n = r.headers[":exception-type"].value, i = { [n]: r }, o = await e(i);
      if (o.$unknown) {
        const a = new Error(t(r.body));
        throw a.name = n, a;
      }
      throw o[n];
    } else if (s === "event") {
      const n = {
        [r.headers[":event-type"].value]: r
      }, i = await e(n);
      return i.$unknown ? void 0 : i;
    } else
      throw Error(`Unrecognizable event type: ${r.headers[":event-type"].value}`);
  };
}
var U0 = class {
  constructor({ utf8Encoder: t, utf8Decoder: r }) {
    this.eventStreamCodec = new I0(t, r), this.utfEncoder = t;
  }
  deserialize(t, r) {
    const s = F0(t);
    return new M0({
      messageStream: new P0({ inputStream: s, decoder: this.eventStreamCodec }),
      deserializer: D0(r, this.utfEncoder)
    });
  }
  serialize(t, r) {
    return new _0({
      messageStream: new O0({ inputStream: t, serializer: r }),
      encoder: this.eventStreamCodec,
      includeEndFrame: true
    });
  }
};
var $0 = (e) => ({
  [Symbol.asyncIterator]: async function* () {
    const t = e.getReader();
    try {
      for (; ; ) {
        const { done: r, value: s } = await t.read();
        if (r)
          return;
        yield s;
      }
    } finally {
      t.releaseLock();
    }
  }
});
var L0 = (e) => {
  const t = e[Symbol.asyncIterator]();
  return new ReadableStream({
    async pull(r) {
      const { done: s, value: n } = await t.next();
      if (s)
        return r.close();
      r.enqueue(n);
    }
  });
};
var H0 = class {
  constructor({ utf8Encoder: t, utf8Decoder: r }) {
    this.universalMarshaller = new U0({
      utf8Decoder: r,
      utf8Encoder: t
    });
  }
  deserialize(t, r) {
    const s = z0(t) ? $0(t) : t;
    return this.universalMarshaller.deserialize(s, r);
  }
  serialize(t, r) {
    const s = this.universalMarshaller.serialize(t, r);
    return typeof ReadableStream == "function" ? L0(s) : s;
  }
};
var z0 = (e) => typeof ReadableStream == "function" && e instanceof ReadableStream;
var q0 = (e) => new H0(e);
async function V0(e, t, r = 1024 * 1024) {
  const s = e.size;
  let n = 0;
  for (; n < s; ) {
    const i = e.slice(n, Math.min(s, n + r));
    t(new Uint8Array(await i.arrayBuffer())), n += i.size;
  }
}
var j0 = async function(t, r) {
  const s = new t();
  return await V0(r, (n) => {
    s.update(n);
  }), s.digest();
};
var W0 = (e) => () => Promise.reject(e);
var $e = 64;
var G0 = 16;
var K0 = [1732584193, 4023233417, 2562383102, 271733878];
var X0 = class {
  constructor() {
    this.reset();
  }
  update(t) {
    if (Z0(t))
      return;
    if (this.finished)
      throw new Error("Attempted to update an already finished hash.");
    const r = Q0(t);
    let s = 0, { byteLength: n } = r;
    for (this.bytesHashed += n; n > 0; )
      this.buffer.setUint8(this.bufferLength++, r[s++]), n--, this.bufferLength === $e && (this.hashBuffer(), this.bufferLength = 0);
  }
  async digest() {
    if (!this.finished) {
      const { buffer: r, bufferLength: s, bytesHashed: n } = this, i = n * 8;
      if (r.setUint8(this.bufferLength++, 128), s % $e >= $e - 8) {
        for (let o = this.bufferLength; o < $e; o++)
          r.setUint8(o, 0);
        this.hashBuffer(), this.bufferLength = 0;
      }
      for (let o = this.bufferLength; o < $e - 8; o++)
        r.setUint8(o, 0);
      r.setUint32($e - 8, i >>> 0, true), r.setUint32($e - 4, Math.floor(i / 4294967296), true), this.hashBuffer(), this.finished = true;
    }
    const t = new DataView(new ArrayBuffer(G0));
    for (let r = 0; r < 4; r++)
      t.setUint32(r * 4, this.state[r], true);
    return new Uint8Array(t.buffer, t.byteOffset, t.byteLength);
  }
  hashBuffer() {
    const { buffer: t, state: r } = this;
    let s = r[0], n = r[1], i = r[2], o = r[3];
    s = ae(s, n, i, o, t.getUint32(0, true), 7, 3614090360), o = ae(o, s, n, i, t.getUint32(4, true), 12, 3905402710), i = ae(i, o, s, n, t.getUint32(8, true), 17, 606105819), n = ae(n, i, o, s, t.getUint32(12, true), 22, 3250441966), s = ae(s, n, i, o, t.getUint32(16, true), 7, 4118548399), o = ae(o, s, n, i, t.getUint32(20, true), 12, 1200080426), i = ae(i, o, s, n, t.getUint32(24, true), 17, 2821735955), n = ae(n, i, o, s, t.getUint32(28, true), 22, 4249261313), s = ae(s, n, i, o, t.getUint32(32, true), 7, 1770035416), o = ae(o, s, n, i, t.getUint32(36, true), 12, 2336552879), i = ae(i, o, s, n, t.getUint32(40, true), 17, 4294925233), n = ae(n, i, o, s, t.getUint32(44, true), 22, 2304563134), s = ae(s, n, i, o, t.getUint32(48, true), 7, 1804603682), o = ae(o, s, n, i, t.getUint32(52, true), 12, 4254626195), i = ae(i, o, s, n, t.getUint32(56, true), 17, 2792965006), n = ae(n, i, o, s, t.getUint32(60, true), 22, 1236535329), s = ce(s, n, i, o, t.getUint32(4, true), 5, 4129170786), o = ce(o, s, n, i, t.getUint32(24, true), 9, 3225465664), i = ce(i, o, s, n, t.getUint32(44, true), 14, 643717713), n = ce(n, i, o, s, t.getUint32(0, true), 20, 3921069994), s = ce(s, n, i, o, t.getUint32(20, true), 5, 3593408605), o = ce(o, s, n, i, t.getUint32(40, true), 9, 38016083), i = ce(i, o, s, n, t.getUint32(60, true), 14, 3634488961), n = ce(n, i, o, s, t.getUint32(16, true), 20, 3889429448), s = ce(s, n, i, o, t.getUint32(36, true), 5, 568446438), o = ce(o, s, n, i, t.getUint32(56, true), 9, 3275163606), i = ce(i, o, s, n, t.getUint32(12, true), 14, 4107603335), n = ce(n, i, o, s, t.getUint32(32, true), 20, 1163531501), s = ce(s, n, i, o, t.getUint32(52, true), 5, 2850285829), o = ce(o, s, n, i, t.getUint32(8, true), 9, 4243563512), i = ce(i, o, s, n, t.getUint32(28, true), 14, 1735328473), n = ce(n, i, o, s, t.getUint32(48, true), 20, 2368359562), s = ue(s, n, i, o, t.getUint32(20, true), 4, 4294588738), o = ue(o, s, n, i, t.getUint32(32, true), 11, 2272392833), i = ue(i, o, s, n, t.getUint32(44, true), 16, 1839030562), n = ue(n, i, o, s, t.getUint32(56, true), 23, 4259657740), s = ue(s, n, i, o, t.getUint32(4, true), 4, 2763975236), o = ue(o, s, n, i, t.getUint32(16, true), 11, 1272893353), i = ue(i, o, s, n, t.getUint32(28, true), 16, 4139469664), n = ue(n, i, o, s, t.getUint32(40, true), 23, 3200236656), s = ue(s, n, i, o, t.getUint32(52, true), 4, 681279174), o = ue(o, s, n, i, t.getUint32(0, true), 11, 3936430074), i = ue(i, o, s, n, t.getUint32(12, true), 16, 3572445317), n = ue(n, i, o, s, t.getUint32(24, true), 23, 76029189), s = ue(s, n, i, o, t.getUint32(36, true), 4, 3654602809), o = ue(o, s, n, i, t.getUint32(48, true), 11, 3873151461), i = ue(i, o, s, n, t.getUint32(60, true), 16, 530742520), n = ue(n, i, o, s, t.getUint32(8, true), 23, 3299628645), s = de(s, n, i, o, t.getUint32(0, true), 6, 4096336452), o = de(o, s, n, i, t.getUint32(28, true), 10, 1126891415), i = de(i, o, s, n, t.getUint32(56, true), 15, 2878612391), n = de(n, i, o, s, t.getUint32(20, true), 21, 4237533241), s = de(s, n, i, o, t.getUint32(48, true), 6, 1700485571), o = de(o, s, n, i, t.getUint32(12, true), 10, 2399980690), i = de(i, o, s, n, t.getUint32(40, true), 15, 4293915773), n = de(n, i, o, s, t.getUint32(4, true), 21, 2240044497), s = de(s, n, i, o, t.getUint32(32, true), 6, 1873313359), o = de(o, s, n, i, t.getUint32(60, true), 10, 4264355552), i = de(i, o, s, n, t.getUint32(24, true), 15, 2734768916), n = de(n, i, o, s, t.getUint32(52, true), 21, 1309151649), s = de(s, n, i, o, t.getUint32(16, true), 6, 4149444226), o = de(o, s, n, i, t.getUint32(44, true), 10, 3174756917), i = de(i, o, s, n, t.getUint32(8, true), 15, 718787259), n = de(n, i, o, s, t.getUint32(36, true), 21, 3951481745), r[0] = s + r[0] & 4294967295, r[1] = n + r[1] & 4294967295, r[2] = i + r[2] & 4294967295, r[3] = o + r[3] & 4294967295;
  }
  reset() {
    this.state = Uint32Array.from(K0), this.buffer = new DataView(new ArrayBuffer($e)), this.bufferLength = 0, this.bytesHashed = 0, this.finished = false;
  }
};
function mr(e, t, r, s, n, i) {
  return t = (t + e & 4294967295) + (s + i & 4294967295) & 4294967295, (t << n | t >>> 32 - n) + r & 4294967295;
}
function ae(e, t, r, s, n, i, o) {
  return mr(t & r | ~t & s, e, t, n, i, o);
}
function ce(e, t, r, s, n, i, o) {
  return mr(t & s | r & ~s, e, t, n, i, o);
}
function ue(e, t, r, s, n, i, o) {
  return mr(t ^ r ^ s, e, t, n, i, o);
}
function de(e, t, r, s, n, i, o) {
  return mr(r ^ (t | ~s), e, t, n, i, o);
}
function Z0(e) {
  return typeof e == "string" ? e.length === 0 : e.byteLength === 0;
}
function Q0(e) {
  return typeof e == "string" ? et(e) : ArrayBuffer.isView(e) ? new Uint8Array(e.buffer, e.byteOffset, e.byteLength / Uint8Array.BYTES_PER_ELEMENT) : new Uint8Array(e);
}
var co = typeof TextEncoder == "function" ? new TextEncoder() : null;
var Y0 = (e) => {
  if (typeof e == "string") {
    if (co)
      return co.encode(e).byteLength;
    let t = e.length;
    for (let r = t - 1; r >= 0; r--) {
      const s = e.charCodeAt(r);
      s > 127 && s <= 2047 ? t++ : s > 2047 && s <= 65535 && (t += 2), s >= 56320 && s <= 57343 && r--;
    }
    return t;
  } else {
    if (typeof e.byteLength == "number")
      return e.byteLength;
    if (typeof e.size == "number")
      return e.size;
  }
  throw new Error(`Body Length computation failed for ${e}`);
};
var J0 = (e) => ({
  apiVersion: "2006-03-01",
  base64Decoder: (e == null ? void 0 : e.base64Decoder) ?? xs,
  base64Encoder: (e == null ? void 0 : e.base64Encoder) ?? ir,
  disableHostPrefix: (e == null ? void 0 : e.disableHostPrefix) ?? false,
  endpointProvider: (e == null ? void 0 : e.endpointProvider) ?? Ma,
  extensions: (e == null ? void 0 : e.extensions) ?? [],
  getAwsChunkedEncodingStream: (e == null ? void 0 : e.getAwsChunkedEncodingStream) ?? jc,
  httpAuthSchemeProvider: (e == null ? void 0 : e.httpAuthSchemeProvider) ?? Qp,
  httpAuthSchemes: (e == null ? void 0 : e.httpAuthSchemes) ?? [
    {
      schemeId: "aws.auth#sigv4",
      identityProvider: (t) => t.getIdentityProvider("aws.auth#sigv4"),
      signer: new lo()
    },
    {
      schemeId: "aws.auth#sigv4a",
      identityProvider: (t) => t.getIdentityProvider("aws.auth#sigv4a"),
      signer: new Sc()
    }
  ],
  logger: (e == null ? void 0 : e.logger) ?? new vs(),
  sdkStreamMixin: (e == null ? void 0 : e.sdkStreamMixin) ?? Jc,
  serviceId: (e == null ? void 0 : e.serviceId) ?? "S3",
  signerConstructor: (e == null ? void 0 : e.signerConstructor) ?? _p,
  signingEscapePath: (e == null ? void 0 : e.signingEscapePath) ?? false,
  urlParser: (e == null ? void 0 : e.urlParser) ?? Yt,
  useArnRegion: (e == null ? void 0 : e.useArnRegion) ?? false,
  utf8Decoder: (e == null ? void 0 : e.utf8Decoder) ?? et,
  utf8Encoder: (e == null ? void 0 : e.utf8Encoder) ?? Ss
});
var ey = ["in-region", "cross-region", "mobile", "standard", "legacy"];
var ty = ({ defaultsMode: e } = {}) => uu(async () => {
  const t = typeof e == "function" ? await e() : e;
  switch (t == null ? void 0 : t.toLowerCase()) {
    case "auto":
      return Promise.resolve(ry() ? "mobile" : "standard");
    case "mobile":
    case "in-region":
    case "cross-region":
    case "standard":
    case "legacy":
      return Promise.resolve(t == null ? void 0 : t.toLocaleLowerCase());
    case void 0:
      return Promise.resolve("legacy");
    default:
      throw new Error(`Invalid parameter for "defaultsMode", expect ${ey.join(", ")}, got ${t}`);
  }
});
var ry = () => {
  var r, s;
  const e = typeof window < "u" && ((r = window == null ? void 0 : window.navigator) != null && r.userAgent) ? sc.parse(window.navigator.userAgent) : void 0, t = (s = e == null ? void 0 : e.platform) == null ? void 0 : s.type;
  return t === "tablet" || t === "mobile";
};
var sy = (e) => {
  const t = ty(e), r = () => t().then(od), s = J0(e);
  return {
    ...s,
    ...e,
    runtime: "browser",
    defaultsMode: t,
    bodyLengthChecker: (e == null ? void 0 : e.bodyLengthChecker) ?? Y0,
    credentialDefaultProvider: (e == null ? void 0 : e.credentialDefaultProvider) ?? ((n) => () => Promise.reject(new Error("Credential is missing"))),
    defaultUserAgentProvider: (e == null ? void 0 : e.defaultUserAgentProvider) ?? w0({ serviceId: s.serviceId, clientVersion: Zm.version }),
    eventStreamSerdeProvider: (e == null ? void 0 : e.eventStreamSerdeProvider) ?? q0,
    maxAttempts: (e == null ? void 0 : e.maxAttempts) ?? Ms,
    md5: (e == null ? void 0 : e.md5) ?? X0,
    region: (e == null ? void 0 : e.region) ?? W0("Region is missing"),
    requestHandler: As.create((e == null ? void 0 : e.requestHandler) ?? r),
    retryMode: (e == null ? void 0 : e.retryMode) ?? (async () => (await r()).retryMode || rp),
    sha1: (e == null ? void 0 : e.sha1) ?? s0,
    sha256: (e == null ? void 0 : e.sha256) ?? f0,
    streamCollector: (e == null ? void 0 : e.streamCollector) ?? po,
    streamHasher: (e == null ? void 0 : e.streamHasher) ?? j0,
    useDualstackEndpoint: (e == null ? void 0 : e.useDualstackEndpoint) ?? (() => Promise.resolve(Dh)),
    useFipsEndpoint: (e == null ? void 0 : e.useFipsEndpoint) ?? (() => Promise.resolve(Uh))
  };
};
var ny = (e) => {
  let t = async () => {
    if (e.region === void 0)
      throw new Error("Region is missing from runtimeConfig");
    const r = e.region;
    return typeof r == "string" ? r : r();
  };
  return {
    setRegion(r) {
      t = r;
    },
    region() {
      return t;
    }
  };
};
var iy = (e) => ({
  region: e.region()
});
var oy = (e) => {
  const t = e.httpAuthSchemes;
  let r = e.httpAuthSchemeProvider, s = e.credentials;
  return {
    setHttpAuthScheme(n) {
      const i = t.findIndex((o) => o.schemeId === n.schemeId);
      i === -1 ? t.push(n) : t.splice(i, 1, n);
    },
    httpAuthSchemes() {
      return t;
    },
    setHttpAuthSchemeProvider(n) {
      r = n;
    },
    httpAuthSchemeProvider() {
      return r;
    },
    setCredentials(n) {
      s = n;
    },
    credentials() {
      return s;
    }
  };
};
var ay = (e) => ({
  httpAuthSchemes: e.httpAuthSchemes(),
  httpAuthSchemeProvider: e.httpAuthSchemeProvider(),
  credentials: e.credentials()
});
var Ht = (e) => e;
var cy = (e, t) => {
  const r = {
    ...Ht(ny(e)),
    ...Ht(ld(e)),
    ...Ht(fc(e)),
    ...Ht(oy(e))
  };
  return t.forEach((s) => s.configure(r)), {
    ...e,
    ...iy(r),
    ...fd(r),
    ...hc(r),
    ...ay(r)
  };
};
var uy = class extends Fu {
  constructor(...[r]) {
    const s = sy(r || {}), n = Jp(s), i = eh(n), o = of(i), a = Ap(o), c = $h(a), u = c, l = tp(u), f = Lh(l), p = Yp(f), g = Lf(p, { session: [() => this, Im] }), E = cy(g, (r == null ? void 0 : r.extensions) || []);
    super(E);
    _(this, "config");
    this.config = E, this.middlewareStack.use(Fh(this.config)), this.middlewareStack.use(Np(this.config)), this.middlewareStack.use(qh(this.config)), this.middlewareStack.use(uf(this.config)), this.middlewareStack.use(ff(this.config)), this.middlewareStack.use(yf(this.config)), this.middlewareStack.use(Rc(this.config, {
      httpAuthSchemeParametersProvider: Kp,
      identityProviderConfigProvider: async (k) => new nu({
        "aws.auth#sigv4": k.credentials,
        "aws.auth#sigv4a": k.credentials
      })
    })), this.middlewareStack.use(_c(this.config)), this.middlewareStack.use(Qf(this.config)), this.middlewareStack.use(yc(this.config)), this.middlewareStack.use(kf(this.config)), this.middlewareStack.use(Mf(this.config)), this.middlewareStack.use($f(this.config));
  }
  destroy() {
    super.destroy();
  }
};
function dy(e) {
  return (t) => async (r) => {
    const s = { ...r.input }, n = [
      {
        target: "SSECustomerKey",
        hash: "SSECustomerKeyMD5"
      },
      {
        target: "CopySourceSSECustomerKey",
        hash: "CopySourceSSECustomerKeyMD5"
      }
    ];
    for (const i of n) {
      const o = s[i.target];
      if (o) {
        let a;
        typeof o == "string" ? hy(o, e) ? a = e.base64Decoder(o) : (a = e.utf8Decoder(o), s[i.target] = e.base64Encoder(a)) : (a = ArrayBuffer.isView(o) ? new Uint8Array(o.buffer, o.byteOffset, o.byteLength) : new Uint8Array(o), s[i.target] = e.base64Encoder(a));
        const c = new e.md5();
        c.update(a), s[i.hash] = e.base64Encoder(await c.digest());
      }
    }
    return t({
      ...r,
      input: s
    });
  };
}
var ly = {
  name: "ssecMiddleware",
  step: "initialize",
  tags: ["SSE"],
  override: true
};
var fy = (e) => ({
  applyToStack: (t) => {
    t.add(dy(e), ly);
  }
});
function hy(e, t) {
  if (!/^(?:[A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/.test(e))
    return false;
  try {
    return t.base64Decoder(e).length === 32;
  } catch {
    return false;
  }
}
var py = class extends Cs.classBuilder().ep({
  ...Da,
  Bucket: { type: "contextParams", name: "Bucket" },
  Key: { type: "contextParams", name: "Key" }
}).m(function(t, r, s, n) {
  return [
    fo(s, this.serialize, this.deserialize),
    la(s, t.getEndpointParameterInstructions()),
    nf(s, {
      requestAlgorithmMember: { httpHeader: "x-amz-sdk-checksum-algorithm", name: "ChecksumAlgorithm" },
      requestChecksumRequired: false
    }),
    Sf(),
    Qo(s),
    fy(s)
  ];
}).s("AmazonS3", "PutObject", {}).n("S3Client", "PutObjectCommand").f(ng, sg).ser(og).de(cg).build() {
};
var gy = axios_default.create({});
async function my(e, t, r) {
  return e.getSignedUrl || console.error("请先配置uploader.getSignedUrl，该方法应该从后端获取签名url"), await e.getSignedUrl(e.bucket, t, e, r);
}
async function yy(e, t) {
  const { file: r, onProgress: s, options: n } = e, o = await my(n, t, "put"), a = decodeURIComponent(o);
  return await gy.put(a, r, {
    onUploadProgress: (c) => {
      const { loaded: u, total: l } = c;
      s({ percent: Math.round(u * 100 / l) });
    }
  });
}
async function wy(e) {
  const { file: t, fileName: r, onProgress: s, options: n } = e, i = n, o = new uy({
    ...(i == null ? void 0 : i.sdkOpts) || {}
  }), a = await vt(t, r, i);
  async function c() {
    const u = { url: i.sdkOpts.endpoint + "/" + i.bucket + "/" + a, key: a };
    return i.successHandle ? await i.successHandle(u) : u;
  }
  if (i.getSignedUrl)
    await yy(e, a);
  else {
    const u = {
      Bucket: i.bucket,
      Key: a
      // The name of the object. For example, 'sample_upload.txt'.
    };
    await o.send(new py({ Body: t, ...u }));
  }
  return await c();
}
async function Ty(e) {
  const { getConfig: t } = bt(), r = t("s3"), s = e.options, n = merge_default(cloneDeep_default(r), s);
  return e.options = n, await wy(e);
}
export {
  my as buildSignedUrl,
  Ty as upload,
  yy as uploadUsingSignedUrl
};
/*! Bundled license information:

@fast-crud/fast-extends/dist/uploader-s3-5e18a75c.mjs:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
//# sourceMappingURL=uploader-s3-5e18a75c-MJKVQNOL.js.map
