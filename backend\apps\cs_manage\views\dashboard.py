"""
CS_MANAGE 仪表盘视图

提供数据卡片和图表数据的API接口
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from django.db.models import Sum, Avg, Count, Q, F, Case, When, IntegerField, FloatField, OuterRef, Subquery
from django.db.models.functions import Coalesce, ExtractHour, TruncDate
from django.utils import timezone
from django.utils.dateparse import parse_datetime
# from django.utils.timezone import make_aware  # USE_TZ=False时不需要
from django.forms.models import model_to_dict
from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.views import APIView

from dvadmin.utils.json_response import SuccessResponse, ErrorResponse, DetailResponse
from dvadmin.utils.viewset import CustomModelViewSet
from apps.cs_manage.models import DailyMetricGroup, CsEmotionAnalysis
from apps.cs_manage.services.qiyu_client import QiyuDataClient
from apps.common.qiyu_service import get_qiyu_service
# from apps.cs_manage.serializers import DashboardOverviewSerializer
from apps.kcs.models import Game, Link, Article
import json

logger = logging.getLogger(__name__)


class CsManageDashboardViewSet(CustomModelViewSet):
    """
    CS_MANAGE 仪表盘视图集
    
    提供数据卡片、图表数据和筛选选项
    """
    
    queryset = DailyMetricGroup.objects.all()
    
    def get_queryset(self):
        """重写queryset以支持筛选"""
        queryset = super().get_queryset()
        
        # 日期筛选
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(date__gte=start_date)
        if end_date:
            queryset = queryset.filter(date__lte=end_date)
            
        # 组别筛选
        group_id = self.request.query_params.get('group_id')
        if group_id is not None:
            queryset = queryset.filter(group_id=group_id)
            
        return queryset.order_by('-date')
    
    @action(detail=False, methods=['post'])
    def overview(self, request):
        """
        获取仪表盘概览数据

        包括数据卡片和筛选选项
        支持多选客服组
        """
        try:
            # 从POST请求体获取参数
            data = request.data
            start_date = data.get('start_date')
            end_date = data.get('end_date')
            group_ids = data.get('group_ids')

            # 兼容旧的单选参数
            if not group_ids:
                group_id = data.get('group_id')
                if group_id is not None:
                    try:
                        group_ids = [int(group_id)]
                    except (ValueError, TypeError):
                        group_ids = None

            # 确保group_ids是列表
            if group_ids and not isinstance(group_ids, list):
                try:
                    group_ids = [int(group_ids)]
                except (ValueError, TypeError):
                    group_ids = None

            logger.info(f"[仪表盘] 接收到参数: start_date={start_date}, end_date={end_date}, group_ids={group_ids}")

            # 默认获取昨天的数据
            if not start_date and not end_date:
                yesterday = (timezone.now() - timedelta(days=1)).date()
                start_date = end_date = yesterday.isoformat()

            # 获取可用的组别选项
            groups = self._get_available_groups()

            # 获取数据卡片
            cards_data = self._get_cards_data(start_date, end_date, group_ids)

            # 获取图表数据
            charts_data = self._get_charts_data(start_date, end_date, group_ids)

            return SuccessResponse(data={
                'cards': cards_data,
                'charts': charts_data,
                'filters': {
                    'groups': groups,
                    'current_filters': {
                        'start_date': start_date,
                        'end_date': end_date,
                        'group_ids': group_ids
                    }
                }
            })
            
        except Exception as e:
            logger.error(f"[仪表盘] 获取概览数据失败: {str(e)}")
            return ErrorResponse(msg=f"获取数据失败: {str(e)}")
    
    def _get_available_groups(self) -> List[Dict]:
        """获取可用的组别选项（从数据库中获取，不调用API）"""
        try:
            # 获取最近30天内有数据的组别
            recent_date = timezone.now().date() - timedelta(days=30)

            # 从数据库中直接获取组别信息（group_id和group_name）
            available_groups = DailyMetricGroup.objects.filter(
                date__gte=recent_date
            ).values('group_id', 'group_name').distinct()

            groups = [{'id': 0, 'name': '全部'}]  # 添加全部选项

            # 添加有数据的组别
            for group in available_groups:
                group_id = group['group_id']
                group_name = group['group_name']

                if group_id != 0:  # 排除全部组别，因为已经添加了
                    # 如果group_name为空，使用默认名称
                    if not group_name:
                        group_name = f"组别{group_id}"

                    groups.append({
                        'id': group_id,
                        'name': group_name
                    })

            # 按名称排序
            groups[1:] = sorted(groups[1:], key=lambda x: x['name'])

            logger.info(f"[仪表盘] 从数据库获取到 {len(groups)} 个有数据的组别")
            return groups

        except Exception as e:
            logger.error(f"[仪表盘] 获取组别列表失败: {str(e)}")
            return [{'id': 0, 'name': '全部'}]
    
    def _get_cards_data(self, start_date: str, end_date: str, group_ids: List[int] = None) -> Dict:
        """获取数据卡片数据"""
        try:
            # 1. 获取基础指标数据
            metrics_data = self._get_metrics_data(start_date, end_date, group_ids)

            # 2. 获取情绪数据
            emotion_data = self._get_emotion_data(start_date, end_date, group_ids)

            # 3. 合并数据
            return {
                **metrics_data,
                'emotion_score': emotion_data.get('avg_emotion_score', 0)
            }

        except Exception as e:
            logger.error(f"[仪表盘] 获取卡片数据失败: {str(e)}")
            return self._get_empty_cards_data()

    def _get_metrics_data(self, start_date: str, end_date: str, group_ids: List[int] = None) -> Dict:
        """获取基础指标数据"""
        try:
            # 构建查询条件
            queryset = DailyMetricGroup.objects.filter(
                date__gte=start_date,
                date__lte=end_date
            )

            # 处理多选组别
            if group_ids is not None and len(group_ids) > 0:
                # 如果包含0（全部），获取汇总数据（group_id=0）
                if 0 in group_ids:
                    queryset = queryset.filter(group_id=0)
                else:
                    # 多选特定组别，查询这些组别的数据进行聚合
                    queryset = queryset.filter(group_id__in=group_ids)
                    logger.info(f"[仪表盘] 筛选组别: {group_ids}")
            else:
                # 默认获取汇总数据（group_id=0）
                queryset = queryset.filter(group_id=0)
                logger.info(f"[仪表盘] 使用默认汇总数据")

            # 聚合计算（只聚合存储的字段）
            aggregated = queryset.aggregate(
                total_sessions=Sum('total_sessions'),
                ai_sessions=Sum('ai_sessions'),
                manual_sessions=Sum('manual_sessions'),
                worksheet_count=Sum('worksheet_count'),
                ai_transfer_sum=Sum('ai_transfer')
            )

            # 对于从raw数据获取的字段，需要特殊处理
            # 获取查询结果用于计算属性字段的平均值
            records = list(queryset.all())

            # 计算属性字段的平均值
            if records:
                avg_satisfaction = sum(record.manual_satisfaction for record in records) / len(records)
                avg_fcr = sum(record.fcr_ratio for record in records) / len(records)
                avg_resp_30 = sum(record.resp_30_ratio for record in records) / len(records)
                avg_first_resp = sum(record.avg_first_resp for record in records) / len(records)
                avg_online_ratio = sum(record.online_ratio for record in records) / len(records)
                total_eva_count = sum(record.eva_count for record in records)
                total_invite_count = sum(record.invite_count for record in records)
            else:
                avg_satisfaction = avg_fcr = avg_resp_30 = avg_first_resp = avg_online_ratio = 0
                total_eva_count = total_invite_count = 0
            
            # 计算AI转人工率
            total_sessions = aggregated['total_sessions'] or 0
            ai_sessions = aggregated['ai_sessions'] or 0
            manual_sessions = aggregated['manual_sessions'] or 0
            ai_transfer_sum = aggregated['ai_transfer_sum'] or 0
            ai_transfer_rate = 0
            if ai_sessions > 0:
                ai_transfer_rate = (ai_transfer_sum / ai_sessions) * 100

            return {
                'total_sessions': total_sessions,
                'ai_sessions': ai_sessions,
                'manual_sessions': manual_sessions,
                'worksheet_count': aggregated['worksheet_count'] or 0,
                'ai_transfer_rate': round(ai_transfer_rate, 2),
                'manual_satisfaction': round(avg_satisfaction, 2),
                'fcr_ratio': round(avg_fcr, 2),
                'resp_30_ratio': round(avg_resp_30, 2),
                'avg_first_resp': int(avg_first_resp),
                'online_ratio': round(avg_online_ratio, 2),
                'eva_count': int(total_eva_count),
                'invite_count': int(total_invite_count),
            }

        except Exception as e:
            logger.error(f"[仪表盘] 获取基础指标数据失败: {str(e)}")
            return self._get_empty_metrics_data()

    def _get_emotion_data(self, start_date: str, end_date: str, group_ids: List[int] = None) -> Dict:
        """获取情绪数据（支持按组别筛选）"""
        try:
            # 构建情绪数据查询条件
            emotion_queryset = CsEmotionAnalysis.objects.filter(
                create_datetime__date__gte=start_date,
                create_datetime__date__lte=end_date,
                status='completed'
            )

            # 根据组别筛选情绪数据
            if group_ids and 0 not in group_ids:
                # 获取组别名称列表
                group_names = []
                for group_id in group_ids:
                    group_name = self._get_group_name_by_id(str(group_id))
                    if group_name:
                        group_names.append(group_name)

                if group_names:
                    emotion_queryset = emotion_queryset.filter(group_name__in=group_names)
                else:
                    # 无法找到组别名称，返回空数据
                    return {'avg_emotion_score': 0}

            # 计算平均情绪分值
            emotion_count = emotion_queryset.count()
            if emotion_count == 0:
                return {'avg_emotion_score': 0}

            avg_score = emotion_queryset.aggregate(
                avg_score=Avg('emotion_change_score')
            )['avg_score'] or 0

            return {'avg_emotion_score': round(avg_score, 2)}

        except Exception as e:
            logger.error(f"[仪表盘] 获取情绪数据失败: {str(e)}")
            return {'avg_emotion_score': 0}

    def _get_group_name_by_id(self, group_id: str) -> Optional[str]:
        """根据组别ID获取组别名称（从数据库获取，不调用API）"""
        try:
            # 从数据库中获取组别名称
            group_record = DailyMetricGroup.objects.filter(
                group_id=int(group_id)
            ).values('group_name').first()

            if group_record and group_record['group_name']:
                return group_record['group_name']

            # 如果数据库中没有找到，返回默认名称
            return f"组别{group_id}"

        except Exception as e:
            logger.error(f"[仪表盘] 获取组别名称失败: {str(e)}")
            return f"组别{group_id}"

    def _get_empty_cards_data(self) -> Dict:
        """获取空的卡片数据"""
        return {
            **self._get_empty_metrics_data(),
            'emotion_score': 0
        }

    def _get_empty_metrics_data(self) -> Dict:
        """获取空的基础指标数据"""
        return {
            'total_sessions': 0,
            'ai_sessions': 0,
            'manual_sessions': 0,
            'worksheet_count': 0,
            'ai_transfer_rate': 0,
            'manual_satisfaction': 0,
            'fcr_ratio': 0,
            'resp_30_ratio': 0,
            'avg_first_resp': 0,
            'online_ratio': 0,
            'eva_count': 0,
            'invite_count': 0,
        }

    def _get_group_trend_data(self, start_date: str, end_date: str, group_ids: List[int] = None) -> Dict:
        """获取按客服组分组的趋势数据（从数据库获取）"""
        try:
            # 查询指定日期范围内所有组别的数据
            # 排除group_id=0的汇总数据，以及AI客服组数据
            queryset = DailyMetricGroup.objects.filter(
                date__gte=start_date,
                date__lte=end_date,
                group_id__gt=0  # 排除全部组别的汇总数据
            ).exclude(
                group_name__icontains='AI'  # 排除AI客服组
            ).exclude(
                group_name__icontains='ai'  # 排除ai客服组（小写）
            ).exclude(
                group_name__icontains='智能'  # 排除智能客服组
            ).exclude(
                group_name__icontains='机器人'  # 排除机器人客服组
            )

            # 如果指定了特定的组别，只查询这些组别
            if group_ids and 0 not in group_ids:
                queryset = queryset.filter(group_id__in=group_ids)

            queryset = queryset.order_by('date', 'group_id')

            # 获取数据记录
            records = list(queryset.select_related())

            if not records:
                return {'ai_chart': {}, 'manual_chart': {}}

            # 按日期分组数据
            date_data = {}  # {date: {group_id: record}}
            all_group_ids = set()
            all_dates = set()

            for record in records:
                date_str = record.date.strftime('%Y-%m-%d')
                all_dates.add(date_str)
                all_group_ids.add(record.group_id)

                if date_str not in date_data:
                    date_data[date_str] = {}

                date_data[date_str][record.group_id] = record

            # 排序日期和组别
            sorted_dates = sorted(list(all_dates))
            sorted_group_ids = sorted(list(all_group_ids))

            # 格式化日期显示（MM-DD格式）
            from datetime import datetime
            formatted_dates = []
            for date_str in sorted_dates:
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                formatted_dates.append(f"{date_obj.month:02d}-{date_obj.day:02d}")

            # 构建AI会话图表数据
            ai_series = []
            for group_id in sorted_group_ids:
                group_ai_data = []
                group_name = f"组别{group_id}"  # 默认名称

                for date_str in sorted_dates:
                    if date_str in date_data and group_id in date_data[date_str]:
                        record = date_data[date_str][group_id]
                        group_ai_data.append(record.ai_sessions)
                        # 获取组别名称（使用第一个找到的记录的名称）
                        if record.group_name:
                            group_name = record.group_name
                    else:
                        group_ai_data.append(0)

                ai_series.append({
                    'name': group_name,
                    'data': group_ai_data,
                    'type': 'bar',
                    'stack': 'ai_total'
                })

            # 构建人工会话图表数据
            manual_series = []
            for group_id in sorted_group_ids:
                group_manual_data = []
                group_name = f"组别{group_id}"  # 默认名称

                for date_str in sorted_dates:
                    if date_str in date_data and group_id in date_data[date_str]:
                        record = date_data[date_str][group_id]
                        group_manual_data.append(record.manual_sessions)
                        # 获取组别名称
                        if record.group_name:
                            group_name = record.group_name
                    else:
                        group_manual_data.append(0)

                manual_series.append({
                    'name': group_name,
                    'data': group_manual_data,
                    'type': 'bar',
                    'stack': 'manual_total'
                })

            return {
                'ai_chart': {
                    'dates': formatted_dates,
                    'series': ai_series,
                    'title': '各客服组AI会话量趋势'
                },
                'manual_chart': {
                    'dates': formatted_dates,
                    'series': manual_series,
                    'title': '各客服组人工会话量趋势'
                }
            }

        except Exception as e:
            logger.error(f"[仪表盘] 获取客服组趋势数据失败: {str(e)}")
            return {'ai_chart': {}, 'manual_chart': {}}

    def _get_game_trend_data(self, start_date: str, end_date: str) -> Dict:
        """获取按游戏分组的趋势数据"""
        try:
            # 从七鱼API获取会话历史数据
            client = QiyuDataClient()

            # 按日期获取会话数据
            from datetime import datetime, timedelta
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')

            game_data = {}  # {date: {game_name: {ai_sessions: x, manual_sessions: y}}}
            dates = []

            # 遍历日期范围
            current_date = start_dt
            while current_date <= end_dt:
                date_str = current_date.strftime('%Y-%m-%d')
                dates.append(current_date.strftime('%m-%d'))

                try:
                    # 获取当日会话数据
                    sessions = client.get_session_history_by_date(current_date)

                    # 按游戏分组统计
                    daily_game_data = {}
                    for session in sessions:
                        if session.get('isValid') != 1:
                            continue

                        # 从session中提取游戏信息（可能需要根据实际数据结构调整）
                        game_name = self._extract_game_name_from_session(session)
                        if not game_name:
                            game_name = '未知游戏'

                        if game_name not in daily_game_data:
                            daily_game_data[game_name] = {'ai_sessions': 0, 'manual_sessions': 0}

                        # 判断是否为AI会话（使用统一入口）
                        from ..services.qiyu_client import QiyuDataClient
                        if QiyuDataClient.is_ai_session(session):
                            daily_game_data[game_name]['ai_sessions'] += 1
                        else:
                            daily_game_data[game_name]['manual_sessions'] += 1

                    game_data[date_str] = daily_game_data

                except Exception as e:
                    logger.warning(f"[仪表盘] 获取 {date_str} 游戏数据失败: {str(e)}")
                    game_data[date_str] = {}

                current_date += timedelta(days=1)

            # 格式化为图表数据
            return self._format_game_trend_data(game_data, dates)

        except Exception as e:
            logger.error(f"[仪表盘] 获取游戏趋势数据失败: {str(e)}")
            return {'ai_chart': {}, 'manual_chart': {}}

    def _extract_game_name_from_session(self, session: Dict) -> Optional[str]:
        """从会话数据中提取游戏名称"""
        # 这里需要根据实际的session数据结构来提取游戏信息
        # 可能的字段：gameId, gameName, productName等

        # 方法1：从URL或参数中提取
        session_ext = session.get('sessionExt', {})
        if isinstance(session_ext, dict):
            # 检查是否有游戏相关字段
            game_info = session_ext.get('gameInfo') or session_ext.get('gameName')
            if game_info:
                return str(game_info)

        # 方法2：从其他字段提取
        game_name = session.get('gameName') or session.get('productName')
        if game_name:
            return str(game_name)

        # 方法3：根据业务规则推断（可能需要根据实际情况调整）
        # 例如从客服组名称推断游戏
        route = session.get('route', '')
        if '太空' in route or 'taikong' in route.lower():
            return '太空行动'
        elif '超自然' in route or 'czr' in route.lower():
            return '超自然'

        return None



    def _format_game_trend_data(self, game_data: Dict, dates: List[str]) -> Dict:
        """格式化游戏趋势数据为图表格式"""
        try:
            # 收集所有游戏名称
            all_games = set()
            for daily_data in game_data.values():
                all_games.update(daily_data.keys())

            all_games = sorted(list(all_games))

            # 构建AI会话图表数据
            ai_series = []
            for game in all_games:
                game_ai_data = []
                for i, date_key in enumerate(game_data.keys()):
                    daily_data = game_data[date_key]
                    ai_count = daily_data.get(game, {}).get('ai_sessions', 0)
                    game_ai_data.append(ai_count)

                ai_series.append({
                    'name': game,
                    'data': game_ai_data,
                    'type': 'bar',
                    'stack': 'ai_total'
                })

            # 构建人工会话图表数据
            manual_series = []
            for game in all_games:
                game_manual_data = []
                for i, date_key in enumerate(game_data.keys()):
                    daily_data = game_data[date_key]
                    manual_count = daily_data.get(game, {}).get('manual_sessions', 0)
                    game_manual_data.append(manual_count)

                manual_series.append({
                    'name': game,
                    'data': game_manual_data,
                    'type': 'bar',
                    'stack': 'manual_total'
                })

            return {
                'ai_chart': {
                    'dates': dates,
                    'series': ai_series,
                    'title': '各游戏AI会话量趋势'
                },
                'manual_chart': {
                    'dates': dates,
                    'series': manual_series,
                    'title': '各游戏人工会话量趋势'
                }
            }

        except Exception as e:
            logger.error(f"[仪表盘] 格式化游戏趋势数据失败: {str(e)}")
            return {'ai_chart': {}, 'manual_chart': {}}
    
    def _get_emotion_score(self, start_date: str, end_date: str) -> Optional[float]:
        """
        实时计算情绪分值（仅全部组别有效）

        不从DailyMetricGroup存储，而是实时从CsEmotionAnalysis计算
        这样避免了重复存储，保持数据的实时性
        """
        try:
            from apps.cs_manage.models import CsEmotionAnalysis

            # 检查是否有情绪分析数据
            emotion_queryset = CsEmotionAnalysis.objects.filter(
                create_datetime__date__gte=start_date,
                create_datetime__date__lte=end_date,
                status='completed'
            )

            emotion_count = emotion_queryset.count()
            if emotion_count == 0:
                logger.info(f"[仪表盘] {start_date} 到 {end_date} 期间无情绪分析数据")
                return None

            # 计算平均情绪变化分值（这是最有意义的指标）
            emotion_data = emotion_queryset.aggregate(
                avg_emotion_change=Avg('emotion_change_score')
            )

            avg_emotion = emotion_data['avg_emotion_change']
            logger.info(f"[仪表盘] {start_date} 到 {end_date} 期间情绪分析: {emotion_count}条记录，平均变化: {avg_emotion}")

            return round(avg_emotion, 2) if avg_emotion is not None else None

        except Exception as e:
            logger.error(f"[仪表盘] 获取情绪数据失败: {str(e)}")
            return None
    
    def _get_charts_data(self, start_date: str, end_date: str, group_ids: List[int] = None) -> Dict:
        """获取图表数据"""
        try:
            # 构建查询条件
            queryset = DailyMetricGroup.objects.filter(
                date__gte=start_date,
                date__lte=end_date
            )

            # 处理多选组别
            if group_ids is not None and len(group_ids) > 0:
                # 如果包含0（全部），获取汇总数据（group_id=0）
                if 0 in group_ids:
                    queryset = queryset.filter(group_id=0)
                else:
                    # 多选特定组别，查询这些组别的数据进行聚合
                    queryset = queryset.filter(group_id__in=group_ids)
                    logger.info(f"[仪表盘] 图表筛选组别: {group_ids}")
            else:
                # 默认获取汇总数据（group_id=0）
                queryset = queryset.filter(group_id=0)
                logger.info(f"[仪表盘] 图表使用默认汇总数据")

            # 获取基础数据（只查询存储的字段）
            records = list(queryset.select_related().order_by('date'))

            # 构建图表数据（使用属性方法获取计算字段）
            daily_data = []
            for record in records:
                daily_data.append({
                    'date': record.date,
                    'total_sessions': record.total_sessions,
                    'ai_sessions': record.ai_sessions,
                    'manual_sessions': record.manual_sessions,
                    'worksheet_count': record.worksheet_count,
                    'avg_satisfaction': record.manual_satisfaction,  # 使用属性方法
                    'avg_fcr': record.fcr_ratio,  # 使用属性方法
                    'avg_resp_30': record.resp_30_ratio,  # 使用属性方法
                })

            # 获取情绪数据（仅全部组别或多选时）
            emotion_data = {}
            if not group_ids or 0 in group_ids:
                emotion_data = self._get_emotion_chart_data(start_date, end_date)

            # 获取按客服组分组的趋势数据（仅全部组别或多选时）
            group_trend_data = {}
            if not group_ids or 0 in group_ids or (group_ids and len(group_ids) > 1):
                group_trend_data = self._get_group_trend_data(start_date, end_date, group_ids)

            return {
                'session_trend': self._format_session_trend_data(daily_data),
                'worksheet_trend': self._format_worksheet_trend_data(daily_data),
                'quality_metrics': self._format_quality_metrics_data(daily_data),
                'emotion_trend': emotion_data,
                'group_trend': group_trend_data
            }

        except Exception as e:
            logger.error(f"[仪表盘] 获取图表数据失败: {str(e)}")
            return {}
    
    def _format_session_trend_data(self, daily_data: List[Dict]) -> Dict:
        """格式化会话趋势数据"""
        # 如果没有数据，返回空结构
        if not daily_data:
            return {
                'dates': [],
                'series': [
                    {'name': '总会话量', 'data': [], 'type': 'bar', 'stack': 'total'},
                    {'name': 'AI会话', 'data': [], 'type': 'bar', 'stack': 'total'},
                    {'name': '人工会话', 'data': [], 'type': 'bar', 'stack': 'total'}
                ]
            }

        # 提取日期和数据
        dates = []
        total_sessions = []
        ai_sessions = []
        manual_sessions = []

        for item in daily_data:
            # 格式化日期为 MM-DD 格式，更适合图表显示
            date_obj = item['date']
            formatted_date = f"{date_obj.month:02d}-{date_obj.day:02d}"
            dates.append(formatted_date)

            total_sessions.append(item['total_sessions'] or 0)
            ai_sessions.append(item['ai_sessions'] or 0)
            manual_sessions.append(item['manual_sessions'] or 0)

        return {
            'dates': dates,
            'series': [
                {'name': 'AI会话', 'data': ai_sessions, 'type': 'bar', 'stack': 'total'},
                {'name': '人工会话', 'data': manual_sessions, 'type': 'bar', 'stack': 'total'}
            ]
        }

    def _format_worksheet_trend_data(self, daily_data: List[Dict]) -> Dict:
        """格式化工单趋势数据"""
        # 如果没有数据，返回空结构
        if not daily_data:
            return {
                'dates': [],
                'series': [
                    {'name': '工单量', 'data': [], 'type': 'line'}
                ]
            }

        # 提取日期和数据
        dates = []
        worksheet_counts = []

        for item in daily_data:
            # 格式化日期为 MM-DD 格式，更适合图表显示
            date_obj = item['date']
            formatted_date = f"{date_obj.month:02d}-{date_obj.day:02d}"
            dates.append(formatted_date)

            worksheet_counts.append(item.get('worksheet_count', 0) or 0)

        return {
            'dates': dates,
            'series': [
                {
                    'name': '工单量',
                    'data': worksheet_counts,
                    'type': 'line'
                }
            ]
        }

    def _format_quality_metrics_data(self, daily_data: List[Dict]) -> Dict:
        """格式化质量指标数据"""
        # 如果没有数据，返回空结构
        if not daily_data:
            return {
                'dates': [],
                'series': [
                    {'name': '满意度(%)', 'data': [], 'type': 'line'},
                    {'name': 'FCR(%)', 'data': [], 'type': 'line'},
                    {'name': '30秒应答率(%)', 'data': [], 'type': 'line'}
                ]
            }

        # 提取日期和数据
        dates = []
        satisfaction = []
        fcr = []
        resp_30 = []

        for item in daily_data:
            # 格式化日期为 MM-DD 格式
            date_obj = item['date']
            formatted_date = f"{date_obj.month:02d}-{date_obj.day:02d}"
            dates.append(formatted_date)

            # 使用新的字段名
            satisfaction.append(round(item.get('avg_satisfaction', 0) or 0, 2))
            fcr.append(round(item.get('avg_fcr', 0) or 0, 2))
            resp_30.append(round(item.get('avg_resp_30', 0) or 0, 2))

        return {
            'dates': dates,
            'series': [
                {'name': '满意度(%)', 'data': satisfaction, 'type': 'line'},
                {'name': 'FCR(%)', 'data': fcr, 'type': 'line'},
                {'name': '30秒应答率(%)', 'data': resp_30, 'type': 'line'}
            ]
        }
    
    def _get_emotion_chart_data(self, start_date: str, end_date: str) -> Dict:
        """获取情绪趋势图表数据"""
        try:
            # 检查是否有情绪数据
            emotion_count = CsEmotionAnalysis.objects.filter(
                create_datetime__date__gte=start_date,
                create_datetime__date__lte=end_date,
                status='completed'
            ).count()

            if emotion_count == 0:
                logger.info(f"[仪表盘] {start_date} 到 {end_date} 期间无情绪分析数据")
                return {
                    'dates': [],
                    'series': [
                        {'name': '初始情绪', 'data': [], 'type': 'line'},
                        {'name': '最终情绪', 'data': [], 'type': 'line'},
                        {'name': '情绪变化', 'data': [], 'type': 'line'}
                    ]
                }

            # 按日期聚合情绪数据
            emotion_daily = CsEmotionAnalysis.objects.filter(
                create_datetime__date__gte=start_date,
                create_datetime__date__lte=end_date,
                status='completed'
            ).extra(
                select={'date': 'DATE(create_datetime)'}
            ).values('date').annotate(
                avg_initial=Avg('initial_emotion_score'),
                avg_final=Avg('final_emotion_score'),
                avg_change=Avg('emotion_change_score'),
                count=Count('id')
            ).order_by('date')

            dates = []
            initial_scores = []
            final_scores = []
            change_scores = []

            for item in emotion_daily:
                # 格式化日期为 MM-DD 格式
                date_obj = item['date']
                formatted_date = f"{date_obj.month:02d}-{date_obj.day:02d}"
                dates.append(formatted_date)

                initial_scores.append(round(item['avg_initial'] or 0, 2))
                final_scores.append(round(item['avg_final'] or 0, 2))
                change_scores.append(round(item['avg_change'] or 0, 2))

            return {
                'dates': dates,
                'series': [
                    {'name': '初始情绪', 'data': initial_scores, 'type': 'line'},
                    {'name': '最终情绪', 'data': final_scores, 'type': 'line'},
                    {'name': '情绪变化', 'data': change_scores, 'type': 'line'}
                ]
            }

        except Exception as e:
            logger.error(f"[仪表盘] 获取情绪图表数据失败: {str(e)}")
            return {
                'dates': [],
                'series': [
                    {'name': '初始情绪', 'data': [], 'type': 'line'},
                    {'name': '最终情绪', 'data': [], 'type': 'line'},
                    {'name': '情绪变化', 'data': [], 'type': 'line'}
                ]
            }

    @action(detail=False, methods=['post'])
    def link_ranking(self, request):
        """
        获取基于客服组筛选的Link排行榜数据
        
        根据客服组筛选对应的游戏，然后统计该游戏的文章Link数据
        """
        try:
            # 获取筛选参数
            start_time = request.data.get('start_time')
            end_time = request.data.get('end_time')
            group_ids = request.data.get('group_ids')  # 支持多选客服组

            # 兼容旧的单选参数
            if not group_ids:
                group_id = request.data.get('group_id')
                if group_id is not None:
                    try:
                        group_ids = [int(group_id)]
                    except (ValueError, TypeError):
                        group_ids = None
            
            # 默认获取最近7天的数据
            if not start_time or not end_time:
                end_time = timezone.now()
                start_time = end_time - timedelta(days=7)
            else:
                start_time = parse_datetime(start_time)
                end_time = parse_datetime(end_time)
                
                # 如果end_time只有日期部分（时间为00:00:00），则设置为当天的23:59:59
                if end_time and end_time.time() == datetime.min.time():
                    end_time = end_time.replace(hour=23, minute=59, second=59, microsecond=999999)
                
                # 由于USE_TZ=False，不需要make_aware处理
                # start_time = make_aware(start_time) if start_time else None
                # end_time = make_aware(end_time) if end_time else None
            
            # 获取Link排行数据
            ranking_data = self._get_link_ranking_data(start_time, end_time, group_ids)

            # 获取Link趋势数据
            trend_data = self._get_link_trend_data(start_time, end_time, group_ids)
            
            return SuccessResponse(data={
                'top_articles': ranking_data,
                'link_trend': trend_data,
                'summary': {
                    'total_links': sum(item['link_count'] for item in ranking_data),
                    'total_articles': len(ranking_data),
                    'period': {
                        'start_time': start_time.strftime('%Y-%m-%d %H:%M:%S') if start_time else None,
                        'end_time': end_time.strftime('%Y-%m-%d %H:%M:%S') if end_time else None
                    }
                }
            })
            
        except Exception as e:
            logger.error(f"[仪表盘] 获取Link排行榜失败: {str(e)}")
            return ErrorResponse(msg=f"获取Link排行榜失败: {str(e)}")
    
    def _get_link_ranking_data(self, start_time, end_time, group_ids: List[int] = None) -> List[Dict]:
        """
        获取Link排行榜数据
        
        根据客服组筛选对应的游戏，统计文章的Link数量
        """
        try:
            # 构建Link查询过滤器
            link_filters = Q()
            if start_time:
                link_filters &= Q(create_datetime__gte=start_time)
            if end_time:
                link_filters &= Q(create_datetime__lte=end_time)
            
            # 根据客服组筛选游戏
            game_filters = Q()
            if group_ids and 0 not in group_ids:
                # 根据客服组名称获取对应的游戏
                group_names = []
                for group_id in group_ids:
                    group_name = self._get_group_name_by_id(str(group_id))
                    if group_name:
                        group_names.append(group_name)

                if group_names:
                    # 通过qiyu_group_name字段匹配游戏
                    game_filters &= Q(game__qiyu_group_name__in=group_names)
                    logger.info(f"[Link排行榜] 筛选游戏组: {group_names}")
            
            # 使用子查询获取每个Article的link_count
            link_subquery = Link.objects.filter(
                article_id=OuterRef('pk')
            )
            if start_time:
                link_subquery = link_subquery.filter(create_datetime__gte=start_time)
            if end_time:
                link_subquery = link_subquery.filter(create_datetime__lte=end_time)
            
            link_subquery = link_subquery.values('article_id').annotate(count=Count('id')).values('count')
            
            # 获取Article queryset并注入link_count字段
            article_query = Article.objects.filter(game_filters).select_related('game')
            
            article_queryset = article_query.annotate(
                link_count=Coalesce(Subquery(link_subquery), 0)
            ).filter(link_count__gt=0).order_by('-link_count')[:10]
            
            # 转为json格式返回
            data = []
            for article in article_queryset:
                # 获取这个Article相关的Link queryset
                article_links = Link.objects.filter(article_id=article.id)
                if start_time:
                    article_links = article_links.filter(create_datetime__gte=start_time)
                if end_time:
                    article_links = article_links.filter(create_datetime__lte=end_time)
                
                # 计算趋势数据
                trend_data = []
                if start_time and end_time:
                    if (end_time - start_time).total_seconds() <= 86400 * 2:  # 2天内按小时
                        hourly_counts = article_links.annotate(time=ExtractHour('create_datetime')).values('time').annotate(
                            count=Count('id')).order_by('time')
                        hours = {hour: 0 for hour in range(24)}
                        for entry in hourly_counts:
                            hours[entry['time']] = entry['count']
                        trend_data = [{'time': hour, 'count': count} for hour, count in hours.items()]
                    else:  # 按日期
                        daily_counts = article_links.annotate(time=TruncDate('create_datetime')).values('time').annotate(
                            count=Count('id')).order_by('time')
                        days = {}
                        current_day = start_time.date()
                        while current_day <= end_time.date():
                            days[current_day] = 0
                            current_day += timedelta(days=1)
                        for entry in daily_counts:
                            entry_date = entry['time']
                            if isinstance(entry_date, datetime):
                                entry_date = entry_date.date()
                            if entry_date in days:
                                days[entry_date] = entry['count']
                        trend_data = [{'time': day.strftime('%Y-%m-%d'), 'count': count} for day, count in days.items()]
                
                # 构造Article数据
                article_data = model_to_dict(article, fields=['id', 'title', 'summary'])
                article_data['create_datetime'] = article.create_datetime.strftime('%Y-%m-%d %H:%M:%S')
                article_data['link_count'] = article.link_count
                article_data['game'] = model_to_dict(article.game, fields=['id', 'name'])
                article_data['trend_data'] = trend_data
                
                data.append(article_data)
            
            logger.info(f"[仪表盘] 获取到 {len(data)} 条Link排行数据")
            return data
            
        except Exception as e:
            logger.error(f"[仪表盘] 获取Link排行数据失败: {str(e)}")
            return []
     
    def _get_link_trend_data(self, start_time, end_time, group_ids: List[int] = None) -> List[Dict]:
        """
        获取Link趋势数据
        """
        try:
            # 构建查询条件
            link_queryset = Link.objects.all()
            
            if start_time:
                link_queryset = link_queryset.filter(create_datetime__gte=start_time)
            if end_time:
                link_queryset = link_queryset.filter(create_datetime__lte=end_time)
            
            # 根据客服组筛选游戏
            if group_ids and 0 not in group_ids:
                # 根据客服组名称获取对应的游戏
                group_names = []
                for group_id in group_ids:
                    group_name = self._get_group_name_by_id(str(group_id))
                    if group_name:
                        group_names.append(group_name)

                if group_names:
                    # 通过游戏的qiyu_group_name字段筛选
                    link_queryset = link_queryset.filter(article__game__qiyu_group_name__in=group_names)
                    logger.info(f"[Link趋势] 筛选游戏组: {group_names}")
            
            # 判断时间跨度，决定按小时还是按天统计
            if start_time and end_time and (end_time - start_time).total_seconds() <= 86400:
                # 按小时统计
                link_counts = link_queryset.annotate(time=ExtractHour('create_datetime')).values('time').annotate(
                    count=Count('id')).order_by('time')
                hours = {hour: 0 for hour in range(24)}
                for entry in link_counts:
                    hours[entry['time']] = entry['count']
                link_counts = [{'time': hour, 'count': count} for hour, count in hours.items()]
            else:
                # 按日期统计
                link_counts = link_queryset.annotate(time=TruncDate('create_datetime')).values('time').annotate(
                    count=Count('id')).order_by('time')
                days = {}
                if start_time and end_time:
                    day = start_time.date()
                    while day <= end_time.date():
                        days[day] = 0
                        day += timedelta(days=1)
                for entry in link_counts:
                    days[entry['time']] = entry['count']
                link_counts = [{'time': day.strftime('%Y-%m-%d'), 'count': count} for day, count in days.items()]
            
            return link_counts
            
        except Exception as e:
            logger.error(f"[仪表盘] 获取Link趋势数据失败: {str(e)}")
            return []
