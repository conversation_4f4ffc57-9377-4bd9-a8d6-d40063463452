import os

from application.settings import BASE_DIR

# ================================================= #
# *************** mysql数据库 配置  *************** #
# ================================================= #
# 数据库 ENGINE ，默认演示使用 sqlite3 数据库，正式环境建议使用 mysql 数据库
# sqlite3 设置
# DATABASE_ENGINE = "django.db.backends.sqlite3"
# DATABASE_NAME = os.path.join(BASE_DIR, "db.sqlite3")

# 使用mysql时，改为此配置
DATABASE_ENGINE = "django.db.backends.mysql"
DATABASE_NAME = 'giant_cs_admin'  # mysql 时使用

# 数据库地址 改为自己数据库地址
DATABASE_HOST = '************'
# # 数据库端口
DATABASE_PORT = 3306
# # 数据库用户名
DATABASE_USER = "root"
# # 数据库密码
DATABASE_PASSWORD = "ZTGAME"

# 表前缀
TABLE_PREFIX = "admin_"
# ================================================= #
# ******** redis配置，无redis 可不进行配置  ******** #
# ================================================= #
REDIS_PASSWORD = 'csadmin'
REDIS_HOST = '**************'
REDIS_URL = f'redis://:{REDIS_PASSWORD or ""}@{REDIS_HOST}:6379'
# ================================================= #
# ****************** 功能 启停  ******************* #
# ================================================= #
DEBUG = True
# 启动登录详细概略获取(通过调用api获取ip详细地址。如果是内网，关闭即可)
ENABLE_LOGIN_ANALYSIS_LOG = True
# 登录接口 /api/token/ 是否需要验证码认证，用于测试，正式环境建议取消
LOGIN_NO_CAPTCHA_AUTH = True
# ================================================= #
# ****************** 其他 配置  ******************* #
# ================================================= #

ALLOWED_HOSTS = ["*"]
# 列权限中排除App应用
COLUMN_EXCLUDE_APPS = []

FEISHU_APP_ID = 'cli_a6a48656ebb9d00d'
FEISHU_APP_SECRET = 'gRgINzUndkKp5oF75p1RhhY3yOccRH3L'

TRINO = {
    "HOST": "************",
    "PORT": "28080",
    "USER": "kefu",
}
WECHAT_MYSQL = {
    "ENGINE": "django.db.backends.mysql",
    "NAME": "wechatunit",
    "USER": "u_wechatunit_r",
    "PASSWORD": "IEe3y1W7cJe4EWG6L8e",
    "HOST": "************",
    "PORT": "6033",
}

ES_SEARCH_SETTINGS = {
    'HOSTS': "http://**************:9200",
    'INDEX_PREFIX': 'kcs_dev', # 索引前缀，方便区分环境
    'BAILIAN_API_KEY': "sk-cdeb6b160abb423799dd790000862ce4",
    'BAILIAN_EMBEDDING_URL': "https://dashscope.aliyuncs.com/compatible-mode/v1/embeddings", # 阿里云 Text Embedding v3 API 地址
    'BAILIAN_EMBEDDING_MODEL': 'text-embedding-v3', # 使用的模型
    'VECTOR_DIMENSION': 1024, # 使用text-embedding-v3默认维度
    'HTTP_AUTH': ('elastic', '123456'), # 如果 ES 需要认证
    # 'USE_SSL': True,
    # 'VERIFY_CERTS': True,
    # 'CA_CERTS': '/path/to/ca.crt',
    'TIMEOUT': 30,
    'REGISTERED_MODELS': [
        {
            'model': 'apps.kcs.models.Article',
            'index_class': 'apps.kcs.search_indexes.ArticleElasticsearchIndex',  # 使用新的索引类
            'config': {
                'index_name': 'kcs_prod_kcs_article',
            }
        }
    ],
    'HYBRID_FETCH_MULTIPLIER': 10,  # 混合搜索时从ES获取的候选结果数量
    'DEFAULT_PAGE_SIZE': 10,  # 默认每页显示的文档数量
}

# swagger 配置
SWAGGER_ENABLE = True  # 开发环境开启swagger

# RocketMQ 配置
ROCKETMQ_SETTINGS = {
    'NAME_SERVER': '**************:9876', # 替换成你的 NameServer 地址
    'CONSUMER_GROUP': 'CS_ADMIN_GROUP', # 消费组ID，必须设置，不能为空
    'SUBSCRIPTIONS': {
        '5286_player_register_event': '*',
        '5199_daily_mission_event': '*',
    },
    'CONSUME_THREAD_MIN': 5,  # 最小消费线程数 (根据需要调整)
    'CONSUME_THREAD_MAX': 20, # 最大消费线程数 (根据需要调整)
    'PULL_BATCH_SIZE': 32,    # 每次拉取消息数量
    'LOG_LEVEL': 'INFO',      # RocketMQ Client 日志级别 (DEBUG, INFO, WARNING, ERROR)
    # ACL 配置
    'ACL_ENABLED': False, # 或者根据需要判断是否启用
    'ACCESS_KEY': 'your_access_key', # 替换成你的 AccessKey
    'SECRET_KEY': 'your_secret_key', # 替换成你的 SecretKey
}