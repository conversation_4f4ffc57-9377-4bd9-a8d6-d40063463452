<template>
  <div class="bg-white rounded-lg shadow-sm border transition-all duration-200 hover:shadow-md">
    <!-- 头部区域：标题在左侧，统计数据在右侧 -->
    <div class="p-4 border-b border-gray-100">
      <div class="flex items-center justify-between">
        <!-- 左侧标题 -->
        <h3 class="text-base font-semibold text-gray-800 flex items-center">
          <el-icon class="mr-2 text-blue-500">
            <TrendCharts />
          </el-icon>
          文章Link排行榜
        </h3>
        
        <!-- 右侧控件和统计数据 -->
        <div class="flex items-center gap-6">
          <!-- Top数量选择 -->
          <div class="flex items-center gap-2">
            <label class="text-xs text-gray-600 whitespace-nowrap">显示:</label>
            <el-select v-model="topCount" @change="handleTopCountChange" size="small" class="!w-20">
              <el-option label="10" :value="10" />
              <el-option label="20" :value="20" />
              <el-option label="30" :value="30" />
              <el-option label="50" :value="50" />
            </el-select>
          </div>

          <div class="flex items-center gap-2">
            <div class="w-6 h-6 rounded-full flex items-center justify-center bg-blue-500">
              <el-icon :size="12" color="#ffffff">
                <TrendCharts />
              </el-icon>
            </div>
            <div class="text-right">
              <p class="text-xs text-gray-600">总文章数</p>
              <span class="text-sm font-bold text-blue-600">
                {{ (summary?.total_articles || 0).toLocaleString() }}
              </span>
            </div>
          </div>

          <div class="flex items-center gap-2">
            <div class="w-6 h-6 rounded-full flex items-center justify-center bg-green-500">
              <el-icon :size="12" color="#ffffff">
                <Link />
              </el-icon>
            </div>
            <div class="text-right">
              <p class="text-xs text-gray-600">总Link数</p>
              <span class="text-sm font-bold text-green-600">
                {{ (summary?.total_links || 0).toLocaleString() }}
              </span>
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- 现代化卡片网格布局 -->
    <div class="p-6">
      <div class="w-full">
        <div v-if="topArticles && topArticles.length > 0" class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-5">
          <div 
            v-for="(article, index) in topArticles" 
            :key="article.id || index"
            class="bg-white rounded-2xl p-5 relative cursor-pointer transition-all duration-300 border-2 hover:-translate-y-1 hover:shadow-xl"
            :class="getCardClass(index)"
            @click="$emit('openArticle', article.id)"
          >
            <!-- 排名徽章 -->
            <div class="absolute -top-2.5 left-5 w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm text-white shadow-lg z-10" :class="getRankBadgeClass(index)">
              <span>{{ index + 1 }}</span>
            </div>

            <!-- 文章内容 -->
            <div class="mt-4">
              <div class="mb-3">
                <h3 class="text-base font-semibold text-gray-800 mb-2 leading-tight line-clamp-2">{{ article.title }}</h3>
                <div class="flex items-center gap-3 flex-wrap">
                  <el-tag type="primary" size="small" class="text-xs">
                    🎮 {{ article.game?.name || '未分类' }}
                  </el-tag>
                  <span class="text-xs text-gray-500">
                    📅 {{ article.create_datetime }}
                  </span>
                </div>
              </div>
              
              <p class="text-sm text-gray-600 leading-relaxed my-3">{{ article.summary || '暂无摘要描述...' }}</p>

              <!-- ECharts趋势图 -->
              <div class="bg-slate-50 rounded-xl p-4 my-4 border border-slate-200">
                <div v-if="article.trend_data && article.trend_data.length > 0">
                  <Echarts :options="getTrendChartOptions(article.trend_data)" height="120px" />
                  
                  <!-- 趋势统计 -->
                  <div class="flex flex-col gap-2 mt-3">
                    <div class="flex gap-4 justify-between">
                      <span class="flex items-center gap-1 text-xs text-gray-600 bg-white px-2 py-1 rounded border border-gray-200">
                        <span class="text-sm">🔗</span>
                        <span>Link数量: {{ article.link_count }}</span>
                      </span>

                      <span class="flex items-center gap-1 text-xs text-gray-600 bg-white px-2 py-1 rounded border border-gray-200">
                        <span class="text-sm">🔝</span>
                        <span>峰值: {{ getMaxTrend(article.trend_data) }}</span>
                      </span>
                      <span class="flex items-center gap-1 text-xs text-gray-600 bg-white px-2 py-1 rounded border border-gray-200">
                        <span class="text-sm">📊</span>
                        <span>均值: {{ getAvgTrend(article.trend_data) }}</span>
                      </span>
                    </div>

                    <!-- Link类型统计 -->
                    <div v-if="article.link_type_stats && article.link_type_stats.length > 0" class="flex flex-wrap gap-1 mt-2">
                      <span
                        v-for="(typeStats, typeIndex) in article.link_type_stats"
                        :key="typeStats.type_id"
                        class="flex items-center gap-1 text-xs px-2 py-1 rounded border"
                        :class="getLinkTypeClass(typeIndex)"
                      >
                        <span>{{ typeStats.type_name }}: {{ typeStats.count }}</span>
                      </span>
                    </div>
                  </div>
                </div>
                
                <div v-else class="text-center py-5 text-gray-400">
                  <div class="text-3xl mb-2">📊</div>
                  <div class="text-sm">暂无趋势数据</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-else class="text-center py-15 px-5 text-gray-500">
          <div class="text-6xl mb-4">📊</div>
          <h3 class="text-xl font-semibold text-gray-700 mb-2">暂无排行数据</h3>
          <p class="text-sm mb-5 leading-relaxed">当前时间段内没有文章Link数据，请尝试调整筛选条件</p>
          <el-button type="primary" @click="$emit('refresh')" class="rounded-lg">
            🔄 刷新数据
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { TrendCharts, Link, DataAnalysis } from '@element-plus/icons-vue'
import Echarts from '/@/components/echarts/index.vue'
import type { EChartsCoreOption } from '/@/components/echarts/useEcharts'
import type { ArticleData } from '../api'

interface Props {
  topArticles: ArticleData[]
  summary: {
    total_links: number
    total_articles: number
    period: {
      start_time: string
      end_time: string
    }
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  openArticle: [id: number]
  refresh: []
  topCountChange: [count: number]
}>()

// Top数量选择
const topCount = ref(10)

const handleTopCountChange = () => {
  emit('topCountChange', topCount.value)
}

// ECharts趋势图配置
const getTrendChartOptions = (trendData: any[]): EChartsCoreOption => {
  const dates = trendData.map(item => formatTrendDate(item.time))
  const values = trendData.map(item => item.count)
  
  return {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        fontSize: 10,
        color: '#6b7280'
      }
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [{
      data: values,
      type: 'bar',
      itemStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: 'rgba(59, 130, 246, 0.8)'
          }, {
            offset: 1,
            color: 'rgba(147, 197, 253, 0.6)'
          }]
        },
        borderRadius: [2, 2, 0, 0]
      },
      emphasis: {
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(59, 130, 246, 1)'
            }, {
              offset: 1,
              color: 'rgba(147, 197, 253, 0.8)'
            }]
          }
        }
      }
    }],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      formatter: (params: any) => {
        const data = params[0]
        return `${data.name}<br/>Link数: ${data.value}`
      }
    }
  }
}

// 获取卡片样式类
const getCardClass = (index: number) => {
  if (index === 0) return 'border-yellow-400 bg-gradient-to-br from-yellow-50 to-white'
  if (index === 1) return 'border-gray-400 bg-gradient-to-br from-gray-50 to-white'
  if (index === 2) return 'border-orange-400 bg-gradient-to-br from-orange-50 to-white'
  return 'border-slate-200'
}

// 获取排名徽章样式
const getRankBadgeClass = (index: number) => {
  if (index === 0) return 'bg-gradient-to-br from-yellow-400 to-yellow-500'
  if (index === 1) return 'bg-gradient-to-br from-gray-400 to-gray-500'
  if (index === 2) return 'bg-gradient-to-br from-orange-400 to-orange-500'
  return 'bg-gradient-to-br from-gray-500 to-gray-600'
}

// 格式化趋势图日期
const formatTrendDate = (dateTime: string) => {
  return new Date(dateTime).toLocaleDateString('zh-CN', {
    month: '2-digit',
    day: '2-digit'
  })
}




// 获取趋势最大值
const getMaxTrend = (trendData: any[]) => {
  if (!trendData || trendData.length === 0) return 0
  return Math.max(...trendData.map(d => d.count))
}

// 获取趋势平均值
const getAvgTrend = (trendData: any[]) => {
  if (!trendData || trendData.length === 0) return 0
  const sum = trendData.reduce((acc, d) => acc + d.count, 0)
  return Math.round(sum / trendData.length)
}

// Link类型样式
const getLinkTypeClass = (index: number) => {
  const colors = [
    'text-blue-600 bg-blue-50 border-blue-200',
    'text-green-600 bg-green-50 border-green-200',
    'text-purple-600 bg-purple-50 border-purple-200',
    'text-orange-600 bg-orange-50 border-orange-200',
    'text-red-600 bg-red-50 border-red-200',
    'text-indigo-600 bg-indigo-50 border-indigo-200'
  ]
  return colors[index % colors.length]
}



</script>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr !important;
  }
  
  .flex {
    flex-direction: column;
  }
  
  .gap-4 {
    gap: 0.5rem;
  }
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>