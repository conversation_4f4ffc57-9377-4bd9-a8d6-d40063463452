{"version": 3, "sources": ["../../@fast-crud/fast-extends/src/editor/components/fs-editor-code/index.vue"], "sourcesContent": ["<template>\n  <div ref=\"monacoRef\" class=\"fs-editor-code\"></div>\n</template>\n\n<script lang=\"ts\" setup>\nimport * as monaco from \"monaco-editor\";\n// import * as monaco from \"monaco-editor/esm/vs/editor/editor.api\";\nimport { onMounted, onUnmounted, ref, watch } from \"vue\";\nimport { cloneDeep, debounce as lodashDebounce } from \"lodash-es\";\nimport { initWorkers } from \"./workers\";\n\n/**\n * config:\n *   value: '', // 编辑器初始文本\n *   language: 'javascript', // 语言\n *   theme: 'vs', // 主题\n *   readOnly: false, // 是否只读\n *   minimap: { enabled: false }, // 是否启用小地图\n *   fontSize: 14, // 字体大小\n *   tabSize: 2, // tab缩进长度\n *   automaticLayout: true, // 自动布局\n *   lineNumbers: 'off', // 是否启用行号\n *   contextmenu: true, // 是否启用上下文菜单\n *   folding: true, // 是否启用代码折叠\n *   foldingStrategy: 'auto', // 代码折叠策略\n *   wordWrap: 'on', // 自动换行设置\n *   wrappingIndent: 'indent', // 换行缩进\n *   formatOnPaste: true, // 粘贴时是否自动格式化\n *   formatOnType: true, // 输入时是否自动格式化\n *   dragAndDrop: true, // 是否允许拖放\n *   cursorStyle: 'line', // 光标样式\n *   cursorBlinking: 'blink', // 光标闪烁方式\n *   scrollbar: {\n *     vertical: 'auto', // 垂直滚动条的显示方式\n *     horizontal: 'auto', // 水平滚动条的显示方式\n *     verticalScrollbarSize: 2, // 垂直滚动条的宽\n *     horizontalScrollbarSize: 2, // 水平滚动条的高度\n *   }\n */\nconst props = defineProps<{\n  language?: string;\n  modelValue?: string;\n  config?: any;\n  schema?: any;\n  debounce?: number;\n  init?: any;\n  readonly?: boolean;\n  disabled?: boolean;\n  id?: string;\n}>();\n\nexport type EditorCodeCtx = {\n  // monaco对象\n  monaco: any;\n  //语言\n  language: string;\n  //配置\n  config: any;\n  //editor实例对象\n  instance?: any;\n\n  schema?: any;\n\n  initialValue?: any;\n};\n\nconst monacoRef = ref();\n\nlet instanceRef = ref();\n\nfunction disposeEditor() {\n  // if (instanceRef.value) {\n  //   instanceRef.value.dispose(); //使用完成销毁实例\n  // }\n}\n\nonUnmounted(() => {\n  disposeEditor();\n});\n\nconst emits = defineEmits([\"update:modelValue\", \"change\", \"ready\"]);\n\nconst emitValue = lodashDebounce((value: any) => {\n  emits(\"update:modelValue\", value);\n}, props.debounce || 500);\n\nasync function createEditor(ctx: EditorCodeCtx) {\n  disposeEditor();\n  const instance = monaco.editor.create(monacoRef.value, {\n    automaticLayout: true,\n    value: props.modelValue,\n    language: ctx.language,\n    theme: \"vs-dark\",\n    minimap: { enabled: false },\n    readOnly: props.readonly || props.disabled,\n    hover: {\n      enabled: true\n    },\n    ...ctx.config\n  });\n\n  // @event `change`\n  instance.onDidChangeModelContent((event) => {\n    const value = instance.getValue();\n    if (props.modelValue !== value) {\n      emits(\"change\", value);\n      emitValue(value);\n    }\n  });\n\n  instanceRef.value = instance;\n  ctx.instance = instance;\n  emits(\"ready\", ctx);\n  return instance;\n}\n\nasync function initJavascript(ctx: EditorCodeCtx) {\n  await import(\"monaco-editor/esm/vs/basic-languages/javascript/javascript.contribution\");\n  monaco.languages.register({ id: \"javascript\" });\n}\n\nasync function initJson(ctx: EditorCodeCtx) {\n  await import(\"monaco-editor/esm/vs/language/json/monaco.contribution\");\n  monaco.languages.register({ id: \"json\" });\n\n  const schemas = [];\n  if (ctx.schema) {\n    schemas.push({\n      // uri: \"http://myserver/foo-schema.json\", // id of the first schema\n      fileMatch: [\"*\"], // associate with our model\n      schema: {\n        ...ctx.schema\n      }\n    });\n  }\n\n  monaco.languages.json.jsonDefaults.setDiagnosticsOptions({\n    validate: true,\n    enableSchemaRequest: false,\n    schemas\n  });\n}\n\nasync function initYaml(ctx: EditorCodeCtx) {\n  await import(\"monaco-editor/esm/vs/basic-languages/yaml/yaml.contribution\");\n  const { configureMonacoYaml } = await import(\"monaco-yaml\");\n  monaco.languages.register({ id: \"yaml\" });\n\n  const schemas = [];\n  if (ctx.schema) {\n    schemas.push({\n      fileMatch: [\"*\"], // associate with our model\n      schema: {\n        ...ctx.schema\n      },\n      uri: \"http://myserver/foo-schema.json\"\n    });\n  }\n  configureMonacoYaml(monaco, {\n    schemas,\n    format: true,\n    hover: true,\n    completion: true,\n    validate: true,\n    isKubernetes: false,\n    enableSchemaRequest: false\n  });\n  const uri = monaco.Uri.parse(props.id || \"fs-editor-code-yaml.yaml\");\n  const oldModel = monaco.editor.getModel(uri);\n  if (oldModel) {\n    oldModel.dispose();\n  }\n  ctx.config.model = monaco.editor.createModel(ctx.initialValue, null, uri);\n}\n\nasync function doInit() {\n  const ctx: EditorCodeCtx = {\n    monaco,\n    language: props.language || \"javascript\",\n    config: cloneDeep(props.config || {}),\n    initialValue: props.modelValue || \"\",\n    schema: props.schema\n  };\n  if (ctx.language === \"javascript\") {\n    await initJavascript(ctx);\n  } else if (ctx.language === \"yaml\") {\n    await initYaml(ctx);\n  } else if (ctx.language === \"json\") {\n    await initJson(ctx);\n  }\n\n  await createEditor(ctx);\n}\n\nonMounted(async () => {\n  await initWorkers();\n  await doInit();\n  watch(\n    () => {\n      return {\n        language: props.language,\n        config: props.config\n      };\n    },\n    (value: any) => {\n      doInit();\n    }\n  );\n});\n</script>\n\n<style lang=\"less\">\n.fs-editor-code {\n  min-height: 100px;\n  width: 100%;\n  border: 1px solid #eee;\n  border-radius: 5px;\n}\n\n.monaco-editor .hover-content {\n  z-index: 10000 !important;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,UAAMA,IAAQC,GA2BRC,IAAYC,IAAAA;AAElB,QAAIC,IAAcD,IAAAA;AAQlBE,gBAAY,MAAM;IACF,CACf;AAED,UAAMC,IAAQC,GAERC,IAAYC,iBAAe,CAACC,MAAe;AAC/CJ,QAAM,qBAAqBI,CAAK;IAAA,GAC/BV,EAAM,YAAY,GAAG;AAExB,mBAAeW,EAAaC,GAAoB;AAE9C,YAAMC,IAAkB,OAAO,OAAOX,EAAU,OAAO;QACrD,iBAAiB;QACjB,OAAOF,EAAM;QACb,UAAUY,EAAI;QACd,OAAO;QACP,SAAS,EAAE,SAAS,MAAM;QAC1B,UAAUZ,EAAM,YAAYA,EAAM;QAClC,OAAO;UACL,SAAS;QACX;QACA,GAAGY,EAAI;MAAA,CACR;AAGQ,aAAAC,EAAA,wBAAwB,CAACC,MAAU;AACpC,cAAAJ,IAAQG,EAAS,SAAA;AACnBb,UAAM,eAAeU,MACvBJ,EAAM,UAAUI,CAAK,GACrBF,EAAUE,CAAK;MACjB,CACD,GAEDN,EAAY,QAAQS,GACpBD,EAAI,WAAWC,GACfP,EAAM,SAASM,CAAG,GACXC;IACT;AAEA,mBAAeE,EAAeH,GAAoB;AAChD,YAAM,OAAO,gDAAyE,GAC/E,UAAU,SAAS,EAAE,IAAI,aAAc,CAAA;IAChD;AAEA,mBAAeI,EAASJ,GAAoB;AAC1C,YAAM,OAAO,4CAAwD,GAC9D,UAAU,SAAS,EAAE,IAAI,OAAQ,CAAA;AAExC,YAAMK,IAAU,CAAA;AACZL,QAAI,UACNK,EAAQ,KAAK;;QAEX,WAAW,CAAC,GAAG;;QACf,QAAQ;UACN,GAAGL,EAAI;QACT;MAAA,CACD,GAGI,UAAU,KAAK,aAAa,sBAAsB;QACvD,UAAU;QACV,qBAAqB;QACrB,SAAAK;MAAA,CACD;IACH;AAEA,mBAAeC,EAASN,GAAoB;AAC1C,YAAM,OAAO,0CAA6D;AAC1E,YAAM,EAAE,qBAAAO,EAAA,IAAwB,MAAM,OAAO,2BAAa;AAC1DC,MAAO,UAAU,SAAS,EAAE,IAAI,OAAQ,CAAA;AAExC,YAAMH,IAAU,CAAA;AACZL,QAAI,UACNK,EAAQ,KAAK;QACX,WAAW,CAAC,GAAG;;QACf,QAAQ;UACN,GAAGL,EAAI;QACT;QACA,KAAK;MAAA,CACN,GAEHO,EAAoBC,qBAAQ;QAC1B,SAAAH;QACA,QAAQ;QACR,OAAO;QACP,YAAY;QACZ,UAAU;QACV,cAAc;QACd,qBAAqB;MAAA,CACtB;AACD,YAAMI,IAAa,IAAI,MAAMrB,EAAM,MAAM,0BAA0B,GAC7DsB,IAAkB,OAAO,SAASD,CAAG;AACvCC,WACFA,EAAS,QAAQ,GAEfV,EAAA,OAAO,QAAe,OAAO,YAAYA,EAAI,cAAc,MAAMS,CAAG;IAC1E;AAEA,mBAAeE,IAAS;AACtB,YAAMX,IAAqB;QACzB,QAAAQ;QACA,UAAUpB,EAAM,YAAY;QAC5B,QAAQwB,kBAAUxB,EAAM,UAAU,CAAA,CAAE;QACpC,cAAcA,EAAM,cAAc;QAClC,QAAQA,EAAM;MAAA;AAEZY,QAAI,aAAa,eACnB,MAAMG,EAAkB,IACfH,EAAI,aAAa,SAC1B,MAAMM,EAASN,CAAG,IACTA,EAAI,aAAa,UAC1B,MAAMI,EAASJ,CAAG,GAGpB,MAAMD,EAAaC,CAAG;IACxB;AAEA,WAAAa,UAAU,YAAY;AACpB,YAAMC,GAAY,GAClB,MAAMH,EAAO,GACbI;QACE,OACS;UACL,UAAU3B,EAAM;UAChB,QAAQA,EAAM;QAAA;QAGlB,CAACU,MAAe;AACPa,YAAAA;QACT;MAAA;IACF,CACD,GAAA,CAAA,GAAA,OAAA,UAAA,GAAA,mBAAA,OAAA;;;;;;;", "names": ["props", "__props", "monacoRef", "ref", "instanceRef", "onUnmounted", "emits", "__emit", "emitValue", "lodashDebounce", "value", "createEditor", "ctx", "instance", "event", "initJavascript", "initJson", "schemas", "initYaml", "configureMonacoYaml", "monaco", "uri", "oldModel", "doInit", "cloneDeep", "onMounted", "initWorkers", "watch"]}