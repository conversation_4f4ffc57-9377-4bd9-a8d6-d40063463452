import {
  useRoute
} from "./chunk-SQPW7ARH.js";
import {
  B,
  i
} from "./chunk-GQR6RJUV.js";
import {
  require_dayjs_min
} from "./chunk-XBAZBRKF.js";
import {
  camelCase_default,
  cloneDeepWith_default,
  cloneDeep_default,
  debounce_default,
  forEach_default,
  forOwn_default,
  get_default,
  identity_default,
  includes_default,
  isArray_default,
  isString_default,
  keys_default,
  lodash_default_default,
  map_default,
  mergeWith_default,
  merge_default,
  omit_default,
  pick_default,
  remove_default,
  set_default,
  size_default,
  sortBy_default,
  toPairs_default,
  toPath_default,
  toString_default,
  unset_default,
  upperFirst_default
} from "./chunk-6KFXODJP.js";
import {
  Fragment,
  TransitionGroup,
  computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createSlots,
  createTextVNode,
  createVNode,
  defineAsyncComponent,
  defineComponent,
  getCurrentInstance,
  guardReactiveProps,
  h,
  inject,
  isRef,
  isShallow,
  isVNode,
  markRaw,
  mergeProps,
  nextTick,
  normalizeClass,
  normalizeProps,
  normalizeStyle,
  onMounted,
  onRenderTriggered,
  openBlock,
  provide,
  reactive,
  ref,
  render,
  renderList,
  renderSlot,
  resolveComponent,
  resolveDirective,
  resolveDynamicComponent,
  shallowReactive,
  shallowRef,
  toDisplayString,
  toHandlerKey,
  toRaw,
  toRef,
  unref,
  useAttrs,
  useSlots,
  vShow,
  watch,
  withCtx,
  withDirectives,
  withKeys,
  withModifiers
} from "./chunk-VL4YS5HC.js";
import {
  __toESM
} from "./chunk-2LSFTFF7.js";

// node_modules/@iconify/vue/dist/iconify.mjs
var matchIconName = /^[a-z0-9]+(-[a-z0-9]+)*$/;
var stringToIcon = (value, validate, allowSimpleName, provider = "") => {
  const colonSeparated = value.split(":");
  if (value.slice(0, 1) === "@") {
    if (colonSeparated.length < 2 || colonSeparated.length > 3) {
      return null;
    }
    provider = colonSeparated.shift().slice(1);
  }
  if (colonSeparated.length > 3 || !colonSeparated.length) {
    return null;
  }
  if (colonSeparated.length > 1) {
    const name2 = colonSeparated.pop();
    const prefix = colonSeparated.pop();
    const result = {
      // Allow provider without '@': "provider:prefix:name"
      provider: colonSeparated.length > 0 ? colonSeparated[0] : provider,
      prefix,
      name: name2
    };
    return validate && !validateIconName(result) ? null : result;
  }
  const name = colonSeparated[0];
  const dashSeparated = name.split("-");
  if (dashSeparated.length > 1) {
    const result = {
      provider,
      prefix: dashSeparated.shift(),
      name: dashSeparated.join("-")
    };
    return validate && !validateIconName(result) ? null : result;
  }
  if (allowSimpleName && provider === "") {
    const result = {
      provider,
      prefix: "",
      name
    };
    return validate && !validateIconName(result, allowSimpleName) ? null : result;
  }
  return null;
};
var validateIconName = (icon, allowSimpleName) => {
  if (!icon) {
    return false;
  }
  return !!// Check prefix: cannot be empty, unless allowSimpleName is enabled
  // Check name: cannot be empty
  ((allowSimpleName && icon.prefix === "" || !!icon.prefix) && !!icon.name);
};
var defaultIconDimensions = Object.freeze(
  {
    left: 0,
    top: 0,
    width: 16,
    height: 16
  }
);
var defaultIconTransformations = Object.freeze({
  rotate: 0,
  vFlip: false,
  hFlip: false
});
var defaultIconProps = Object.freeze({
  ...defaultIconDimensions,
  ...defaultIconTransformations
});
var defaultExtendedIconProps = Object.freeze({
  ...defaultIconProps,
  body: "",
  hidden: false
});
function mergeIconTransformations(obj1, obj2) {
  const result = {};
  if (!obj1.hFlip !== !obj2.hFlip) {
    result.hFlip = true;
  }
  if (!obj1.vFlip !== !obj2.vFlip) {
    result.vFlip = true;
  }
  const rotate = ((obj1.rotate || 0) + (obj2.rotate || 0)) % 4;
  if (rotate) {
    result.rotate = rotate;
  }
  return result;
}
function mergeIconData(parent, child) {
  const result = mergeIconTransformations(parent, child);
  for (const key in defaultExtendedIconProps) {
    if (key in defaultIconTransformations) {
      if (key in parent && !(key in result)) {
        result[key] = defaultIconTransformations[key];
      }
    } else if (key in child) {
      result[key] = child[key];
    } else if (key in parent) {
      result[key] = parent[key];
    }
  }
  return result;
}
function getIconsTree(data, names) {
  const icons = data.icons;
  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);
  const resolved = /* @__PURE__ */ Object.create(null);
  function resolve(name) {
    if (icons[name]) {
      return resolved[name] = [];
    }
    if (!(name in resolved)) {
      resolved[name] = null;
      const parent = aliases[name] && aliases[name].parent;
      const value = parent && resolve(parent);
      if (value) {
        resolved[name] = [parent].concat(value);
      }
    }
    return resolved[name];
  }
  Object.keys(icons).concat(Object.keys(aliases)).forEach(resolve);
  return resolved;
}
function internalGetIconData(data, name, tree) {
  const icons = data.icons;
  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);
  let currentProps = {};
  function parse(name2) {
    currentProps = mergeIconData(
      icons[name2] || aliases[name2],
      currentProps
    );
  }
  parse(name);
  tree.forEach(parse);
  return mergeIconData(data, currentProps);
}
function parseIconSet(data, callback) {
  const names = [];
  if (typeof data !== "object" || typeof data.icons !== "object") {
    return names;
  }
  if (data.not_found instanceof Array) {
    data.not_found.forEach((name) => {
      callback(name, null);
      names.push(name);
    });
  }
  const tree = getIconsTree(data);
  for (const name in tree) {
    const item2 = tree[name];
    if (item2) {
      callback(name, internalGetIconData(data, name, item2));
      names.push(name);
    }
  }
  return names;
}
var optionalPropertyDefaults = {
  provider: "",
  aliases: {},
  not_found: {},
  ...defaultIconDimensions
};
function checkOptionalProps(item2, defaults) {
  for (const prop in defaults) {
    if (prop in item2 && typeof item2[prop] !== typeof defaults[prop]) {
      return false;
    }
  }
  return true;
}
function quicklyValidateIconSet(obj) {
  if (typeof obj !== "object" || obj === null) {
    return null;
  }
  const data = obj;
  if (typeof data.prefix !== "string" || !obj.icons || typeof obj.icons !== "object") {
    return null;
  }
  if (!checkOptionalProps(obj, optionalPropertyDefaults)) {
    return null;
  }
  const icons = data.icons;
  for (const name in icons) {
    const icon = icons[name];
    if (
      // Name cannot be empty
      !name || // Must have body
      typeof icon.body !== "string" || // Check other props
      !checkOptionalProps(
        icon,
        defaultExtendedIconProps
      )
    ) {
      return null;
    }
  }
  const aliases = data.aliases || /* @__PURE__ */ Object.create(null);
  for (const name in aliases) {
    const icon = aliases[name];
    const parent = icon.parent;
    if (
      // Name cannot be empty
      !name || // Parent must be set and point to existing icon
      typeof parent !== "string" || !icons[parent] && !aliases[parent] || // Check other props
      !checkOptionalProps(
        icon,
        defaultExtendedIconProps
      )
    ) {
      return null;
    }
  }
  return data;
}
var dataStorage = /* @__PURE__ */ Object.create(null);
function newStorage(provider, prefix) {
  return {
    provider,
    prefix,
    icons: /* @__PURE__ */ Object.create(null),
    missing: /* @__PURE__ */ new Set()
  };
}
function getStorage(provider, prefix) {
  const providerStorage = dataStorage[provider] || (dataStorage[provider] = /* @__PURE__ */ Object.create(null));
  return providerStorage[prefix] || (providerStorage[prefix] = newStorage(provider, prefix));
}
function addIconSet(storage2, data) {
  if (!quicklyValidateIconSet(data)) {
    return [];
  }
  return parseIconSet(data, (name, icon) => {
    if (icon) {
      storage2.icons[name] = icon;
    } else {
      storage2.missing.add(name);
    }
  });
}
function addIconToStorage(storage2, name, icon) {
  try {
    if (typeof icon.body === "string") {
      storage2.icons[name] = { ...icon };
      return true;
    }
  } catch (err) {
  }
  return false;
}
var simpleNames = false;
function allowSimpleNames(allow) {
  if (typeof allow === "boolean") {
    simpleNames = allow;
  }
  return simpleNames;
}
function getIconData(name) {
  const icon = typeof name === "string" ? stringToIcon(name, true, simpleNames) : name;
  if (icon) {
    const storage2 = getStorage(icon.provider, icon.prefix);
    const iconName = icon.name;
    return storage2.icons[iconName] || (storage2.missing.has(iconName) ? null : void 0);
  }
}
function addIcon(name, data) {
  const icon = stringToIcon(name, true, simpleNames);
  if (!icon) {
    return false;
  }
  const storage2 = getStorage(icon.provider, icon.prefix);
  if (data) {
    return addIconToStorage(storage2, icon.name, data);
  } else {
    storage2.missing.add(icon.name);
    return true;
  }
}
function addCollection(data, provider) {
  if (typeof data !== "object") {
    return false;
  }
  if (typeof provider !== "string") {
    provider = data.provider || "";
  }
  if (simpleNames && !provider && !data.prefix) {
    let added = false;
    if (quicklyValidateIconSet(data)) {
      data.prefix = "";
      parseIconSet(data, (name, icon) => {
        if (addIcon(name, icon)) {
          added = true;
        }
      });
    }
    return added;
  }
  const prefix = data.prefix;
  if (!validateIconName({
    provider,
    prefix,
    name: "a"
  })) {
    return false;
  }
  const storage2 = getStorage(provider, prefix);
  return !!addIconSet(storage2, data);
}
var defaultIconSizeCustomisations = Object.freeze({
  width: null,
  height: null
});
var defaultIconCustomisations = Object.freeze({
  // Dimensions
  ...defaultIconSizeCustomisations,
  // Transformations
  ...defaultIconTransformations
});
var unitsSplit = /(-?[0-9.]*[0-9]+[0-9.]*)/g;
var unitsTest = /^-?[0-9.]*[0-9]+[0-9.]*$/g;
function calculateSize(size, ratio, precision) {
  if (ratio === 1) {
    return size;
  }
  precision = precision || 100;
  if (typeof size === "number") {
    return Math.ceil(size * ratio * precision) / precision;
  }
  if (typeof size !== "string") {
    return size;
  }
  const oldParts = size.split(unitsSplit);
  if (oldParts === null || !oldParts.length) {
    return size;
  }
  const newParts = [];
  let code = oldParts.shift();
  let isNumber = unitsTest.test(code);
  while (true) {
    if (isNumber) {
      const num = parseFloat(code);
      if (isNaN(num)) {
        newParts.push(code);
      } else {
        newParts.push(Math.ceil(num * ratio * precision) / precision);
      }
    } else {
      newParts.push(code);
    }
    code = oldParts.shift();
    if (code === void 0) {
      return newParts.join("");
    }
    isNumber = !isNumber;
  }
}
function splitSVGDefs(content, tag = "defs") {
  let defs = "";
  const index = content.indexOf("<" + tag);
  while (index >= 0) {
    const start = content.indexOf(">", index);
    const end = content.indexOf("</" + tag);
    if (start === -1 || end === -1) {
      break;
    }
    const endEnd = content.indexOf(">", end);
    if (endEnd === -1) {
      break;
    }
    defs += content.slice(start + 1, end).trim();
    content = content.slice(0, index).trim() + content.slice(endEnd + 1);
  }
  return {
    defs,
    content
  };
}
function mergeDefsAndContent(defs, content) {
  return defs ? "<defs>" + defs + "</defs>" + content : content;
}
function wrapSVGContent(body, start, end) {
  const split = splitSVGDefs(body);
  return mergeDefsAndContent(split.defs, start + split.content + end);
}
var isUnsetKeyword = (value) => value === "unset" || value === "undefined" || value === "none";
function iconToSVG(icon, customisations) {
  const fullIcon = {
    ...defaultIconProps,
    ...icon
  };
  const fullCustomisations = {
    ...defaultIconCustomisations,
    ...customisations
  };
  const box = {
    left: fullIcon.left,
    top: fullIcon.top,
    width: fullIcon.width,
    height: fullIcon.height
  };
  let body = fullIcon.body;
  [fullIcon, fullCustomisations].forEach((props) => {
    const transformations = [];
    const hFlip = props.hFlip;
    const vFlip = props.vFlip;
    let rotation = props.rotate;
    if (hFlip) {
      if (vFlip) {
        rotation += 2;
      } else {
        transformations.push(
          "translate(" + (box.width + box.left).toString() + " " + (0 - box.top).toString() + ")"
        );
        transformations.push("scale(-1 1)");
        box.top = box.left = 0;
      }
    } else if (vFlip) {
      transformations.push(
        "translate(" + (0 - box.left).toString() + " " + (box.height + box.top).toString() + ")"
      );
      transformations.push("scale(1 -1)");
      box.top = box.left = 0;
    }
    let tempValue;
    if (rotation < 0) {
      rotation -= Math.floor(rotation / 4) * 4;
    }
    rotation = rotation % 4;
    switch (rotation) {
      case 1:
        tempValue = box.height / 2 + box.top;
        transformations.unshift(
          "rotate(90 " + tempValue.toString() + " " + tempValue.toString() + ")"
        );
        break;
      case 2:
        transformations.unshift(
          "rotate(180 " + (box.width / 2 + box.left).toString() + " " + (box.height / 2 + box.top).toString() + ")"
        );
        break;
      case 3:
        tempValue = box.width / 2 + box.left;
        transformations.unshift(
          "rotate(-90 " + tempValue.toString() + " " + tempValue.toString() + ")"
        );
        break;
    }
    if (rotation % 2 === 1) {
      if (box.left !== box.top) {
        tempValue = box.left;
        box.left = box.top;
        box.top = tempValue;
      }
      if (box.width !== box.height) {
        tempValue = box.width;
        box.width = box.height;
        box.height = tempValue;
      }
    }
    if (transformations.length) {
      body = wrapSVGContent(
        body,
        '<g transform="' + transformations.join(" ") + '">',
        "</g>"
      );
    }
  });
  const customisationsWidth = fullCustomisations.width;
  const customisationsHeight = fullCustomisations.height;
  const boxWidth = box.width;
  const boxHeight = box.height;
  let width;
  let height;
  if (customisationsWidth === null) {
    height = customisationsHeight === null ? "1em" : customisationsHeight === "auto" ? boxHeight : customisationsHeight;
    width = calculateSize(height, boxWidth / boxHeight);
  } else {
    width = customisationsWidth === "auto" ? boxWidth : customisationsWidth;
    height = customisationsHeight === null ? calculateSize(width, boxHeight / boxWidth) : customisationsHeight === "auto" ? boxHeight : customisationsHeight;
  }
  const attributes = {};
  const setAttr = (prop, value) => {
    if (!isUnsetKeyword(value)) {
      attributes[prop] = value.toString();
    }
  };
  setAttr("width", width);
  setAttr("height", height);
  const viewBox = [box.left, box.top, boxWidth, boxHeight];
  attributes.viewBox = viewBox.join(" ");
  return {
    attributes,
    viewBox,
    body
  };
}
var regex = /\sid="(\S+)"/g;
var randomPrefix = "IconifyId" + Date.now().toString(16) + (Math.random() * 16777216 | 0).toString(16);
var counter = 0;
function replaceIDs(body, prefix = randomPrefix) {
  const ids = [];
  let match;
  while (match = regex.exec(body)) {
    ids.push(match[1]);
  }
  if (!ids.length) {
    return body;
  }
  const suffix = "suffix" + (Math.random() * 16777216 | Date.now()).toString(16);
  ids.forEach((id2) => {
    const newID = typeof prefix === "function" ? prefix(id2) : prefix + (counter++).toString();
    const escapedID = id2.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    body = body.replace(
      // Allowed characters before id: [#;"]
      // Allowed characters after id: [)"], .[a-z]
      new RegExp('([#;"])(' + escapedID + ')([")]|\\.[a-z])', "g"),
      "$1" + newID + suffix + "$3"
    );
  });
  body = body.replace(new RegExp(suffix, "g"), "");
  return body;
}
var storage = /* @__PURE__ */ Object.create(null);
function setAPIModule(provider, item2) {
  storage[provider] = item2;
}
function getAPIModule(provider) {
  return storage[provider] || storage[""];
}
function createAPIConfig(source) {
  let resources;
  if (typeof source.resources === "string") {
    resources = [source.resources];
  } else {
    resources = source.resources;
    if (!(resources instanceof Array) || !resources.length) {
      return null;
    }
  }
  const result = {
    // API hosts
    resources,
    // Root path
    path: source.path || "/",
    // URL length limit
    maxURL: source.maxURL || 500,
    // Timeout before next host is used.
    rotate: source.rotate || 750,
    // Timeout before failing query.
    timeout: source.timeout || 5e3,
    // Randomise default API end point.
    random: source.random === true,
    // Start index
    index: source.index || 0,
    // Receive data after time out (used if time out kicks in first, then API module sends data anyway).
    dataAfterTimeout: source.dataAfterTimeout !== false
  };
  return result;
}
var configStorage = /* @__PURE__ */ Object.create(null);
var fallBackAPISources = [
  "https://api.simplesvg.com",
  "https://api.unisvg.com"
];
var fallBackAPI = [];
while (fallBackAPISources.length > 0) {
  if (fallBackAPISources.length === 1) {
    fallBackAPI.push(fallBackAPISources.shift());
  } else {
    if (Math.random() > 0.5) {
      fallBackAPI.push(fallBackAPISources.shift());
    } else {
      fallBackAPI.push(fallBackAPISources.pop());
    }
  }
}
configStorage[""] = createAPIConfig({
  resources: ["https://api.iconify.design"].concat(fallBackAPI)
});
function addAPIProvider(provider, customConfig) {
  const config2 = createAPIConfig(customConfig);
  if (config2 === null) {
    return false;
  }
  configStorage[provider] = config2;
  return true;
}
function getAPIConfig(provider) {
  return configStorage[provider];
}
var detectFetch = () => {
  let callback;
  try {
    callback = fetch;
    if (typeof callback === "function") {
      return callback;
    }
  } catch (err) {
  }
};
var fetchModule = detectFetch();
function calculateMaxLength(provider, prefix) {
  const config2 = getAPIConfig(provider);
  if (!config2) {
    return 0;
  }
  let result;
  if (!config2.maxURL) {
    result = 0;
  } else {
    let maxHostLength = 0;
    config2.resources.forEach((item2) => {
      const host = item2;
      maxHostLength = Math.max(maxHostLength, host.length);
    });
    const url = prefix + ".json?icons=";
    result = config2.maxURL - maxHostLength - config2.path.length - url.length;
  }
  return result;
}
function shouldAbort(status) {
  return status === 404;
}
var prepare = (provider, prefix, icons) => {
  const results = [];
  const maxLength = calculateMaxLength(provider, prefix);
  const type = "icons";
  let item2 = {
    type,
    provider,
    prefix,
    icons: []
  };
  let length = 0;
  icons.forEach((name, index) => {
    length += name.length + 1;
    if (length >= maxLength && index > 0) {
      results.push(item2);
      item2 = {
        type,
        provider,
        prefix,
        icons: []
      };
      length = name.length;
    }
    item2.icons.push(name);
  });
  results.push(item2);
  return results;
};
function getPath(provider) {
  if (typeof provider === "string") {
    const config2 = getAPIConfig(provider);
    if (config2) {
      return config2.path;
    }
  }
  return "/";
}
var send = (host, params, callback) => {
  if (!fetchModule) {
    callback("abort", 424);
    return;
  }
  let path = getPath(params.provider);
  switch (params.type) {
    case "icons": {
      const prefix = params.prefix;
      const icons = params.icons;
      const iconsList = icons.join(",");
      const urlParams = new URLSearchParams({
        icons: iconsList
      });
      path += prefix + ".json?" + urlParams.toString();
      break;
    }
    case "custom": {
      const uri = params.uri;
      path += uri.slice(0, 1) === "/" ? uri.slice(1) : uri;
      break;
    }
    default:
      callback("abort", 400);
      return;
  }
  let defaultError = 503;
  fetchModule(host + path).then((response) => {
    const status = response.status;
    if (status !== 200) {
      setTimeout(() => {
        callback(shouldAbort(status) ? "abort" : "next", status);
      });
      return;
    }
    defaultError = 501;
    return response.json();
  }).then((data) => {
    if (typeof data !== "object" || data === null) {
      setTimeout(() => {
        if (data === 404) {
          callback("abort", data);
        } else {
          callback("next", defaultError);
        }
      });
      return;
    }
    setTimeout(() => {
      callback("success", data);
    });
  }).catch(() => {
    callback("next", defaultError);
  });
};
var fetchAPIModule = {
  prepare,
  send
};
function sortIcons(icons) {
  const result = {
    loaded: [],
    missing: [],
    pending: []
  };
  const storage2 = /* @__PURE__ */ Object.create(null);
  icons.sort((a, b) => {
    if (a.provider !== b.provider) {
      return a.provider.localeCompare(b.provider);
    }
    if (a.prefix !== b.prefix) {
      return a.prefix.localeCompare(b.prefix);
    }
    return a.name.localeCompare(b.name);
  });
  let lastIcon = {
    provider: "",
    prefix: "",
    name: ""
  };
  icons.forEach((icon) => {
    if (lastIcon.name === icon.name && lastIcon.prefix === icon.prefix && lastIcon.provider === icon.provider) {
      return;
    }
    lastIcon = icon;
    const provider = icon.provider;
    const prefix = icon.prefix;
    const name = icon.name;
    const providerStorage = storage2[provider] || (storage2[provider] = /* @__PURE__ */ Object.create(null));
    const localStorage2 = providerStorage[prefix] || (providerStorage[prefix] = getStorage(provider, prefix));
    let list;
    if (name in localStorage2.icons) {
      list = result.loaded;
    } else if (prefix === "" || localStorage2.missing.has(name)) {
      list = result.missing;
    } else {
      list = result.pending;
    }
    const item2 = {
      provider,
      prefix,
      name
    };
    list.push(item2);
  });
  return result;
}
function removeCallback(storages, id2) {
  storages.forEach((storage2) => {
    const items = storage2.loaderCallbacks;
    if (items) {
      storage2.loaderCallbacks = items.filter((row) => row.id !== id2);
    }
  });
}
function updateCallbacks(storage2) {
  if (!storage2.pendingCallbacksFlag) {
    storage2.pendingCallbacksFlag = true;
    setTimeout(() => {
      storage2.pendingCallbacksFlag = false;
      const items = storage2.loaderCallbacks ? storage2.loaderCallbacks.slice(0) : [];
      if (!items.length) {
        return;
      }
      let hasPending = false;
      const provider = storage2.provider;
      const prefix = storage2.prefix;
      items.forEach((item2) => {
        const icons = item2.icons;
        const oldLength = icons.pending.length;
        icons.pending = icons.pending.filter((icon) => {
          if (icon.prefix !== prefix) {
            return true;
          }
          const name = icon.name;
          if (storage2.icons[name]) {
            icons.loaded.push({
              provider,
              prefix,
              name
            });
          } else if (storage2.missing.has(name)) {
            icons.missing.push({
              provider,
              prefix,
              name
            });
          } else {
            hasPending = true;
            return true;
          }
          return false;
        });
        if (icons.pending.length !== oldLength) {
          if (!hasPending) {
            removeCallback([storage2], item2.id);
          }
          item2.callback(
            icons.loaded.slice(0),
            icons.missing.slice(0),
            icons.pending.slice(0),
            item2.abort
          );
        }
      });
    });
  }
}
var idCounter = 0;
function storeCallback(callback, icons, pendingSources) {
  const id2 = idCounter++;
  const abort = removeCallback.bind(null, pendingSources, id2);
  if (!icons.pending.length) {
    return abort;
  }
  const item2 = {
    id: id2,
    icons,
    callback,
    abort
  };
  pendingSources.forEach((storage2) => {
    (storage2.loaderCallbacks || (storage2.loaderCallbacks = [])).push(item2);
  });
  return abort;
}
function listToIcons(list, validate = true, simpleNames2 = false) {
  const result = [];
  list.forEach((item2) => {
    const icon = typeof item2 === "string" ? stringToIcon(item2, validate, simpleNames2) : item2;
    if (icon) {
      result.push(icon);
    }
  });
  return result;
}
var defaultConfig = {
  resources: [],
  index: 0,
  timeout: 2e3,
  rotate: 750,
  random: false,
  dataAfterTimeout: false
};
function sendQuery(config2, payload, query, done) {
  const resourcesCount = config2.resources.length;
  const startIndex = config2.random ? Math.floor(Math.random() * resourcesCount) : config2.index;
  let resources;
  if (config2.random) {
    let list = config2.resources.slice(0);
    resources = [];
    while (list.length > 1) {
      const nextIndex = Math.floor(Math.random() * list.length);
      resources.push(list[nextIndex]);
      list = list.slice(0, nextIndex).concat(list.slice(nextIndex + 1));
    }
    resources = resources.concat(list);
  } else {
    resources = config2.resources.slice(startIndex).concat(config2.resources.slice(0, startIndex));
  }
  const startTime = Date.now();
  let status = "pending";
  let queriesSent = 0;
  let lastError;
  let timer = null;
  let queue = [];
  let doneCallbacks = [];
  if (typeof done === "function") {
    doneCallbacks.push(done);
  }
  function resetTimer() {
    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  }
  function abort() {
    if (status === "pending") {
      status = "aborted";
    }
    resetTimer();
    queue.forEach((item2) => {
      if (item2.status === "pending") {
        item2.status = "aborted";
      }
    });
    queue = [];
  }
  function subscribe(callback, overwrite) {
    if (overwrite) {
      doneCallbacks = [];
    }
    if (typeof callback === "function") {
      doneCallbacks.push(callback);
    }
  }
  function getQueryStatus() {
    return {
      startTime,
      payload,
      status,
      queriesSent,
      queriesPending: queue.length,
      subscribe,
      abort
    };
  }
  function failQuery() {
    status = "failed";
    doneCallbacks.forEach((callback) => {
      callback(void 0, lastError);
    });
  }
  function clearQueue() {
    queue.forEach((item2) => {
      if (item2.status === "pending") {
        item2.status = "aborted";
      }
    });
    queue = [];
  }
  function moduleResponse(item2, response, data) {
    const isError = response !== "success";
    queue = queue.filter((queued) => queued !== item2);
    switch (status) {
      case "pending":
        break;
      case "failed":
        if (isError || !config2.dataAfterTimeout) {
          return;
        }
        break;
      default:
        return;
    }
    if (response === "abort") {
      lastError = data;
      failQuery();
      return;
    }
    if (isError) {
      lastError = data;
      if (!queue.length) {
        if (!resources.length) {
          failQuery();
        } else {
          execNext();
        }
      }
      return;
    }
    resetTimer();
    clearQueue();
    if (!config2.random) {
      const index = config2.resources.indexOf(item2.resource);
      if (index !== -1 && index !== config2.index) {
        config2.index = index;
      }
    }
    status = "completed";
    doneCallbacks.forEach((callback) => {
      callback(data);
    });
  }
  function execNext() {
    if (status !== "pending") {
      return;
    }
    resetTimer();
    const resource = resources.shift();
    if (resource === void 0) {
      if (queue.length) {
        timer = setTimeout(() => {
          resetTimer();
          if (status === "pending") {
            clearQueue();
            failQuery();
          }
        }, config2.timeout);
        return;
      }
      failQuery();
      return;
    }
    const item2 = {
      status: "pending",
      resource,
      callback: (status2, data) => {
        moduleResponse(item2, status2, data);
      }
    };
    queue.push(item2);
    queriesSent++;
    timer = setTimeout(execNext, config2.rotate);
    query(resource, payload, item2.callback);
  }
  setTimeout(execNext);
  return getQueryStatus;
}
function initRedundancy(cfg) {
  const config2 = {
    ...defaultConfig,
    ...cfg
  };
  let queries = [];
  function cleanup() {
    queries = queries.filter((item2) => item2().status === "pending");
  }
  function query(payload, queryCallback, doneCallback) {
    const query2 = sendQuery(
      config2,
      payload,
      queryCallback,
      (data, error) => {
        cleanup();
        if (doneCallback) {
          doneCallback(data, error);
        }
      }
    );
    queries.push(query2);
    return query2;
  }
  function find(callback) {
    return queries.find((value) => {
      return callback(value);
    }) || null;
  }
  const instance = {
    query,
    find,
    setIndex: (index) => {
      config2.index = index;
    },
    getIndex: () => config2.index,
    cleanup
  };
  return instance;
}
function emptyCallback$1() {
}
var redundancyCache = /* @__PURE__ */ Object.create(null);
function getRedundancyCache(provider) {
  if (!redundancyCache[provider]) {
    const config2 = getAPIConfig(provider);
    if (!config2) {
      return;
    }
    const redundancy = initRedundancy(config2);
    const cachedReundancy = {
      config: config2,
      redundancy
    };
    redundancyCache[provider] = cachedReundancy;
  }
  return redundancyCache[provider];
}
function sendAPIQuery(target, query, callback) {
  let redundancy;
  let send2;
  if (typeof target === "string") {
    const api = getAPIModule(target);
    if (!api) {
      callback(void 0, 424);
      return emptyCallback$1;
    }
    send2 = api.send;
    const cached = getRedundancyCache(target);
    if (cached) {
      redundancy = cached.redundancy;
    }
  } else {
    const config2 = createAPIConfig(target);
    if (config2) {
      redundancy = initRedundancy(config2);
      const moduleKey = target.resources ? target.resources[0] : "";
      const api = getAPIModule(moduleKey);
      if (api) {
        send2 = api.send;
      }
    }
  }
  if (!redundancy || !send2) {
    callback(void 0, 424);
    return emptyCallback$1;
  }
  return redundancy.query(query, send2, callback)().abort;
}
function emptyCallback() {
}
function loadedNewIcons(storage2) {
  if (!storage2.iconsLoaderFlag) {
    storage2.iconsLoaderFlag = true;
    setTimeout(() => {
      storage2.iconsLoaderFlag = false;
      updateCallbacks(storage2);
    });
  }
}
function checkIconNamesForAPI(icons) {
  const valid = [];
  const invalid = [];
  icons.forEach((name) => {
    (name.match(matchIconName) ? valid : invalid).push(name);
  });
  return {
    valid,
    invalid
  };
}
function parseLoaderResponse(storage2, icons, data) {
  function checkMissing() {
    const pending = storage2.pendingIcons;
    icons.forEach((name) => {
      if (pending) {
        pending.delete(name);
      }
      if (!storage2.icons[name]) {
        storage2.missing.add(name);
      }
    });
  }
  if (data && typeof data === "object") {
    try {
      const parsed = addIconSet(storage2, data);
      if (!parsed.length) {
        checkMissing();
        return;
      }
    } catch (err) {
      console.error(err);
    }
  }
  checkMissing();
  loadedNewIcons(storage2);
}
function parsePossiblyAsyncResponse(response, callback) {
  if (response instanceof Promise) {
    response.then((data) => {
      callback(data);
    }).catch(() => {
      callback(null);
    });
  } else {
    callback(response);
  }
}
function loadNewIcons(storage2, icons) {
  if (!storage2.iconsToLoad) {
    storage2.iconsToLoad = icons;
  } else {
    storage2.iconsToLoad = storage2.iconsToLoad.concat(icons).sort();
  }
  if (!storage2.iconsQueueFlag) {
    storage2.iconsQueueFlag = true;
    setTimeout(() => {
      storage2.iconsQueueFlag = false;
      const { provider, prefix } = storage2;
      const icons2 = storage2.iconsToLoad;
      delete storage2.iconsToLoad;
      if (!icons2 || !icons2.length) {
        return;
      }
      const customIconLoader = storage2.loadIcon;
      if (storage2.loadIcons && (icons2.length > 1 || !customIconLoader)) {
        parsePossiblyAsyncResponse(
          storage2.loadIcons(icons2, prefix, provider),
          (data) => {
            parseLoaderResponse(storage2, icons2, data);
          }
        );
        return;
      }
      if (customIconLoader) {
        icons2.forEach((name) => {
          const response = customIconLoader(name, prefix, provider);
          parsePossiblyAsyncResponse(response, (data) => {
            const iconSet = data ? {
              prefix,
              icons: {
                [name]: data
              }
            } : null;
            parseLoaderResponse(storage2, [name], iconSet);
          });
        });
        return;
      }
      const { valid, invalid } = checkIconNamesForAPI(icons2);
      if (invalid.length) {
        parseLoaderResponse(storage2, invalid, null);
      }
      if (!valid.length) {
        return;
      }
      const api = prefix.match(matchIconName) ? getAPIModule(provider) : null;
      if (!api) {
        parseLoaderResponse(storage2, valid, null);
        return;
      }
      const params = api.prepare(provider, prefix, valid);
      params.forEach((item2) => {
        sendAPIQuery(provider, item2, (data) => {
          parseLoaderResponse(storage2, item2.icons, data);
        });
      });
    });
  }
}
var loadIcons = (icons, callback) => {
  const cleanedIcons = listToIcons(icons, true, allowSimpleNames());
  const sortedIcons = sortIcons(cleanedIcons);
  if (!sortedIcons.pending.length) {
    let callCallback = true;
    if (callback) {
      setTimeout(() => {
        if (callCallback) {
          callback(
            sortedIcons.loaded,
            sortedIcons.missing,
            sortedIcons.pending,
            emptyCallback
          );
        }
      });
    }
    return () => {
      callCallback = false;
    };
  }
  const newIcons = /* @__PURE__ */ Object.create(null);
  const sources = [];
  let lastProvider, lastPrefix;
  sortedIcons.pending.forEach((icon) => {
    const { provider, prefix } = icon;
    if (prefix === lastPrefix && provider === lastProvider) {
      return;
    }
    lastProvider = provider;
    lastPrefix = prefix;
    sources.push(getStorage(provider, prefix));
    const providerNewIcons = newIcons[provider] || (newIcons[provider] = /* @__PURE__ */ Object.create(null));
    if (!providerNewIcons[prefix]) {
      providerNewIcons[prefix] = [];
    }
  });
  sortedIcons.pending.forEach((icon) => {
    const { provider, prefix, name } = icon;
    const storage2 = getStorage(provider, prefix);
    const pendingQueue = storage2.pendingIcons || (storage2.pendingIcons = /* @__PURE__ */ new Set());
    if (!pendingQueue.has(name)) {
      pendingQueue.add(name);
      newIcons[provider][prefix].push(name);
    }
  });
  sources.forEach((storage2) => {
    const list = newIcons[storage2.provider][storage2.prefix];
    if (list.length) {
      loadNewIcons(storage2, list);
    }
  });
  return callback ? storeCallback(callback, sortedIcons, sources) : emptyCallback;
};
function mergeCustomisations(defaults, item2) {
  const result = {
    ...defaults
  };
  for (const key in item2) {
    const value = item2[key];
    const valueType = typeof value;
    if (key in defaultIconSizeCustomisations) {
      if (value === null || value && (valueType === "string" || valueType === "number")) {
        result[key] = value;
      }
    } else if (valueType === typeof result[key]) {
      result[key] = key === "rotate" ? value % 4 : value;
    }
  }
  return result;
}
var separator = /[\s,]+/;
function flipFromString(custom, flip) {
  flip.split(separator).forEach((str) => {
    const value = str.trim();
    switch (value) {
      case "horizontal":
        custom.hFlip = true;
        break;
      case "vertical":
        custom.vFlip = true;
        break;
    }
  });
}
function rotateFromString(value, defaultValue = 0) {
  const units = value.replace(/^-?[0-9.]*/, "");
  function cleanup(value2) {
    while (value2 < 0) {
      value2 += 4;
    }
    return value2 % 4;
  }
  if (units === "") {
    const num = parseInt(value);
    return isNaN(num) ? 0 : cleanup(num);
  } else if (units !== value) {
    let split = 0;
    switch (units) {
      case "%":
        split = 25;
        break;
      case "deg":
        split = 90;
    }
    if (split) {
      let num = parseFloat(value.slice(0, value.length - units.length));
      if (isNaN(num)) {
        return 0;
      }
      num = num / split;
      return num % 1 === 0 ? cleanup(num) : 0;
    }
  }
  return defaultValue;
}
function iconToHTML(body, attributes) {
  let renderAttribsHTML = body.indexOf("xlink:") === -1 ? "" : ' xmlns:xlink="http://www.w3.org/1999/xlink"';
  for (const attr in attributes) {
    renderAttribsHTML += " " + attr + '="' + attributes[attr] + '"';
  }
  return '<svg xmlns="http://www.w3.org/2000/svg"' + renderAttribsHTML + ">" + body + "</svg>";
}
function encodeSVGforURL(svg) {
  return svg.replace(/"/g, "'").replace(/%/g, "%25").replace(/#/g, "%23").replace(/</g, "%3C").replace(/>/g, "%3E").replace(/\s+/g, " ");
}
function svgToData(svg) {
  return "data:image/svg+xml," + encodeSVGforURL(svg);
}
function svgToURL(svg) {
  return 'url("' + svgToData(svg) + '")';
}
var defaultExtendedIconCustomisations = {
  ...defaultIconCustomisations,
  inline: false
};
var svgDefaults = {
  "xmlns": "http://www.w3.org/2000/svg",
  "xmlns:xlink": "http://www.w3.org/1999/xlink",
  "aria-hidden": true,
  "role": "img"
};
var commonProps = {
  display: "inline-block"
};
var monotoneProps = {
  backgroundColor: "currentColor"
};
var coloredProps = {
  backgroundColor: "transparent"
};
var propsToAdd = {
  Image: "var(--svg)",
  Repeat: "no-repeat",
  Size: "100% 100%"
};
var propsToAddTo = {
  webkitMask: monotoneProps,
  mask: monotoneProps,
  background: coloredProps
};
for (const prefix in propsToAddTo) {
  const list = propsToAddTo[prefix];
  for (const prop in propsToAdd) {
    list[prefix + prop] = propsToAdd[prop];
  }
}
var customisationAliases = {};
["horizontal", "vertical"].forEach((prefix) => {
  const attr = prefix.slice(0, 1) + "Flip";
  customisationAliases[prefix + "-flip"] = attr;
  customisationAliases[prefix.slice(0, 1) + "-flip"] = attr;
  customisationAliases[prefix + "Flip"] = attr;
});
function fixSize(value) {
  return value + (value.match(/^[-0-9.]+$/) ? "px" : "");
}
var render2 = (icon, props) => {
  const customisations = mergeCustomisations(defaultExtendedIconCustomisations, props);
  const componentProps = { ...svgDefaults };
  const mode = props.mode || "svg";
  const style = {};
  const propsStyle = props.style;
  const customStyle = typeof propsStyle === "object" && !(propsStyle instanceof Array) ? propsStyle : {};
  for (let key in props) {
    const value = props[key];
    if (value === void 0) {
      continue;
    }
    switch (key) {
      case "icon":
      case "style":
      case "onLoad":
      case "mode":
      case "ssr":
        break;
      case "inline":
      case "hFlip":
      case "vFlip":
        customisations[key] = value === true || value === "true" || value === 1;
        break;
      case "flip":
        if (typeof value === "string") {
          flipFromString(customisations, value);
        }
        break;
      case "color":
        style.color = value;
        break;
      case "rotate":
        if (typeof value === "string") {
          customisations[key] = rotateFromString(value);
        } else if (typeof value === "number") {
          customisations[key] = value;
        }
        break;
      case "ariaHidden":
      case "aria-hidden":
        if (value !== true && value !== "true") {
          delete componentProps["aria-hidden"];
        }
        break;
      default: {
        const alias = customisationAliases[key];
        if (alias) {
          if (value === true || value === "true" || value === 1) {
            customisations[alias] = true;
          }
        } else if (defaultExtendedIconCustomisations[key] === void 0) {
          componentProps[key] = value;
        }
      }
    }
  }
  const item2 = iconToSVG(icon, customisations);
  const renderAttribs = item2.attributes;
  if (customisations.inline) {
    style.verticalAlign = "-0.125em";
  }
  if (mode === "svg") {
    componentProps.style = {
      ...style,
      ...customStyle
    };
    Object.assign(componentProps, renderAttribs);
    let localCounter = 0;
    let id2 = props.id;
    if (typeof id2 === "string") {
      id2 = id2.replace(/-/g, "_");
    }
    componentProps["innerHTML"] = replaceIDs(item2.body, id2 ? () => id2 + "ID" + localCounter++ : "iconifyVue");
    return h("svg", componentProps);
  }
  const { body, width, height } = icon;
  const useMask = mode === "mask" || (mode === "bg" ? false : body.indexOf("currentColor") !== -1);
  const html = iconToHTML(body, {
    ...renderAttribs,
    width: width + "",
    height: height + ""
  });
  componentProps.style = {
    ...style,
    "--svg": svgToURL(html),
    "width": fixSize(renderAttribs.width),
    "height": fixSize(renderAttribs.height),
    ...commonProps,
    ...useMask ? monotoneProps : coloredProps,
    ...customStyle
  };
  return h("span", componentProps);
};
allowSimpleNames(true);
setAPIModule("", fetchAPIModule);
if (typeof document !== "undefined" && typeof window !== "undefined") {
  const _window = window;
  if (_window.IconifyPreload !== void 0) {
    const preload = _window.IconifyPreload;
    const err = "Invalid IconifyPreload syntax.";
    if (typeof preload === "object" && preload !== null) {
      (preload instanceof Array ? preload : [preload]).forEach((item2) => {
        try {
          if (
            // Check if item is an object and not null/array
            typeof item2 !== "object" || item2 === null || item2 instanceof Array || // Check for 'icons' and 'prefix'
            typeof item2.icons !== "object" || typeof item2.prefix !== "string" || // Add icon set
            !addCollection(item2)
          ) {
            console.error(err);
          }
        } catch (e) {
          console.error(err);
        }
      });
    }
  }
  if (_window.IconifyProviders !== void 0) {
    const providers = _window.IconifyProviders;
    if (typeof providers === "object" && providers !== null) {
      for (let key in providers) {
        const err = "IconifyProviders[" + key + "] is invalid.";
        try {
          const value = providers[key];
          if (typeof value !== "object" || !value || value.resources === void 0) {
            continue;
          }
          if (!addAPIProvider(key, value)) {
            console.error(err);
          }
        } catch (e) {
          console.error(err);
        }
      }
    }
  }
}
var emptyIcon = {
  ...defaultIconProps,
  body: ""
};
var Icon = defineComponent({
  // Do not inherit other attributes: it is handled by render()
  inheritAttrs: false,
  // Set initial data
  data() {
    return {
      // Current icon name
      _name: "",
      // Loading
      _loadingIcon: null,
      // Mounted status
      iconMounted: false,
      // Callback counter to trigger re-render
      counter: 0
    };
  },
  mounted() {
    this.iconMounted = true;
  },
  unmounted() {
    this.abortLoading();
  },
  methods: {
    abortLoading() {
      if (this._loadingIcon) {
        this._loadingIcon.abort();
        this._loadingIcon = null;
      }
    },
    // Get data for icon to render or null
    getIcon(icon, onload, customise) {
      if (typeof icon === "object" && icon !== null && typeof icon.body === "string") {
        this._name = "";
        this.abortLoading();
        return {
          data: icon
        };
      }
      let iconName;
      if (typeof icon !== "string" || (iconName = stringToIcon(icon, false, true)) === null) {
        this.abortLoading();
        return null;
      }
      let data = getIconData(iconName);
      if (!data) {
        if (!this._loadingIcon || this._loadingIcon.name !== icon) {
          this.abortLoading();
          this._name = "";
          if (data !== null) {
            this._loadingIcon = {
              name: icon,
              abort: loadIcons([iconName], () => {
                this.counter++;
              })
            };
          }
        }
        return null;
      }
      this.abortLoading();
      if (this._name !== icon) {
        this._name = icon;
        if (onload) {
          onload(icon);
        }
      }
      if (customise) {
        data = Object.assign({}, data);
        const customised = customise(data.body, iconName.name, iconName.prefix, iconName.provider);
        if (typeof customised === "string") {
          data.body = customised;
        }
      }
      const classes = ["iconify"];
      if (iconName.prefix !== "") {
        classes.push("iconify--" + iconName.prefix);
      }
      if (iconName.provider !== "") {
        classes.push("iconify--" + iconName.provider);
      }
      return { data, classes };
    }
  },
  // Render icon
  render() {
    this.counter;
    const props = this.$attrs;
    const icon = this.iconMounted || props.ssr ? this.getIcon(props.icon, props.onLoad, props.customise) : null;
    if (!icon) {
      return render2(emptyIcon, props);
    }
    let newProps = props;
    if (icon.classes) {
      newProps = {
        ...props,
        class: (typeof props["class"] === "string" ? props["class"] + " " : "") + icon.classes.join(" ")
      };
    }
    return render2({
      ...defaultIconProps,
      ...icon.data
    }, newProps);
  }
});

// node_modules/@fast-crud/fast-crud/dist/index-7517cf48.mjs
var import_dayjs = __toESM(require_dayjs_min(), 1);
var Hi = Object.defineProperty;
var zi = (e, t, n) => t in e ? Hi(e, t, { enumerable: true, configurable: true, writable: true, value: n }) : e[t] = n;
var se = (e, t, n) => (zi(e, typeof t != "symbol" ? t + "" : t, n), n);
var Jo = (e, t, n) => {
  if (!t.has(e))
    throw TypeError("Cannot " + n);
};
var m = (e, t, n) => (Jo(e, t, "read from private field"), n ? n.call(e) : t.get(e));
var pe = (e, t, n) => {
  if (t.has(e))
    throw TypeError("Cannot add the same private member more than once");
  t instanceof WeakSet ? t.add(e) : t.set(e, n);
};
var de = (e, t, n, o) => (Jo(e, t, "write to private field"), o ? o.call(e, n) : t.set(e, n), n);
var mo = (e, t, n, o) => ({
  set _(r) {
    de(e, t, r, n);
  },
  get _() {
    return m(e, t, o);
  }
});
var te = (e, t, n) => (Jo(e, t, "access private method"), n);
var Zn = {
  commonOptions(e) {
    return {};
  },
  defaultOptions(e) {
    const { t } = e, n = (r) => computed(() => t(r)), o = i.get();
    return {
      settings: {
        plugins: {
          mobile: {
            enabled: true,
            props: {
              isMobile: computed(() => window.innerWidth < 768)
            }
          }
        }
      },
      mode: {},
      status: {},
      search: {
        container: {
          is: "fs-search-layout-default",
          collapse: true,
          col: {
            span: 4
          }
        },
        formItem: {
          wrapperCol: {
            style: {
              width: "50%"
            }
          }
        },
        options: {
          ...o.form.inlineLayout,
          // n-form 是否显示校验反馈
          showFeedback: false
        },
        onValidateError({ trigger: r }) {
          r === "search" && o.notification.error({ message: t("fs.search.error.message") });
        },
        collapse: true,
        show: true,
        buttons: {
          search: {
            className: {
              "fs-search-btn-search": true
            },
            icon: o.icons.search
          },
          reset: {
            className: {
              "fs-search-btn-reset": true
            },
            icon: o.icons.refresh
          }
        }
      },
      form: {
        labelPlacement: "left",
        labelPosition: "right",
        labelWidth: "120px",
        style: {
          "grid-template-columns": "50% 50%"
          // grid布局，默认两列
        },
        row: {
          gutter: 10
        },
        col: { span: 12 },
        labelAlign: "right",
        labelCol: { span: 4 },
        wrapperCol: { span: 18 },
        wrapper: {
          is: o.dialog.name,
          ...o.formWrapper.buildWidthBind(o.dialog.name, "960px"),
          ...o.formWrapper.buildInitBind(o.dialog.name),
          dragenabled: true,
          destroyOnClose: true,
          ...o.dialog.footer(),
          buttons: {
            cancel: {
              text: n("fs.form.cancel"),
              order: 1,
              click: ({ doClose: r }) => {
                r();
              }
            },
            reset: {
              text: n("fs.form.reset"),
              order: 1,
              click: ({ reset: r }) => {
                r();
              }
            },
            ok: {
              text: n("fs.form.ok"),
              order: 1,
              type: "primary",
              click: async ({ submit: r }) => {
                await r();
              }
            }
          }
        }
      },
      addForm: {
        wrapper: {
          title: n("fs.addForm.title")
        }
      },
      editForm: {
        wrapper: {
          title: n("fs.editForm.title")
        }
      },
      viewForm: {
        wrapper: {
          title: n("fs.viewForm.title"),
          buttons: {
            reset: {
              show: false
            },
            cancel: {
              show: false
            }
          }
        }
      },
      rowHandle: {
        width: 250,
        title: n("fs.rowHandle.title"),
        order: 1e3,
        dropdown: {
          // 操作列折叠
          more: {
            text: null,
            type: "primary",
            icon: o.icons.more
          }
        }
      },
      pagination: {
        background: true,
        pageSize: 20,
        [o.pagination.currentPage]: 1,
        [o.pagination.total]: 1,
        pageSizes: [5, 10, 20, 50],
        layout: "total, sizes, prev, pager, next, jumper",
        showSizeChanger: true,
        showQuickJumper: true,
        showSizePicker: true,
        showTotal: (r) => t("fs.pagination.showTotal", [r])
        //antdv
      },
      table: {
        show: true,
        height: "100%",
        rowKey: o.table.defaultRowKey,
        stripe: true,
        border: true,
        bordered: true,
        singleLine: false,
        scrollTopOnRefreshed: true,
        editable: { enabled: false, rowKey: "$editable_id" },
        pagination: false
        //antdv 关闭默认分页
      },
      toolbar: {
        compact: true,
        buttons: {
          search: {
            className: {
              "fs-toolbar-btn-search": true
            }
          },
          compact: {
            className: {
              "fs-toolbar-btn-compact": true
            }
          },
          refresh: {
            className: {
              "fs-toolbar-btn-refresh": true
            }
          },
          export: {
            className: {
              "fs-toolbar-btn-export": true
            }
          },
          columns: {
            className: {
              "fs-toolbar-btn-columns": true
            }
          }
        }
      },
      actionbar: {
        buttons: {
          add: {
            className: {
              "fs-actionbar-btn-add": true
            },
            type: "primary",
            text: n("fs.actionbar.add")
          }
        }
      }
    };
  }
};
function ps() {
  var t;
  return (t = new Error().stack) == null ? void 0 : t.split(`
`)[3];
}
var Mt = (...e) => {
};
function gr(...e) {
  console.log.apply(this, arguments);
}
function gs(...e) {
  console.warn.apply(this, arguments);
}
function vs(...e) {
  console.error.apply(this, arguments);
}
var bs = (...e) => {
  vs("%c [error]", "font-weight: 600;", ...e);
};
var ys = (...e) => {
  gs("%c [warn]", "font-weight: 600;", ...e);
};
var Zr = (...e) => {
  gr("%c [info]", "font-weight: 600;", ...e);
};
var ws = (...e) => {
  if (!console.log)
    return;
  const t = ps();
  {
    const n = ["%c [debug]", "font-weight: 600;", ...e];
    gr(...n);
    const o = ["%c " + t, "color:#999"];
    gr(...o);
  }
};
var ue = {
  debug: Mt,
  info: Mt,
  warn: Mt,
  error: Mt,
  log: Mt
};
function _s(e = {}) {
  const t = (e == null ? void 0 : e.level) || "info";
  switch (ue.debug = Mt, ue.info = Mt, ue.warn = Mt, ue.error = Mt, ue.log = Mt, t) {
    case "debug":
      ue.debug = ws;
    case "info":
      ue.info = Zr, ue.log = Zr;
    case "warn":
      ue.warn = ys;
    case "error":
      ue.error = bs;
      break;
  }
}
_s();
function Cs(e) {
  return e == null || e === "";
}
function Ss(...e) {
  for (const t of e)
    if (!(t == null || t === ""))
      return false;
  return false;
}
function Fs(...e) {
  for (const t of e)
    if (t == null || t === "")
      return true;
  return false;
}
var Lr = {
  isEmpty: Cs,
  isAllEmpty: Ss,
  hasEmpty: Fs
};
function Rs(e, t = false) {
  t && onRenderTriggered((n) => {
    n.key, n.target, n.type;
  });
}
function qr(e) {
  return defineAsyncComponent({
    loader: e,
    onError(t, n, o, r) {
      console.error("load error", t), t.message.match(/fetch/) && r <= 3 ? n() : o();
    }
  });
}
function Ds(e, t, n, o) {
  const r = qr(n);
  e.component(t, r, o);
}
function ks(e, t, n, o, r) {
  const a = uo(t, o, r);
  forEach_default(a, (i2, s) => {
    n && n.indexOf(s) != -1 || Ds(e, s, i2, null);
  });
}
function $s(e, t, n, o, r) {
  const a = uo(t, o, r);
  forEach_default(a, (i2, s) => {
    n && n.indexOf(s) || e.component(s, i2.default);
  });
}
function uo(e, t, n) {
  const o = {};
  return t == null && (t = /.*\/(.+).(vue|jsx|tsx)/), forEach_default(e, (r, a) => {
    const i2 = a.match(t);
    if ((i2 == null ? void 0 : i2.length) <= 1) {
      console.error(`"${a}" can't pick a component name,this component can't register`);
      return;
    }
    let s = i2[1];
    s = camelCase_default(s), s = upperFirst_default(s), n && (r = n(r)), o[s] = r;
  }), o;
}
function Os(e) {
  const t = uo(e), n = {};
  return forEach_default(t, (o, r) => {
    n[r] = qr(o);
  }), n;
}
function Es(e) {
  const t = uo(e), n = {};
  return forEach_default(t, (o, r) => {
    n[r] = o.default;
  }), n;
}
var Ts = {
  transformFromGlob: uo,
  installAsyncComponents: ks,
  installSyncComponents: $s,
  createAsyncComponent: qr,
  loadAsyncComponentFromGlob: Os,
  loadComponentFromGlob: Es
};
var Ba = class {
  constructor(t) {
    se(this, "remoteStorage");
    se(this, "$router");
    se(this, "tableName");
    se(this, "keyType");
    se(this, "id");
    this.remoteStorage = t.remoteStorage, this.$router = t.$router, this.tableName = t.tableName, this.keyType = t.keyType, this.id = t.id;
  }
  getTableId() {
    const t = this.tableName;
    let n = "fs-crud";
    return this.id && (n = n + "." + this.id), t && typeof t == "string" ? n + "." + t : n;
  }
  async getTable() {
    const t = this.getTableId(), n = localStorage.getItem(t);
    if (n != null)
      return JSON.parse(n);
  }
  async saveTable(t) {
    const n = this.getTableId();
    localStorage.setItem(n, JSON.stringify(t));
  }
  async clearTable() {
    const t = this.getTableId();
    localStorage.removeItem(t);
  }
  async updateTableValue(t, n) {
    if (n == null && (n = this.getItemKey()), this.remoteStorage) {
      await this.remoteStorage.set(n, t);
      return;
    }
    let o = await this.getTable();
    o == null && (o = {}), o[n] = t, await this.saveTable(o);
  }
  getItemKey() {
    const t = this.$router, n = this.keyType;
    let o = location.href;
    return t && (o = t.path), this.id && (o = o + "." + this.id), n == null || typeof n != "string" || !n ? o : o + "." + n;
  }
  async getTableValue(t) {
    if (t == null && (t = this.getItemKey()), this.remoteStorage)
      return await this.remoteStorage.get(t);
    const n = await this.getTable();
    return n == null ? null : n[t];
  }
  async clearTableValue(t) {
    if (t == null && (t = this.getItemKey()), this.remoteStorage) {
      await this.remoteStorage.remove(t);
      return;
    }
    const n = await this.getTable();
    n != null && (delete n[t], await this.saveTable(n));
  }
};
var As = /\D/;
var Is = /^[a-zA-Z_$]+([\w_$]*)$/;
var Vs = /"/g;
function ea(...e) {
  return e.reduce((t, n) => t ? !n || n.startsWith("[") ? `${t}${n}` : `${t}.${n}` : n, "");
}
function ja(e) {
  function t(n, ...o) {
    if (o = o.filter((r) => r !== void 0), e.isString(n))
      return ea(...o, n);
    if (Array.isArray(n))
      return o = ea(...o), n.reduce((r, a) => {
        const i2 = typeof a;
        return i2 === "number" ? a < 0 || a % 1 !== 0 ? `${r}["${a}"]` : `${r}[${a}]` : i2 !== "string" ? `${r}["${a}"]` : a ? As.test(a) ? Is.test(a) ? r ? `${r}.${a}` : `${r}${a}` : `${r}["${a.replace(Vs, '\\"')}"]` : `${r}[${a}]` : `${r}[""]`;
      }, o);
  }
  return t;
}
ja.notChainable = true;
var Ps = /^[a-zA-Z_$]+([\w_$]*)$/;
var Ms = /"/g;
var Na = Object.prototype.hasOwnProperty;
function Bs(e) {
  const t = typeof e;
  return e != null && (t == "object" || t == "function");
}
function js(e) {
  const t = ja(e);
  function n(a) {
    const { options: i2, obj: s, callback: u } = a;
    i2.pathFormatArray = i2.pathFormat == "array", a.depth = 0;
    let l = false;
    const c = () => (l = true, false);
    for (; a && !l; ) {
      if (!a.inited) {
        if (a.inited = true, a.info = na(a.value, i2.ownPropertiesOnly), i2.checkCircular && (a.circularParentIndex = -1, a.circularParent = null, a.isCircular = false, a.info.isObject && !a.info.isEmpty)) {
          let d = a.parent;
          for (; d; ) {
            if (d.value === a.value) {
              a.isCircular = true, a.circularParent = d, a.circularParentIndex = a.depth - d.depth - 1;
              break;
            }
            d = d.parent;
          }
        }
        if (a.children = [], i2.childrenPath && i2.childrenPath.forEach((d, f) => {
          const h2 = e.get(a.value, d), v = na(h2, i2.ownPropertiesOnly);
          v.isEmpty || a.children.push([d, i2.strChildrenPath[f], h2, v]);
        }), a.isLeaf = a.isCircular || i2.childrenPath !== void 0 && !a.children.length || !a.info.isObject || a.info.isEmpty, a.needCallback = (a.depth || i2.includeRoot) && (!i2.leavesOnly || a.isLeaf), a.needCallback) {
          const d = new ta(s, i2, c);
          d.setItem(a, false);
          try {
            a.res = u(a.value, a.key, a.parent && a.parent.value, d);
          } catch (f) {
            throw f.message && (f.message += `
callback failed before deep iterate at:
` + t(a.path)), f;
          }
        }
        if (l)
          break;
        a.res !== false && !l && !a.isCircular && a.info.isObject && (i2.childrenPath !== void 0 && (a.depth || !i2.rootIsChildren) ? (a.childrenItems = [], a.children.length && a.children.forEach(([d, f, h2, v]) => {
          a.childrenItems = [
            ...a.childrenItems,
            ...v.isArray ? o(a, h2, i2, d, f) : r(a, h2, i2, d, f)
          ];
        })) : a.childrenItems = a.info.isArray ? o(a, a.value, i2, [], "") : r(a, a.value, i2, [], "")), a.currentChildIndex = -1;
      }
      if (a.childrenItems && a.currentChildIndex < a.childrenItems.length - 1) {
        a.currentChildIndex++, a.childrenItems[a.currentChildIndex].parentItem = a, a = a.childrenItems[a.currentChildIndex];
        continue;
      }
      if (a.needCallback && i2.callbackAfterIterate) {
        const d = new ta(s, i2, c);
        d.setItem(a, true);
        try {
          u(a.value, a.key, a.parent && a.parent.value, d);
        } catch (f) {
          throw f.message && (f.message += `
callback failed after deep iterate at:
` + t(a.path)), f;
        }
      }
      a = a.parentItem;
    }
  }
  return n;
  function o(a, i2, s, u, l) {
    let c;
    s.pathFormatArray || (c = a.strPath || "", l && c && !l.startsWith("[") && (c += "."), c += l || "");
    const d = [];
    for (let f = 0; f < i2.length; f++) {
      const h2 = i2[f];
      if (h2 === void 0 && !(f in i2))
        continue;
      let v;
      const g = !s.pathFormatArray;
      g && (v = `${c}[${f}]`), d.push({
        value: h2,
        key: f + "",
        path: [...a.path || [], ...u, f + ""],
        strPath: v,
        depth: a.depth + 1,
        parent: {
          value: a.value,
          key: a.key,
          path: g ? a.strPath : a.path,
          parent: a.parent,
          depth: a.depth,
          info: a.info
        },
        childrenPath: u.length && u || void 0,
        strChildrenPath: l || void 0
      });
    }
    return d;
  }
  function r(a, i2, s, u, l) {
    let c;
    s.pathFormatArray || (c = a.strPath || "", l && c && !l.startsWith("[") && (c += "."), c += l || "");
    const d = [], f = !s.pathFormatArray;
    for (const h2 in i2) {
      if (s.ownPropertiesOnly && !Na.call(i2, h2))
        continue;
      let v;
      f && (Ps.test(h2) ? c ? v = `${c}.${h2}` : v = `${h2}` : v = `${c}["${h2.replace(Ms, '\\"')}"]`), d.push({
        value: i2[h2],
        key: h2,
        path: [...a.path || [], ...u, h2],
        strPath: v,
        depth: a.depth + 1,
        parent: {
          value: a.value,
          key: a.key,
          path: f ? a.strPath : a.path,
          parent: a.parent,
          depth: a.depth,
          info: a.info
        },
        childrenPath: u.length && u || void 0,
        strChildrenPath: l || void 0
      });
    }
    return d;
  }
}
var ta = class {
  constructor(t, n, o) {
    se(this, "_item");
    se(this, "obj");
    se(this, "_options");
    se(this, "afterIterate");
    this.obj = t, this._options = n, this.break = o;
  }
  setItem(t, n) {
    this._item = t, this.afterIterate = n;
  }
  get path() {
    return this._options.pathFormatArray ? this._item.path : this._item.strPath;
  }
  get parent() {
    return this._item.parent;
  }
  get parents() {
    if (!this._item._parents) {
      this._item._parents = [];
      let t = this._item.parent;
      for (; t; )
        this._item._parents[t.depth] = t, t = t.parent;
    }
    return this._item._parents;
  }
  get depth() {
    return this._item.depth;
  }
  get isLeaf() {
    return this._item.isLeaf;
  }
  get isCircular() {
    return this._item.isCircular;
  }
  get circularParentIndex() {
    return this._item.circularParentIndex;
  }
  get circularParent() {
    return this._item.circularParent;
  }
  get childrenPath() {
    return this._options.childrenPath !== void 0 && (this._options.pathFormatArray ? this._item.childrenPath : this._item.strChildrenPath) || void 0;
  }
  get info() {
    return this._item.info;
  }
};
function Ns(e, t) {
  for (const n in e)
    if (!t || Na.call(e, n))
      return false;
  return true;
}
function na(e, t) {
  const n = { isObject: Bs(e) };
  return n.isArray = n.isObject && Array.isArray(e), n.isEmpty = n.isArray ? !e.length : n.isObject ? Ns(e, t) : true, n;
}
function Ls(e) {
  const t = js(e);
  function n(o, r, a) {
    if (r === void 0 && (r = identity_default), a = merge_default({
      includeRoot: !Array.isArray(o),
      pathFormat: "string",
      checkCircular: false,
      leavesOnly: false,
      ownPropertiesOnly: true
      //
    }, a || {}), a.childrenPath !== void 0) {
      if (!a.includeRoot && a.rootIsChildren === void 0 && (a.rootIsChildren = Array.isArray(o)), !isString_default(a.childrenPath) && !Array.isArray(a.childrenPath))
        throw Error("childrenPath can be string or array");
      isString_default(a.childrenPath) && (a.childrenPath = [a.childrenPath]), a.strChildrenPath = a.childrenPath, a.childrenPath = [];
      for (let i2 = a.strChildrenPath.length - 1; i2 >= 0; i2--)
        a.childrenPath[i2] = toPath_default(a.strChildrenPath[i2]);
    }
    return t({
      value: o,
      callback: r,
      options: a,
      obj: o
    }), o;
  }
  return n;
}
var qs = Ls({ isString: isString_default });
var La = {
  forEachDeep: qs
};
var xs = {
  /**
   * 重构object，但忽略某些字段
   * @param ref
   * @param skips
   */
  omit(e, ...t) {
    const n = Object.keys(e.value), o = {};
    for (const r of n)
      r !== "loading" && (t.indexOf(r) >= 0 || (o[r] = e.value[r]));
    return o;
  }
};
var vt = {
  logger: ue,
  strings: Lr,
  trace: Rs,
  vite: Ts,
  store: Ba,
  deepdash: La,
  dash: xs
};
function Zo(e, t) {
  return e.type !== "antdv" ? {} : { labelCol: { span: t }, wrapperCol: { span: 23 - t } };
}
function Hs() {
  const { ui: e } = B();
  return {
    colspan: {
      //跨列
      form: {
        col: { span: 24 },
        ...Zo(e, 2)
      }
    },
    colspan3: {
      //跨列
      form: {
        col: { span: 24 },
        ...Zo(e, 3)
      }
    },
    colspan4: {
      //跨列
      form: {
        col: { span: 24 },
        ...Zo(e, 4)
      }
    }
  };
}
var zs = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: Hs
}, Symbol.toStringTag, { value: "Module" }));
function Ws() {
  const e = i.get();
  return {
    button: {
      form: {
        component: {
          name: e.input.name,
          vModel: e.input.modelValue,
          [e.input.clearable]: true
        }
      },
      column: {
        component: {
          name: "fs-button",
          vModel: "text"
        }
      }
    },
    link: {
      form: {
        component: {
          name: e.input.name,
          vModel: e.input.modelValue,
          [e.input.clearable]: true
        }
      },
      column: {
        component: {
          name: "fs-button",
          vModel: "text",
          ...e.button.linkType
        }
      }
    }
  };
}
var Us = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: Ws
}, Symbol.toStringTag, { value: "Module" }));
function Ks() {
  const e = i.get();
  return {
    "dict-cascader": {
      search: {
        component: {
          clearable: true
        }
      },
      form: {
        component: {
          name: "fs-dict-cascader",
          vModel: e.cascader.modelValue,
          [e.cascader.clearable]: true
        }
      },
      column: {
        component: { name: "fs-dict-cascader-format" }
      }
    }
  };
}
var Ys = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: Ks
}, Symbol.toStringTag, { value: "Module" }));
var qa = {
  name: "zh-cn",
  fs: {
    component: {
      select: {
        placeholder: "请选择"
      }
    },
    addForm: { title: "添加" },
    editForm: { title: "编辑" },
    viewForm: { title: "查看" },
    rowHandle: {
      title: "操作",
      remove: {
        text: "删除",
        confirmTitle: "删除提示",
        confirmMessage: "您确定要删除该记录吗?",
        success: "删除成功!"
      },
      copy: {
        text: "复制"
      },
      edit: {
        text: "编辑"
      },
      view: {
        text: "查看"
      }
    },
    form: {
      cancel: "取消",
      ok: "确定",
      reset: "重置",
      saveRemind: {
        title: "提示",
        content: "表单数据有变更，是否保存",
        cancel: "不保存",
        ok: "保存"
      }
    },
    actionbar: { add: "添加" },
    toolbar: {
      columnFilter: {
        title: "列设置",
        fixed: "固定",
        order: "排序",
        reset: "还原",
        confirm: "确定",
        unnamed: "未命名"
      },
      search: { title: "查询显示" },
      refresh: { title: "刷新" },
      compact: { title: "紧凑模式" },
      export: { title: "导出" },
      columns: { title: "列设置" }
    },
    search: {
      container: {
        collapseButton: {
          text: {
            collapse: "收起",
            expand: "展开"
          }
        }
      },
      search: { text: "查询" },
      reset: { text: "重置" },
      error: {
        message: "查询表单校验失败"
      }
    },
    pagination: {
      showTotal: "共 {0} 条"
    },
    date: {
      formatter: { to: "至" }
    },
    extends: {
      tableSelect: {
        view: "查看",
        select: "选择"
      },
      cropper: {
        title: "图片裁剪",
        preview: "预览",
        reChoose: "重新选择",
        flipX: "左右翻转",
        flipY: "上下翻转",
        reset: "重置",
        cancel: "取消",
        confirm: "确定",
        chooseImage: "+ 选择图片",
        onlySupport: "仅支持",
        sizeLimit: "大小不能超过",
        sizeNoLimit: "大小不限制"
      },
      fileUploader: {
        text: "文件上传",
        limitTip: "文件数量不能超过 {0}",
        sizeLimitTip: "文件大小不能超过 {0},当前大小：{1}",
        loadError: "图片加载失败",
        pixelLimitTip: "图片像素尺寸不能超过 宽:{0},高:{1}",
        hasUploading: "还有文件正在上传，请等待上传完成，或删除它"
      }
    }
  }
};
var Gs = {
  name: "en",
  fs: {
    component: {
      select: {
        placeholder: "please select"
      }
    },
    addForm: { title: "add" },
    editForm: { title: "edit" },
    viewForm: { title: "view" },
    rowHandle: {
      title: "handle",
      remove: {
        text: "remove",
        confirmTitle: "remove tip",
        confirmMessage: "Are you sure you want to delete this record?",
        success: "delete success!"
      },
      copy: {
        text: "copy"
      },
      edit: {
        text: "edit"
      },
      view: {
        text: "view"
      }
    },
    form: {
      cancel: "cancel",
      ok: "ok",
      reset: "reset",
      saveRemind: {
        title: "save remind",
        content: "The form data has changed, whether to save",
        cancel: "don't save",
        ok: "save"
      }
    },
    actionbar: { add: "add" },
    toolbar: {
      columnFilter: {
        title: "columns set",
        fixed: "fixed",
        order: "sort",
        reset: "reset",
        confirm: "ok",
        unnamed: "unnamed"
      },
      search: { title: "show search bar" },
      refresh: { title: "refresh" },
      compact: { title: "compact mode" },
      export: { title: "export" },
      columns: { title: "columns set" }
    },
    search: {
      container: {
        collapseButton: {
          text: {
            collapse: "collapse",
            expand: "expand"
          }
        }
      },
      search: { text: "search" },
      reset: { text: "reset" },
      error: {
        message: "form valid error"
      }
    },
    pagination: {
      showTotal: "Total {0} items"
    },
    date: { formatter: { to: "to" } },
    extends: {
      tableSelect: {
        view: "view",
        select: "select"
      },
      cropper: {
        title: "image crop",
        preview: "preview",
        reChoose: "reChoose",
        flipX: "flipX",
        flipY: "flipY",
        reset: "reset",
        cancel: "cancel",
        confirm: "confirm",
        chooseImage: "+ choose image",
        onlySupport: "only",
        sizeLimit: "size limit",
        sizeNoLimit: " no limit"
      },
      fileUploader: {
        text: "fileUpload",
        limitTip: "file count limit: {0}",
        sizeLimitTip: "file size limit:  {0}, current size: {1}",
        loadError: "image load error",
        pixelLimitTip: "pixel limit : width:{0},height:{1}",
        hasUploading: "The file is being uploaded, please wait for the upload to complete or delete"
      }
    }
  }
};
function Xs(e, t) {
  let n = get_default(qa, e);
  return n == null ? e : t instanceof Array ? (forEach_default(t, (o, r) => {
    n = n.replace("{" + r + "}", o);
  }), n) : n.replace("{n}", t);
}
var Qs = class {
  constructor() {
    se(this, "vueI18nInstance", null);
  }
  t(t, n) {
    return Xs(t, n);
  }
  setVueI18n(t) {
    if (!t)
      return;
    t.global && (t = t.global);
    const n = t.availableLocales;
    for (const o of n) {
      if (o.startsWith("zh")) {
        const r = t.getLocaleMessage(o), a = cloneDeep_default(r.fs || {});
        t.mergeLocaleMessage(o, { fs: qa.fs }), t.mergeLocaleMessage(o, { fs: a });
      } else if (o.startsWith("en")) {
        const r = t.getLocaleMessage(o), a = cloneDeep_default(r.fs || {});
        t.mergeLocaleMessage(o, { fs: Gs.fs }), t.mergeLocaleMessage(o, { fs: a });
      }
      ue.debug("i18n", t.getLocaleMessage(o));
    }
    this.vueI18nInstance = t;
  }
};
var Co = new Qs();
function ot() {
  return Co.vueI18nInstance != null ? {
    // @ts-ignore
    t: Co.vueI18nInstance.t
  } : { t: Co.t };
}
var Ko = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {};
function Yo(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
var xa = { exports: {} };
(function(e, t) {
  (function(n, o) {
    e.exports = o();
  })(Ko, function() {
    return function(n, o) {
      var r = o.prototype, a = r.format;
      r.format = function(i2) {
        var s = this, u = this.$locale();
        if (!this.isValid())
          return a.bind(this)(i2);
        var l = this.$utils(), c = (i2 || "YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g, function(d) {
          switch (d) {
            case "Q":
              return Math.ceil((s.$M + 1) / 3);
            case "Do":
              return u.ordinal(s.$D);
            case "gggg":
              return s.weekYear();
            case "GGGG":
              return s.isoWeekYear();
            case "wo":
              return u.ordinal(s.week(), "W");
            case "w":
            case "ww":
              return l.s(s.week(), d === "w" ? 1 : 2, "0");
            case "W":
            case "WW":
              return l.s(s.isoWeek(), d === "W" ? 1 : 2, "0");
            case "k":
            case "kk":
              return l.s(String(s.$H === 0 ? 24 : s.$H), d === "k" ? 1 : 2, "0");
            case "X":
              return Math.floor(s.$d.getTime() / 1e3);
            case "x":
              return s.$d.getTime();
            case "z":
              return "[" + s.offsetName() + "]";
            case "zzz":
              return "[" + s.offsetName("long") + "]";
            default:
              return d;
          }
        });
        return a.bind(this)(c);
      };
    };
  });
})(xa);
var Js = xa.exports;
var Zs = Yo(Js);
import_dayjs.default.extend(Zs);
function er(e, t) {
  if (!Lr.isEmpty(e))
    return (0, import_dayjs.default)(e).format(t);
}
function Ha(e, t = "YYYY-MM-DD HH:mm:ss") {
  if (e != null && e instanceof Array && e.length > 1) {
    if (Lr.hasEmpty(e))
      return;
    const { t: n } = ot();
    return `${er(e[0], t)} ${n("fs.date.formatter.to")} ${er(e[1], t)}`;
  }
  return er(e, t);
}
function el(e) {
  const { value: t } = e;
  return Ha(t, "YYYY-MM-DD");
}
function tl(e) {
  const { value: t } = e;
  return Ha(t, "YYYY-MM-DD HH:mm:ss");
}
var za = { exports: {} };
(function(e, t) {
  (function(n, o) {
    e.exports = o();
  })(Ko, function() {
    var n = "week", o = "year";
    return function(r, a, i2) {
      var s = a.prototype;
      s.week = function(u) {
        if (u === void 0 && (u = null), u !== null)
          return this.add(7 * (u - this.week()), "day");
        var l = this.$locale().yearStart || 1;
        if (this.month() === 11 && this.date() > 25) {
          var c = i2(this).startOf(o).add(1, o).date(l), d = i2(this).endOf(n);
          if (c.isBefore(d))
            return 1;
        }
        var f = i2(this).startOf(o).date(l).startOf(n).subtract(1, "millisecond"), h2 = this.diff(f, n, true);
        return h2 < 0 ? i2(this).startOf("week").week() : Math.ceil(h2);
      }, s.weeks = function(u) {
        return u === void 0 && (u = null), this.week(u);
      };
    };
  });
})(za);
var nl = za.exports;
var ol = Yo(nl);
var Wa = { exports: {} };
(function(e, t) {
  (function(n, o) {
    e.exports = o();
  })(Ko, function() {
    return function(n, o) {
      o.prototype.weekday = function(r) {
        var a = this.$locale().weekStart || 0, i2 = this.$W, s = (i2 < a ? i2 + 7 : i2) - a;
        return this.$utils().u(r) ? s : this.subtract(s, "day").add(r, "day");
      };
    };
  });
})(Wa);
var rl = Wa.exports;
var al = Yo(rl);
var Ua = { exports: {} };
(function(e, t) {
  (function(n, o) {
    e.exports = o();
  })(Ko, function() {
    return function(n, o, r) {
      var a = o.prototype, i2 = function(d) {
        return d && (d.indexOf ? d : d.s);
      }, s = function(d, f, h2, v, g) {
        var w = d.name ? d : d.$locale(), y = i2(w[f]), R = i2(w[h2]), F = y || R.map(function(A) {
          return A.slice(0, v);
        });
        if (!g)
          return F;
        var k = w.weekStart;
        return F.map(function(A, N) {
          return F[(N + (k || 0)) % 7];
        });
      }, u = function() {
        return r.Ls[r.locale()];
      }, l = function(d, f) {
        return d.formats[f] || function(h2) {
          return h2.replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g, function(v, g, w) {
            return g || w.slice(1);
          });
        }(d.formats[f.toUpperCase()]);
      }, c = function() {
        var d = this;
        return { months: function(f) {
          return f ? f.format("MMMM") : s(d, "months");
        }, monthsShort: function(f) {
          return f ? f.format("MMM") : s(d, "monthsShort", "months", 3);
        }, firstDayOfWeek: function() {
          return d.$locale().weekStart || 0;
        }, weekdays: function(f) {
          return f ? f.format("dddd") : s(d, "weekdays");
        }, weekdaysMin: function(f) {
          return f ? f.format("dd") : s(d, "weekdaysMin", "weekdays", 2);
        }, weekdaysShort: function(f) {
          return f ? f.format("ddd") : s(d, "weekdaysShort", "weekdays", 3);
        }, longDateFormat: function(f) {
          return l(d.$locale(), f);
        }, meridiem: this.$locale().meridiem, ordinal: this.$locale().ordinal };
      };
      a.localeData = function() {
        return c.bind(this)();
      }, r.localeData = function() {
        var d = u();
        return { firstDayOfWeek: function() {
          return d.weekStart || 0;
        }, weekdays: function() {
          return r.weekdays();
        }, weekdaysShort: function() {
          return r.weekdaysShort();
        }, weekdaysMin: function() {
          return r.weekdaysMin();
        }, months: function() {
          return r.months();
        }, monthsShort: function() {
          return r.monthsShort();
        }, longDateFormat: function(f) {
          return l(d, f);
        }, meridiem: d.meridiem, ordinal: d.ordinal };
      }, r.months = function() {
        return s(u(), "months");
      }, r.monthsShort = function() {
        return s(u(), "monthsShort", "months", 3);
      }, r.weekdays = function(d) {
        return s(u(), "weekdays", null, null, d);
      }, r.weekdaysShort = function(d) {
        return s(u(), "weekdaysShort", "weekdays", 3, d);
      }, r.weekdaysMin = function(d) {
        return s(u(), "weekdaysMin", "weekdays", 2, d);
      };
    };
  });
})(Ua);
var il = Ua.exports;
var sl = Yo(il);
import_dayjs.default.extend(al);
import_dayjs.default.extend(sl);
import_dayjs.default.extend(ol);
function ll() {
  const e = i.get();
  function t(o) {
    const { row: r, key: a, value: i2 } = o;
    i2 != null && (e.type === "naive" ? r[a] = (0, import_dayjs.default)(i2).valueOf() : e.type === "antdv" && e.version === "4" || e.type);
  }
  return {
    datetime: {
      form: {
        component: {
          //el-date-picker,a-date-picker
          ...e.datePicker.buildDateType("datetime"),
          vModel: e.datePicker.modelValue
        }
      },
      column: {
        width: 170,
        component: { name: "fs-date-format" }
      },
      valueBuilder(o) {
        t(o);
      }
    },
    date: {
      form: {
        component: {
          //el-date-picker,a-date-picker
          ...e.datePicker.buildDateType("date"),
          vModel: e.datePicker.modelValue
        }
      },
      column: {
        align: "center",
        width: 120,
        component: { name: "fs-date-format", format: "YYYY-MM-DD" }
      },
      valueBuilder(o) {
        t(o);
      }
    },
    daterange: {
      form: {
        component: {
          //el-date-picker,a-date-picker
          ...e.datePicker.buildDateType("daterange"),
          vModel: e.datePicker.modelValue
        }
      },
      column: { width: 210, formatter: el },
      valueBuilder({ row: o, key: r, value: a }) {
        a != null && Array.isArray(a) && a.length === 2 && a != null && (e.type === "naive" ? o[r] = [(0, import_dayjs.default)(a[0]).valueOf(), (0, import_dayjs.default)(a[1]).valueOf()] : o[r] = [(0, import_dayjs.default)(a[0]), (0, import_dayjs.default)(a[1])]);
      }
    },
    datetimerange: {
      form: {
        component: {
          ...e.datePicker.buildDateType("datetimerange"),
          vModel: e.datePicker.modelValue
        }
      },
      column: {
        width: 340,
        formatter: tl
      },
      valueBuilder({ row: o, key: r, value: a }) {
        a != null && Array.isArray(a) && a.length === 2 && (e.type === "naive" ? o[r] = [(0, import_dayjs.default)(a[0]).valueOf(), (0, import_dayjs.default)(a[1]).valueOf()] : o[r] = [(0, import_dayjs.default)(a[0]), (0, import_dayjs.default)(a[1])]);
      }
    },
    time: {
      form: {
        component: {
          //el-time-picker,a-time-picker
          name: e.timePicker.name,
          vModel: e.timePicker.modelValue
        }
      },
      column: {
        width: 100,
        align: "center",
        component: { name: "fs-date-format", format: "HH:mm:ss" }
      },
      valueBuilder(o) {
        t(o);
      }
    },
    month: {
      form: {
        component: {
          //el-date-picker,a-date-picker
          ...e.datePicker.buildDateType("month"),
          vModel: e.datePicker.modelValue
        }
      },
      column: {
        align: "center",
        width: 120,
        component: { name: "fs-date-format", format: "YYYY-MM" }
      },
      valueBuilder(o) {
        t(o);
      }
    },
    week: {
      form: {
        component: {
          //el-date-picker,a-date-picker
          ...e.datePicker.buildDateType("week"),
          vModel: e.datePicker.modelValue
        }
      },
      column: {
        align: "center",
        width: 120,
        component: { name: "fs-date-format", format: "YYYY-ww[周]" }
      },
      valueBuilder(o) {
        t(o);
      }
    },
    quarter: {
      form: {
        component: {
          //el-date-picker,a-date-picker
          ...e.datePicker.buildDateType("quarter"),
          vModel: e.datePicker.modelValue
        }
      },
      column: {
        align: "center",
        width: 120,
        component: { name: "fs-date-format", format: "YYYY-[Q]Q" }
      },
      valueBuilder(o) {
        t(o);
      }
    },
    year: {
      form: {
        component: {
          //el-date-picker,a-date-picker
          ...e.datePicker.buildDateType("year"),
          vModel: e.datePicker.modelValue
        }
      },
      column: {
        align: "center",
        width: 120,
        component: { name: "fs-date-format", format: "YYYY" }
      },
      valueBuilder(o) {
        t(o);
      }
    }
  };
}
var ul = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: ll
}, Symbol.toStringTag, { value: "Module" }));
function cl() {
  const { ui: e } = B();
  return {
    number: {
      form: { component: { name: e.number.name, props: {} } },
      align: "center"
    },
    switch: {
      form: { component: { name: e.switch.name, props: {} } },
      component: { name: e.switch.name, props: {} },
      align: "center"
    },
    slider: {
      form: { component: { name: "el-slider", props: {} } },
      align: "center"
    },
    rate: {
      form: { component: { name: "el-rate", props: {} } },
      align: "center"
    },
    "color-picker": {
      form: { component: { name: "el-color-picker", props: {} } },
      align: "center"
    },
    transfer: {
      form: { component: { name: "el-transfer", props: {} } },
      align: "center"
    },
    autocomplete: {
      form: { component: { name: "el-autocomplete", props: {} } }
    }
  };
}
var dl = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: cl
}, Symbol.toStringTag, { value: "Module" }));
function fl() {
  return {
    icon: {
      form: {
        component: {
          name: "fs-icon-selector",
          vModel: "modelValue",
          [i.get().input.clearable]: true
        }
      },
      column: {
        component: {
          name: "fs-icon",
          vModel: "icon",
          style: "font-size:18px"
        }
      }
    }
  };
}
var hl = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: fl
}, Symbol.toStringTag, { value: "Module" }));
function ml() {
  const e = i.get();
  return {
    number: {
      form: {
        component: {
          // el-input-number,a-input-number
          name: e.number.name,
          vModel: e.modelValue
        }
      }
    }
  };
}
var pl = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: ml
}, Symbol.toStringTag, { value: "Module" }));
function gl() {
  return {
    "phone-number": {
      form: {
        component: {
          name: "el-phone-number-input"
        }
      },
      column: {
        formatter(e, t, n) {
          let o = "";
          return n != null && (n.callingCode != null ? o += "(+" + n.callingCode + ")" : n.countryCode != null && (o += "(" + n.countryCode + ")"), n.phoneNumber != null && (o += n.phoneNumber)), o;
        }
      }
    }
  };
}
var vl = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: gl
}, Symbol.toStringTag, { value: "Module" }));
function bl() {
  const e = i.get();
  return {
    select: {
      search: { autoSearchTrigger: "change" },
      form: {
        component: {
          name: e.select.name,
          [e.select.clearable]: true
        }
      }
    },
    "dict-select": {
      search: { autoSearchTrigger: "change" },
      column: { component: { name: "fs-values-format", vModel: "modelValue" } },
      form: {
        component: {
          name: "fs-dict-select",
          vModel: e.select.modelValue,
          [e.select.clearable]: true
        }
      }
    },
    "table-select": {
      column: { component: { name: "fs-values-format", vModel: "modelValue" } },
      form: {
        component: {
          name: "fs-table-select"
        }
      }
    },
    "dict-radio": {
      search: {
        component: {
          name: "fs-dict-select",
          vModel: e.select.modelValue,
          autoSearchTrigger: "change"
        }
      },
      form: {
        component: {
          name: "fs-dict-radio",
          vModel: e.radioGroup.modelValue,
          [e.select.clearable]: true
        }
      },
      column: { component: { name: "fs-values-format", vModel: "modelValue" } }
    },
    "dict-checkbox": {
      search: {
        component: { name: "fs-dict-select" },
        autoSearchTrigger: "change"
      },
      form: {
        component: {
          name: "fs-dict-checkbox",
          vModel: e.radioGroup.modelValue,
          [e.select.clearable]: true
        }
      },
      column: { component: { name: "fs-values-format", vModel: "modelValue" } }
    },
    "dict-switch": {
      search: {
        component: { name: "fs-dict-select", vModel: e.select.modelValue },
        autoSearchTrigger: "change"
      },
      form: {
        component: {
          name: "fs-dict-switch",
          vModel: e.switch.modelValue,
          [e.select.clearable]: true
        }
      },
      column: { component: { name: "fs-values-format", vModel: "modelValue" } }
    }
  };
}
var yl = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: bl
}, Symbol.toStringTag, { value: "Module" }));
function wl() {
  const e = i.get();
  return {
    text: {
      form: {
        component: {
          // el-input, a-input
          name: e.input.name,
          vModel: e.textArea.modelValue,
          [e.input.clearable]: true
        }
      },
      search: {
        autoSearchTrigger: "enter"
      }
    },
    password: {
      form: {
        component: {
          // el-input / a-input-password
          name: e.inputPassword.name,
          vModel: e.inputPassword.modelValue,
          ...e.inputPassword.passwordType
        }
      },
      search: {
        autoSearchTrigger: "enter"
      }
    },
    textarea: {
      search: {
        component: {
          // el-input / a-input
          name: e.input.name,
          type: "text",
          [e.input.clearable]: true
        },
        autoSearchTrigger: "enter"
      },
      form: {
        component: {
          // el-input / a-textarea
          name: e.textArea.name,
          type: e.textArea.type,
          vModel: e.textArea.modelValue,
          [e.input.clearable]: true
        }
      }
    }
  };
}
var _l = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: wl
}, Symbol.toStringTag, { value: "Module" }));
function Cl() {
  const e = i.get();
  return {
    "dict-tree": {
      search: { autoSearchTrigger: "change" },
      column: { component: { name: "fs-values-format", vModel: "modelValue" } },
      form: {
        component: {
          name: "fs-dict-tree",
          vModel: e.treeSelect.modelValue,
          [e.treeSelect.clearable]: true
        }
      }
    }
  };
}
var Sl = Object.freeze(Object.defineProperty({
  __proto__: null,
  default: Cl
}, Symbol.toStringTag, { value: "Module" }));
var Fl = Object.assign({ "./list/assist.ts": zs, "./list/button.ts": Us, "./list/cascader.ts": Ys, "./list/date.ts": ul, "./list/el.ts": dl, "./list/icon.ts": hl, "./list/number.ts": pl, "./list/phone.ts": vl, "./list/select.ts": yl, "./list/text.ts": _l, "./list/tree.ts": Sl });
var Ka = [];
lodash_default_default.forEach(Fl, (e) => {
  Ka.push(e.default);
});
var eo = {};
function Rl() {
  return eo;
}
function Dl(e) {
  return eo[e];
}
function kl(e) {
  for (const t in e)
    eo[t] = e[t];
}
var Rn = {
  getType: Dl,
  addTypes: kl,
  getTypes: Rl,
  install() {
    for (const e of Ka)
      lodash_default_default.forEach(e(), (t, n) => {
        eo[n] = t;
      });
    ue.debug("types installed:", eo);
  }
};
var $l = defineComponent({
  name: "FsPage",
  setup() {
    const e = ref();
    return onMounted(() => {
      window.getComputedStyle(e.value.parentNode).getPropertyValue("position") !== "relative" && vt.logger.warn(
        "fs-page父节点的position建议为relative,因为fs-page为相对定位（position:absolute），如果样式没有异常，你可以忽略此警告"
      );
    }), {
      pageRef: e
    };
  }
});
var ke = (e, t) => {
  const n = e.__vccOpts || e;
  for (const [o, r] of t)
    n[o] = r;
  return n;
};
var Ol = {
  ref: "pageRef",
  class: "fs-page"
};
var El = {
  key: 0,
  class: "fs-page-header"
};
var Tl = { class: "fs-page-content" };
var Al = {
  key: 1,
  class: "fs-page-footer"
};
function Il(e, t, n, o, r, a) {
  return openBlock(), createElementBlock("div", Ol, [
    e.$slots.header != null ? (openBlock(), createElementBlock("div", El, [
      renderSlot(e.$slots, "header")
    ])) : createCommentVNode("", true),
    createBaseVNode("div", Tl, [
      renderSlot(e.$slots, "default")
    ]),
    e.$slots.footer != null ? (openBlock(), createElementBlock("div", Al, [
      renderSlot(e.$slots, "footer")
    ])) : createCommentVNode("", true)
  ], 512);
}
var Vl = ke($l, [["render", Il]]);
function vr(e) {
  return e != null && (e instanceof Ya || isRef(e));
}
function oa(e) {
  return vr(e) && !e.cloneable;
}
function Pl(e, ...t) {
  function n(r, a) {
    if (a != null && (isArray_default(r) || vr(a)))
      return a;
  }
  let o = null;
  for (const r of t)
    vr(r) && (o = r);
  return o || mergeWith_default(e, ...t, n);
}
function Ml(e) {
  if (oa(e))
    return e;
  function t(n) {
    if (oa(n))
      return n;
  }
  return cloneDeepWith_default(e, t);
}
var Ya = class {
  constructor() {
    se(this, "cloneable", false);
  }
  setCloneable(t) {
    this.cloneable = t;
  }
};
function De() {
  return {
    merge: Pl,
    cloneDeep: Ml,
    UnMergeable: Ya
  };
}
var { cloneDeep: Bl } = De();
function jl(e) {
  return e instanceof xr;
}
function Nl(e) {
  return e instanceof Go;
}
function ra(e, t, n) {
  const o = {};
  if (e == null)
    return o;
  const r = n ? jl : Nl;
  return La.forEachDeep(e, (a, i2, s, u) => {
    if (a == null)
      return false;
    if (r(a)) {
      const l = u.path;
      if (t) {
        for (const c of t)
          if (typeof c == "string") {
            if (l.startsWith(c))
              return false;
          } else if (c instanceof RegExp && c.test(l))
            return true;
      }
      return o[l] = a, false;
    }
    return !isShallow(a);
  }, {
    // https://deepdash.io/#eachdeep-foreachdeep
    checkCircular: true
  }), o;
}
function Ll(e, t) {
  if (e == null || Object.keys(e).length <= 0)
    return null;
  const n = {};
  return forEach_default(e, (o, r) => {
    n[r] = o.buildAsyncRef(t);
  }), n;
}
function ql(e, t) {
  t == null || Object.keys(t).length <= 0 || forEach_default(t, (n, o) => {
    set_default(e, o, n.value == null ? null : n.value);
  });
}
function xl(e, t, n, o) {
  const r = computed(() => {
    const s = e();
    return ra(s, n, false);
  }), a = computed(() => {
    const s = e();
    return ra(s, n, true);
  }), i2 = Ll(a.value, t);
  return computed(() => {
    let s = e();
    const u = Object.keys(a.value).length, l = Object.keys(r.value).length;
    if (u > 0 || l > 0) {
      if (s = Bl(s), l > 0) {
        const c = t ? t() : {};
        forEach_default(r.value, (d, f) => {
          set_default(s, f, d.computeFn(c));
        });
      }
      u > 0 && ql(s, i2);
    }
    return o ? o(s) : s;
  });
}
var Go = class {
  constructor(t) {
    se(this, "computeFn");
    this.computeFn = t;
  }
};
function Hl(e) {
  return new Go(e);
}
var xr = class {
  constructor(t) {
    se(this, "watch");
    se(this, "asyncFn");
    se(this, "defaultValue");
    const { asyncFn: n, defaultValue: o } = t;
    this.watch = t.watch, this.asyncFn = n, this.defaultValue = o;
  }
  buildAsyncRef(t) {
    t = t || function() {
    };
    const n = ref(this.defaultValue), o = computed(() => this.watch ? this.watch(t()) : null);
    return watch(() => o.value, async (r) => {
      n.value = await this.asyncFn(r, t());
    }, { immediate: true }), n;
  }
};
function zl(e) {
  return new xr(e);
}
function Nt() {
  return {
    ComputeValue: Go,
    compute: Hl,
    AsyncComputeValue: xr,
    asyncCompute: zl,
    doComputed: xl
  };
}
var nn = {
  orderDefault: 1
};
var {
  merge: Ot,
  cloneDeep: xt
} = De();
var Nn = [];
function Xo(e) {
  remove_default(Nn, (t) => t.name === e.name), Nn.push(e), Nn.sort((t, n) => t.order - n.order), ue.debug("mergeColumnPlugin register success: current:", e, "registered:", Nn);
}
function Wl(e) {
  var t, n;
  if (e.dict) {
    if ((t = e.column) != null && t.component) {
      const o = xt(e.dict);
      e.column.component.dict = Ot(o, e.column.component.dict);
    }
    if ((n = e.form) != null && n.component) {
      const o = xt(e.dict);
      e.form.component.dict = Ot(o, e.form.component.dict);
    }
  }
  return e;
}
function Ul(e) {
  if (!e.type)
    return e;
  let t = [];
  typeof e.type == "string" ? t = [e.type] : e.type instanceof Array && (t = e.type);
  const n = {};
  for (const o of t) {
    const r = Rn.getType(o);
    r && Ot(n, r);
  }
  return e = Ot(n, e), e;
}
var Kl = {
  name: "type",
  handle: Ul,
  order: -2
};
var Yl = {
  name: "dict",
  handle: Wl,
  order: -1
};
var Gl = {
  name: "viewFormUseCellComponent",
  order: 10,
  handle: (e = {}, t = {}) => {
    var r, a;
    if (!((r = t.settings) != null && r.viewFormUseCellComponent))
      return e;
    const o = {
      component: ((a = e.column) == null ? void 0 : a.component) || {}
    };
    return (e.type === "text" || e.type instanceof Array && e.type.includes("text")) && (o.render = (i2) => {
      const {
        value: s
      } = i2;
      return createVNode("span", null, [s]);
    }), Ot(e, {
      viewForm: o
    }), e;
  }
};
Xo(Kl);
Xo(Yl);
Xo(Gl);
function Hr(e, t) {
  const n = {};
  return forEach_default(e, (o, r) => {
    if (o.key = r, o.children)
      o.children = Hr(o.children, t);
    else
      for (const a of Nn)
        o = a.handle(o, t);
    n[r] = o;
  }), n;
}
function zr(e = {}, t) {
  return forEach_default(t, (n, o) => {
    n.children ? zr(e, n.children) : e[o] = n;
  }), e;
}
function Wr(e = {}, t) {
  return forEach_default(t, (n, o) => {
    n.children ? Wr(e, n.children) : e[o] = n;
  }), e;
}
function Xl(e) {
  const t = e, n = t.column || {};
  return n.title == null && (n.title = t.title), n.key = t.key, t.children && (n.children = Xa(t.children)), Ga(n), reactive(n);
}
function Ga(e) {
  var t;
  e && (t = e.component) != null && t.name && typeof e.component.name != "string" && (isRef(e.component.name) || (e.component.name = shallowRef(e.component.name)));
}
function Xa(e) {
  let t = {};
  return forEach_default(e, (n, o) => {
    t[o] = Xl(n);
  }), t = Qa(t), t;
}
function Ql(e) {
  return sortBy_default(e, (t) => t.order ?? nn.orderDefault);
}
function Qa(e) {
  const t = [];
  for (const r in e) {
    const a = e[r];
    a.key = r, a.children && size_default(a.children) > 0 && (a.children = Qa(a.children)), t.push(a);
  }
  const n = Ql(t), o = {};
  for (const r of n)
    o[r.key] = r;
  return o;
}
function Ja(e, t) {
  const n = {};
  return forEach_default(e, (o) => {
    const r = xt(o[t]) || {};
    t === "form" && r.title == null && (r.title = o.title), r.key = o.key, n[o.key] = r, Ga(r);
  }), n;
}
function Ln(e, t, n, o) {
  const r = Ja(n, t), a = Ot(xt(e.form), e[t], {
    columns: r
  });
  return o && o(a), a;
}
function Jl(e, t = "search", n) {
  var s;
  const o = Ja(n, t), r = {}, a = ((s = e.settings) == null ? void 0 : s.searchCopyFormProps) ?? ["component", "valueChange", "title", "key", "label", "render"];
  function i2(u, l, c) {
    if (includes_default(a, c) && e.columns[l]) {
      const f = e.columns[l][c];
      f && (u[c] = f);
    }
  }
  return forEach_default(xt(e.form.columns), (u, l) => {
    const c = {};
    i2(c, l, "valueResolve"), i2(c, l, "valueBuilder"), r[l] = Ot(c, pick_default(u, a));
  }), Ot({
    columns: r
  }, {
    columns: o
  }, e.search);
}
function Zl(e, t) {
  const {
    t: n
  } = ot(), {
    merge: o
  } = De();
  t = t || {};
  const r = o(Zn.defaultOptions({
    t: n
  }), Zn.commonOptions({
    crudOptions: e,
    context: t,
    crudExpose: null
  }), e), a = Hr(xt(r.columns), r), i2 = zr({}, a);
  return Ln(r, "form", i2);
}
function eu(e) {
  forEach_default(e.columns, (o, r) => {
    o.key = r;
  });
  const t = Hr(xt(e.columns), e);
  e.columns = t;
  const n = zr({}, t);
  return e.table.columns = Xa(xt(t)), e.table.columnsMap = Wr({}, e.table.columns), Ot(e.toolbar, {
    columnsFilter: {
      originalColumns: xt(e.table.columns)
    }
  }), e.form = Ln(e, "form", n), e.addForm = Ln(e, "addForm", n), e.editForm = Ln(e, "editForm", n), e.viewForm = Ln(e, "viewForm", n, (o) => {
    forEach_default(o.columns, (r) => {
      r.component || (r.component = {}), r.component.disabled = true;
    });
  }), e.search = Jl(e, "search", n), e.table.editable && (e.table.editable.addForm = Ot(e.addForm.columns, e.table.editable.addForm), e.table.editable.editForm = Ot(e.editForm.columns, e.table.editable.editForm)), e;
}
function br(e, t) {
  forEach_default(e, (n, o) => {
    n.key || (n.key = o), n.children ? br(n.children, t) : t(n, o);
  });
}
function Po(e, t) {
  forEach_default(e, (n, o) => {
    n.key || (n.key = o), n.children ? Po(n.children, t) : t(n, o);
  });
}
function Za() {
  return {
    buildFormOptions: Zl,
    buildColumns: eu,
    registerMergeColumnPlugin: Xo,
    forEachColumns: Po
  };
}
var tu = 0;
var tr = {};
async function nu(e) {
  const t = e.id || `${tu++}`, n = document.createElement("div");
  return new Promise((o, r) => {
    let a = tr[t];
    a != null && (a.vm.exposed.open(e), o(a));
    const i2 = createVNode(to, {
      id: t,
      onClosed() {
        e.id || delete tr[t];
      }
    });
    i2.appContext = to._context, render(i2, n), document.body.appendChild(n);
    const u = i2.component;
    a = {
      id: t,
      vNode: i2,
      vm: u,
      props: i2.component.props
    }, tr[t] = a, a.vm.exposed.open(e), o(a.vm.exposed);
  });
}
function ou() {
  let e = null;
  try {
    e = inject("use:form:wrapper", () => {
    })();
  } catch (o) {
    ue.warn("cant inject use:form:wrapper，建议在App.vue中使用<fs-form-provider>组件包裹<router-view/>", o);
  }
  let t = null;
  e == null ? t = async (o) => await nu(o) : t = async (o) => await e.open(o);
  async function n(o) {
    const { buildFormOptions: r } = Za(), a = r(o.crudOptions, o.context);
    return await t(a);
  }
  return {
    openDialog: t,
    openCrudFormDialog: n
  };
}
var { merge: ei } = De();
var ru = ei;
function au(e) {
  const { crudExpose: t } = e, { crudBinding: n } = t;
  B(), ot();
  const { merge: o } = De();
  watch(() => {
    var a, i2, s;
    return (s = (i2 = (a = n.value) == null ? void 0 : a.table) == null ? void 0 : i2.editable) == null ? void 0 : s.enabled;
  }, (a) => {
    a ? n.value.table.editable.mode === "row" ? n.value.rowHandle.active = "editRow" : n.value.rowHandle.active = "editable" : n.value.rowHandle.active = "default";
  });
  const r = {
    /**
     * 启用编辑
     * @param opts
     */
    async enable(a, i2) {
      const s = n.value.table.editable;
      o(s, { enabled: true }, a), i2 && i2({ editable: s });
    },
    /**
     * 禁用编辑
     */
    disable() {
      var a;
      (a = t.getTableRef()) == null || a.editable.resume(), n.value.table.editable.enabled = false, n.value.rowHandle.active = "default";
    },
    /**
     * 激活所有编辑
     */
    active(a) {
      t.getTableRef().editable.active(a);
    },
    /**
     * 退出编辑
     */
    inactive() {
      t.getTableRef().editable.inactive();
    },
    /**
     * 添加行
     */
    addRow(a) {
      t.getTableRef().editable.addRow(a);
    },
    activeCols(a) {
      t.getTableRef().editable.activeCols(a);
    },
    /**
     * 还原，取消编辑
     */
    resume() {
      t.getTableRef().editable.resume();
    },
    /**
     * 还原，取消编辑,同resume
     */
    cancel() {
      t.getTableRef().editable.cancelAll();
    },
    /**
     * 本地保存，不提交到后台
     */
    persist() {
      t.getTableRef().editable.persist();
    },
    removeRow(a) {
      t.getTableRef().editable.removeRow(a);
    },
    getEditableRow(a) {
      var i2, s;
      return (s = (i2 = t.getTableRef()) == null ? void 0 : i2.editable) == null ? void 0 : s.getEditableRow(a);
    },
    getActiveRows() {
      var a, i2;
      return (i2 = (a = t.getTableRef()) == null ? void 0 : a.editable) == null ? void 0 : i2.getActiveRows();
    },
    async doSaveRow(a) {
      let i2 = a.editableId;
      i2 || (i2 = a.row[n.value.table.editable.rowKey]);
      const s = r.getEditableRow(i2);
      await s.save({
        async doSave(u) {
          var h2, v;
          const { isAdd: l, row: c, setData: d } = u, f = c;
          if (((v = (h2 = n.value) == null ? void 0 : h2.mode) == null ? void 0 : v.name) !== "local")
            try {
              if (s.loading = true, l) {
                const g = await n.value.request.addRequest({ form: f });
                d(g);
              } else
                await n.value.request.editRequest({ form: f, row: f });
            } finally {
              s.loading = false;
            }
        }
      });
    },
    async doCancelRow(a) {
      let i2 = a.editableId;
      i2 || (i2 = a.row[n.value.table.editable.rowKey]);
      const s = r.getEditableRow(i2);
      if (s.isAdd) {
        r.removeRow(i2);
        return;
      }
      s.cancel();
    },
    async doRemoveRow(a) {
      let i2 = a.editableId;
      i2 || (i2 = a.row[n.value.table.editable.rowKey]);
      const s = r.getEditableRow(i2);
      return await t.doRemove(a, {
        async handle() {
          return s.isAdd ? (r.removeRow(i2), false) : n.value.mode.name === "local" ? (r.removeRow(i2), { isLocal: true }) : await n.value.request.delRequest(a);
        }
      });
    },
    getInstance() {
      t.getTableRef().editable;
    },
    eachCells(a) {
      var i2;
      (i2 = t.getTableRef().editable) == null || i2.eachCells(a);
    },
    eachRows(a) {
      var i2;
      (i2 = t.getTableRef().editable) == null || i2.eachRows(a);
    },
    async validate() {
      var a;
      return await ((a = t.getTableRef().editable) == null ? void 0 : a.validate());
    },
    getTableData(a) {
      var i2;
      return (i2 = t.getTableRef().editable) == null ? void 0 : i2.getCleanTableData(a);
    },
    getCleanTableData(a) {
      var i2;
      return (i2 = t.getTableRef().editable) == null ? void 0 : i2.getCleanTableData(a);
    }
  };
  return r;
}
function ti(e) {
  const { crudRef: t, crudBinding: n } = e, { ui: o } = B(), { t: r } = ot(), a = ou();
  function i2() {
    t.value == null && ue.warn("crudRef还未初始化，请在onMounted之后调用");
  }
  function s() {
    n.value == null && ue.warn("crudBinding还未初始化，请在useFs或useCrud之后调用");
  }
  const u = {
    crudRef: t,
    crudBinding: n,
    getFormWrapperRef() {
      return t.value.formWrapperRef;
    },
    getFormRef: () => {
      const l = u.getFormWrapperRef();
      if (l == null || (l == null ? void 0 : l.formRef) == null) {
        ue.error("当前无法获取FormRef，请在编辑对话框已打开的状态下调用此方法，如果是在打开对话框时调用，可以尝试先nextTick");
        return;
      }
      return l == null ? void 0 : l.formRef;
    },
    getFormData: () => {
      const l = u.getFormRef();
      return l == null ? void 0 : l.getFormData();
    },
    setFormData: (l, c) => {
      var d;
      (d = u.getFormRef()) == null || d.setFormData(l, c);
    },
    getFormComponentRef(l, c = false) {
      const d = u.getFormRef();
      return d == null ? void 0 : d.getComponentRef(l, c);
    },
    doValueBuilder(l, c) {
      c == null && (c = toRaw(n.value.columns)), ue.debug("doValueBuilder ,columns=", c);
      const d = [];
      Po(c, (f) => {
        f.valueBuilder != null && d.push(f);
      }), d.length !== 0 && (forEach_default(l, (f, h2) => {
        forEach_default(d, (v) => {
          v.valueBuilder({
            value: f[v.key],
            row: f,
            form: f,
            index: h2,
            key: v.key,
            column: v
          });
        }), f.children && isArray_default(f.children) && u.doValueBuilder(f.children, c);
      }), ue.debug("valueBuilder success:", l));
    },
    doValueResolve({ form: l }, c) {
      c == null && (c = toRaw(n.value.columns));
      const d = [];
      Po(c, (f) => {
        f.valueResolve != null && d.push(f);
      }), d.length !== 0 && (ue.debug("doValueResolve ,columns=", c), forEach_default(d, (f) => {
        const h2 = f.key;
        f.valueResolve({
          value: l[h2],
          row: l,
          form: l,
          key: h2,
          column: f
        });
      }), ue.debug("valueResolve success:", l));
    },
    doSearchValidate() {
      u.getSearchRef().doValidate();
    },
    getSearchFormData() {
      return n.value.search.validatedForm;
    },
    getSearchValidatedFormData() {
      return n.value.search.validatedForm;
    },
    /**
     * {form,mergeForm}
     */
    setSearchFormData(l) {
      if (t.value && t.value.setSearchFormData({
        form: l.form,
        mergeForm: l.mergeForm
      }), l.mergeForm === false)
        for (const d in n.value.search.validatedForm)
          delete n.value.search.validatedForm[d];
      const { merge: c } = De();
      c(n.value.search.validatedForm, l.form), l.triggerSearch && u.doRefresh();
    },
    /**
     * 获取search组件ref
     */
    getSearchRef() {
      var l;
      return i2(), (l = t.value) == null ? void 0 : l.getSearchRef();
    },
    buildPageQuery(l) {
      var g, w;
      const c = l.page;
      let d = l.form;
      d == null && (d = cloneDeep_default(u.getSearchValidatedFormData()) || {}, (w = (g = n.value) == null ? void 0 : g.search) != null && w.columns && u.doValueResolve({ form: d }, toRaw(n.value.search.columns)));
      let f = l.sort;
      f == null && (f = n.value.table.sort || {});
      const h2 = { page: c, form: d, sort: f };
      let v = h2;
      return n.value.request.transformQuery && (v = n.value.request.transformQuery(h2)), v;
    },
    async search(l, c = {}) {
      const d = u.buildPageQuery(l);
      let f;
      const h2 = unref(n.value.table.disableLoading);
      try {
        c.silence !== true && h2 !== true && (n.value.table.loading = true), ue.debug("pageRequest", d), f = await n.value.request.pageRequest(d);
      } finally {
        n.value.table.loading = false;
      }
      if (f == null) {
        ue.warn("pageRequest返回结果不能为空");
        return;
      }
      let v = f;
      return n.value.request.transformRes && (v = n.value.request.transformRes({
        res: f,
        query: d
      })), v.records && u.doValueBuilder(v.records), v;
    },
    getPage() {
      let l = {
        currentPage: 1,
        pageSize: 10
      };
      return n.value.pagination && (l = {
        currentPage: n.value.pagination[o.pagination.currentPage],
        pageSize: n.value.pagination.pageSize
      }), l;
    },
    async doRefresh(l) {
      var w, y;
      if (n.value.request.pageRequest == null)
        return;
      ue.debug("do refresh:", l), n.value.pagination && l != null && l.goFirstPage && (n.value.pagination[o.pagination.currentPage] = 1);
      const c = u.getPage(), d = await u.search({ page: c }, { silence: l == null ? void 0 : l.silence });
      if (d == null) {
        ue.error("pageRequest返回结构不正确，请配置正确的request.transformRes，期望：{currentPage>0, pageSize>0, total, records:[]},实际返回：", d);
        return;
      }
      const { currentPage: f = c.currentPage || 1, pageSize: h2 = c.pageSize, total: v } = d, { records: g } = d;
      if (g == null || !(g instanceof Array) || v == null || f == null || f <= 0 || isNaN(f) || h2 == null || h2 <= 0 || isNaN(h2)) {
        ue.error("pageRequest返回结构不正确，请配置正确的request.transformRes，期望：{currentPage>0, pageSize>0, total, records:[]},实际返回：", d), ue.info("如果你的不需要分页，也需要按照上面的格式返回，可以让pageSize=99999，然后配置crudOptions.pagination.show=false来隐藏分页组件");
        return;
      }
      if (n.value.data = g, n.value.pagination && (n.value.pagination[o.pagination.currentPage] = f, n.value.pagination.pageSize = h2, n.value.pagination[o.pagination.total] = v || g.length), (l == null ? void 0 : l.scrollTop) ?? n.value.table.scrollTopOnRefreshed) {
        const R = u.getTableRef();
        R == null || R.scrollTo(0);
      }
      (y = (w = n.value) == null ? void 0 : w.table) != null && y.onRefreshed && n.value.table.onRefreshed({
        data: g
      });
    },
    /**
     * 获取toolbar组件Ref
     */
    getToolbarRef: () => t.value.toolbarRef,
    /**
     * 获取列设置组件Ref
     */
    getColumnsFilterRef: () => u.getToolbarRef().columnsFilterRef,
    /**
     * 获取列设置的原始列配置Ref
     * 可以修改列设置的原始配置
     */
    getColumnsFilterOriginalColumnsRef: () => u.getColumnsFilterRef().original,
    /**
     * 获取列设置的列配置Ref
     * 可以动态修改列设置每列的配置
     */
    getColumnsFilterColumnsRef: () => u.getColumnsFilterRef().columns,
    doPageTurn(l) {
      n.value.pagination[o.pagination.currentPage] = l;
    },
    /**
     *
     * @param opts = {
     *   form
     *   goFirstPage =true
     *   mergeForm=false
     * }
     */
    async doSearch(l) {
      ue.debug("do search:", l), l = ei({ goFirstPage: true }, l), l.goFirstPage && u.doPageTurn(1), l.form && t.value && u.setSearchFormData({
        form: l.form,
        mergeForm: l.mergeForm,
        refWarning: false,
        triggerSearch: false
      }), await u.doRefresh();
    },
    /**
     * 获取FsTable实例
     */
    getTableRef() {
      var l;
      return i2(), (l = t.value) == null ? void 0 : l.tableRef;
    },
    /**
     * 获取x-Table实例
     */
    getBaseTableRef() {
      const l = this.getTableRef();
      if (l == null) {
        ue.warn("fs-table还未挂载");
        return;
      }
      return l.tableRef;
    },
    /**
     * 获取表格数据
     */
    getTableData() {
      return s(), n.value.data;
    },
    setTableData(l) {
      s(), n.value.data = l;
    },
    insertTableRow(l, c) {
      s(), n.value.data.splice(l, 0, c);
    },
    updateTableRow(l, c, d = true) {
      d ? n.value.data[l] = ru(n.value.data[l], c) : n.value.data[l] = c;
    },
    removeTableRow(l) {
      s(), n.value.data.splice(l, 1);
    },
    removeTableRowByRowKey: (l, c) => {
      s(), c == null && (c = n.value.data);
      for (let d = 0; d < c.length; d++) {
        const f = c[d];
        if (f[n.value.table.rowKey] === l)
          return c.splice(d, 1), true;
        if (f.children && isArray_default(f.children) && u.removeTableRowByRowKey(l, f.children))
          return true;
      }
    },
    getTableDataRow(l) {
      const c = u.getTableData();
      if (c == null)
        throw new Error("table data is not init");
      if (c.length <= l)
        throw new Error("index over array length");
      return c[l];
    },
    /**
     * 选择某一行
     * @param index
     * @param row
     */
    doSelectCurrentRow({ row: l }) {
      u.getTableRef().value.setCurrentRow(l);
    },
    /**
     * 删除行按钮
     * @param context
     * @param opts
     */
    async doRemove(l, c) {
      var g;
      const d = n.value.table.remove ?? c ?? {};
      if ((c == null ? void 0 : c.noConfirm) !== true)
        try {
          d.confirmFn ? await d.confirmFn(l) : await o.messageBox.confirm({
            title: d.confirmTitle || r("fs.rowHandle.remove.confirmTitle"),
            message: d.confirmMessage || r("fs.rowHandle.remove.confirmMessage"),
            type: "warn",
            ...d.confirmProps
          });
        } catch {
          d.onCanceled && await d.onCanceled(l);
          return;
        }
      let f = null;
      const h2 = ((g = n.value.mode) == null ? void 0 : g.name) === "local";
      if (c != null && c.handle ? f = await c.handle(l) : h2 ? u.removeTableRow(l == null ? void 0 : l.index) : f = await n.value.request.delRequest(l), f === false)
        return;
      const v = { ...l, res: f };
      if (d.afterRemove && await d.afterRemove(v) === false)
        return false;
      d.showSuccessNotification !== false && o.notification.success(r("fs.rowHandle.remove.success")), h2 || d.refreshTable !== false && await u.doRefresh({ scrollTop: false }), d.onRemoved && await d.onRemoved({ ...l, res: f });
    },
    /**
     *
     * 打开表单对话框
     * @param formOpts ={mode, initialForm: row, index,...formOptions}
     */
    async openDialog(l) {
      if (l.newInstance === true && a)
        return await a.openDialog(l);
      const c = this.getFormWrapperRef();
      return c.open(l), c;
    },
    async _openDialog(l, c, d) {
      var w, y;
      const { merge: f } = De();
      let h2 = c.row || c[o.tableColumn.row];
      delete c.row, h2 == null && c.index != null && (h2 = u.getTableDataRow(c.index)), (y = (w = n.value) == null ? void 0 : w.request) != null && y.infoRequest && (h2 = await n.value.request.infoRequest({ mode: l, row: h2 }));
      const v = {
        mode: l
      }, g = toRaw(n.value[l + "Form"]);
      return f(v, g, { initialForm: h2 }, c, d), await this.openDialog(v);
    },
    async openAdd(l, c = {}) {
      return this._openDialog("add", l, c);
    },
    async openEdit(l, c = {}) {
      return this._openDialog("edit", l, c);
    },
    async openView(l, c = {}) {
      return this._openDialog("view", l, c);
    },
    async openCopy(l, c = {}) {
      return this._openDialog("add", l, c);
    },
    editable: void 0
  };
  return u.editable = au({ crudExpose: u }), { expose: u, crudExpose: u };
}
async function iu() {
  const e = await Object.assign({ "./lib/index.ts": () => import("./index-20b7e8ae-O666NKGB.js") });
  let t = null;
  return forEach_default(e, (o) => {
    t = o;
  }), (await t()).exportUtil;
}
async function su() {
  const e = await Object.assign({ "./lib/index.ts": () => import("./index-20b7e8ae-O666NKGB.js") });
  let t = null;
  return forEach_default(e, (o) => {
    t = o;
  }), (await t()).importUtil;
}
function lu({ originalRow: e, row: t, key: n, col: o }) {
  var i2;
  const r = e[n], a = (i2 = o.component) == null ? void 0 : i2.dict;
  if (a && r != null) {
    const s = a.getNodesFromDataMap(r);
    if (s != null && s.length > 0) {
      const u = map_default(s, (l) => a.getLabel(l) || a.getValue(l)).join("|");
      u != null && u !== "" && (t[n] = u);
    }
  }
  return t;
}
async function uu(e, t = {}) {
  if (t.server) {
    const l = e.getPage(), c = e.buildPageQuery({ page: l });
    await t.server(c);
    return;
  }
  const n = e.crudBinding;
  let o = t.columns;
  o == null && (o = [], forEach_default(n.value.table.columnsMap, (l) => {
    if (!(t.columnFilter && t.columnFilter(l) === false) && !(t.onlyShow && unref(l.show) === false) && l.exportable !== false && l.key !== "_index") {
      const c = {
        key: l.key,
        title: l.title
      };
      o.push(c);
    }
  }));
  for (const l of o) {
    const c = n.value.table.columnsMap[l.key];
    l.columnProps = c || {}, t.columnBuilder && t.columnBuilder({ col: l });
  }
  const { merge: r } = De(), a = await iu(), i2 = [];
  let s = n.value.data;
  if (t.dataFrom === "search") {
    const l = r({
      page: {
        currentPage: 1,
        pageSize: 99999999
      }
    }, n.value.toolbar.export.searchParams);
    s = (await e.search(l, { silence: true })).records;
  }
  for (const l of s) {
    const c = cloneDeep_default(l);
    forEach_default(o, (d) => {
      const f = d.columnProps, h2 = {
        row: c,
        originalRow: l,
        key: d.key,
        col: f,
        exportCol: d
      };
      t.autoUseDictLabel !== false && lu(h2), t.dataFormatter && t.dataFormatter(h2);
    }), i2.push(c);
  }
  const u = r({
    columns: o,
    data: i2,
    filename: "table",
    noHeader: false,
    separator: ",",
    quoted: false
    //每项数据是否加引号
  }, {
    ...t
  });
  t.fileType === "excel" ? await a.excel(u) : await a.csv(u);
}
async function ep(e, t) {
  const o = await (await su()).csv(t.file), r = e.crudBinding;
  t.append === false && (r.value.data.length = 0);
  const a = r.value.table.editable.enabled;
  for (const i2 of o.data)
    a ? e.editable.addRow({ row: i2, active: false }) : r.value.data.push(i2);
}
var ni = {};
function oi(e, t, n = {}) {
  ni[e] = {
    handle: t,
    opts: n
  };
}
function cu(e) {
  return ni[e];
}
oi("rowSelection", (e, t, n) => {
  const r = t.crudExpose.crudBinding;
  function a() {
    return r.value.table.rowKey || "id";
  }
  const { ui: i2 } = B();
  if (!e) {
    ue.warn("请配置settings.plugins.rowSelection.props参数");
    return;
  }
  let s = i2.table;
  return (n == null ? void 0 : n.table.tableVersion) == "v2" && (s = i2.tableV2), s.buildSelectionCrudOptions({
    crossPage: e.crossPage,
    getRowKey: a,
    getPageData() {
      return r.value.data;
    },
    useCompute: () => Nt(),
    selectionFixed: e.selectionFixed,
    multiple: e.multiple,
    selectedRowKeys: e.selectedRowKeys,
    onSelectedKeysChanged: async (u) => {
      const l = e.selectedRowKeys instanceof Function ? e.selectedRowKeys() : e.selectedRowKeys;
      l.value = [...u], await nextTick(), e.onSelectedChanged && e.onSelectedChanged(l.value);
    }
  });
}, {
  before: true,
  order: -2
});
oi("mobile", (e, t, n) => {
  const o = n.rowHandle, r = o.buttons;
  let a = {};
  for (const s in r) {
    const u = r[s];
    isRef(u.dropdown) ? a = u : a[s] = {
      ...u,
      dropdown: computed(() => e.isMobile.value ? true : u.dropdown)
    };
  }
  let i2 = o.width;
  return (i2 == null || !isRef(i2)) && (i2 = computed(() => {
    var s;
    return e.isMobile.value ? ((s = e == null ? void 0 : e.rowHandle) == null ? void 0 : s.width) || 60 : o.width || 250;
  })), {
    rowHandle: {
      width: i2,
      buttons: a
    }
  };
}, {
  before: false,
  order: -2
});
var { merge: Lt } = De();
function du(e) {
  e.context == null && (e.context = {});
  const t = i.get(), { t: n } = ot(), o = (D) => computed(() => n(D));
  let r = e.crudOptions;
  const a = e.expose || e.crudExpose;
  if (!a)
    throw new Error("crudExpose不能为空，请给useCrud传入{crudExpose}参数");
  const i2 = a, { crudBinding: s } = i2, { doRefresh: u, doValueResolve: l } = i2;
  function c() {
    return {
      pagination: {
        ...t.pagination.onChange({
          setCurrentPage(x) {
            s.value.pagination[t.pagination.currentPage] = x;
          },
          setPageSize(x) {
            s.value.pagination.pageSize = x, s.value.pagination[t.pagination.currentPage] = 1;
          },
          async doAfterChange() {
            return await u();
          }
        })
      }
    };
  }
  function d() {
    return {
      form: {
        async doSubmit(D) {
          var x, I, V, O;
          if (D.mode === "edit")
            if (l(D), ((x = r.mode) == null ? void 0 : x.name) === "local")
              i2.updateTableRow(D.index, D.form, r.mode.isMergeWhenUpdate);
            else
              return (I = s.value.request) != null && I.editRequest || ue.warn("request.editRequest 未定义，无法保存"), await s.value.request.editRequest(D);
          else if (D.mode === "add")
            if (l(D), ((V = r.mode) == null ? void 0 : V.name) === "local") {
              const M = r.mode.isAppendWhenAdd ? i2.getTableData().length : 0;
              i2.insertTableRow(M, D.form);
            } else
              return (O = s.value.request) != null && O.addRequest || ue.warn("request.addRequest 未定义，无法保存"), await s.value.request.addRequest(D);
        },
        async onSuccess({ mode: D }) {
          D === "edit" ? await u({ scrollTop: false }) : D === "add" ? await u({ scrollTop: true, goFirstPage: true }) : await u();
        }
      }
    };
  }
  function f() {
    return {
      rowHandle: {
        buttons: {
          remove: {
            click: async (D) => {
              D.row = D[t.tableColumn.row], await i2.doRemove(D);
            }
          },
          copy: {
            show: false,
            click: async (D) => {
              D.row = D[t.tableColumn.row], await i2.openCopy({
                row: D.row,
                index: D.index
              });
            }
          },
          edit: {
            click: async (D) => {
              D.row = D[t.tableColumn.row], await i2.openEdit({
                row: D.row,
                index: D.index
              });
            }
          },
          view: {
            click: async (D) => {
              D.row = D[t.tableColumn.row], await i2.openView({
                row: D.row,
                index: D.index
              });
            }
          }
        }
      }
    };
  }
  function h2() {
    return {
      search: {
        buttons: {
          search: {
            loading: computed(() => {
              var D, x;
              return (x = (D = s.value) == null ? void 0 : D.table) == null ? void 0 : x.loading;
            })
          }
        },
        on_reset() {
          s.value.table.sort = {}, br(s.value.table.columns, (x) => {
            x.sortOrder = false;
          });
          const D = a.getBaseTableRef();
          D != null && D.clearSort && D.clearSort();
        },
        on_search() {
          a.doRefresh({ goFirstPage: true });
        },
        "onUpdate:form": (D) => {
          s.value.search.form = D;
        },
        "onUpdate:validatedForm": (D) => {
          s.value.search.validatedForm = D;
        },
        "onUpdate:collapse": (D) => {
          s.value.search.collapse = D;
        },
        container: {
          collapse: true,
          "onUpdate:collapse": (D) => {
            s.value.search.container.collapse = D;
          }
        }
      }
    };
  }
  function v() {
    return {
      tabs: {},
      onTabChange(D) {
        a.setSearchFormData({ form: D, mergeForm: true }), u();
      }
    };
  }
  function g() {
    const D = ref(false);
    return {
      toolbar: {
        buttons: {
          refresh: {
            type: "primary",
            icon: t.icons.refresh,
            title: o("fs.toolbar.refresh.title"),
            order: 1,
            circle: true,
            click: async () => {
              await a.doRefresh({ scrollTop: false });
            }
          },
          search: {
            type: computed(() => s.value.search.show !== false ? "primary" : "default"),
            icon: t.icons.search,
            title: o("fs.toolbar.search.title"),
            order: 2,
            circle: true,
            click: () => {
              s.value.search.show = !s.value.search.show;
            }
          },
          compact: {
            type: computed(() => s.value.toolbar.compact ? "primary" : "default"),
            icon: t.icons.compact,
            title: o("fs.toolbar.compact.title"),
            order: 3,
            circle: true,
            click: () => {
              s.value.toolbar.compact = !s.value.toolbar.compact;
            }
          },
          export: {
            show: true,
            type: "primary",
            icon: t.icons.export,
            order: 4,
            loading: D,
            title: o("fs.toolbar.export.title"),
            circle: true,
            click: async () => {
              D.value = true;
              try {
                await uu(a, s.value.toolbar.export);
              } finally {
                D.value = false;
              }
            }
          },
          columns: {
            type: "primary",
            icon: t.icons.columnsFilter,
            title: o("fs.toolbar.columns.title"),
            circle: true,
            order: 5
          }
        },
        "onUpdate:columns"(x) {
          const I = s.value.table.columns;
          function V(M, X) {
            const le = {};
            return forEach_default(X, (ye) => {
              const me = M[ye.key];
              if (me) {
                delete me.order;
                const Se = Lt({ ...me }, ye);
                le[ye.key] = Se, me.children && (Se.children = V(me.children, ye.children));
              }
            }), le;
          }
          const O = V(I, x);
          s.value.table.columns = O, s.value.table.columnsMap = Wr({}, O);
        }
      }
    };
  }
  function w() {
    return {
      table: {
        onSortChange(D) {
          const { isServerSort: x, prop: I, asc: V, order: O } = D;
          br(s.value.table.columns, (X) => {
            X.key === I ? X.sortOrder = O : X.sortOrder = false;
          });
          const M = s.value.table.sort;
          s.value.table.sort = x ? { prop: I, order: O, asc: V } : null, (x || M != null) && i2.doRefresh();
        }
      }
    };
  }
  function y() {
    return {
      actionbar: {
        buttons: {
          add: {
            click() {
              i2.openAdd({});
            }
          }
        }
      }
    };
  }
  function R() {
    const { compute: D } = Nt();
    return {
      actionbar: {
        buttons: {
          addRow: {
            show: false,
            text: o("fs.actionbar.add"),
            type: "primary",
            click: () => {
              i2.editable.addRow();
            }
          }
        }
      },
      rowHandle: {
        group: {
          editable: {
            remove: {
              text: "删除",
              ...t.button.colors("danger"),
              click: async (x) => {
                const { index: I, row: V } = x, O = V[s.value.table.editable.rowKey];
                await i2.editable.doRemoveRow({ editableId: O, row: V });
              }
            }
          },
          editRow: {
            edit: {
              text: "编辑",
              loading: D((x) => {
                const { index: I, row: V } = x, O = V[s.value.table.editable.rowKey], M = i2.editable.getEditableRow(O);
                return !!(M != null && M.loading);
              }),
              click: async (x) => {
                var M;
                const { index: I, row: V } = x, O = V[s.value.table.editable.rowKey];
                if (s.value.table.editable.exclusive) {
                  const X = i2.editable.getActiveRows();
                  forEach_default(X, (le) => {
                    s.value.table.editable.exclusiveEffect === "save" ? i2.editable.doSaveRow({ row: le.rowData }) : i2.editable.doCancelRow({ row: le.rowData });
                  });
                }
                (M = i2.editable.getEditableRow(O)) == null || M.active();
              },
              show: D((x) => {
                var M, X;
                const { index: I, row: V } = x, O = V[s.value.table.editable.rowKey];
                return !((X = (M = i2.editable) == null ? void 0 : M.getEditableRow(O)) != null && X.isEditing);
              })
            },
            save: {
              text: "保存",
              loading: false,
              click: async (x) => {
                const { index: I, row: V } = x;
                await i2.editable.doSaveRow({ row: V });
              },
              show: D((x) => {
                var M, X;
                const { index: I, row: V } = x, O = V[s.value.table.editable.rowKey];
                return !!((X = (M = i2.editable) == null ? void 0 : M.getEditableRow(O)) != null && X.isEditing);
              })
            },
            cancel: {
              text: "取消",
              click: async (x) => {
                var O;
                const { index: I, row: V } = x;
                await ((O = i2.editable) == null ? void 0 : O.doCancelRow({ row: V }));
              },
              show: D((x) => {
                var M, X;
                const { index: I, row: V } = x, O = V[s.value.table.editable.rowKey];
                return !!((X = (M = i2.editable) == null ? void 0 : M.getEditableRow(O)) != null && X.isEditing);
              })
            },
            remove: {
              text: "删除",
              ...t.button.colors("danger"),
              click: async (x) => {
                var M;
                const { index: I, row: V } = x, O = V[s.value.table.editable.rowKey];
                await ((M = i2.editable) == null ? void 0 : M.doRemoveRow({ row: V, editableId: O }));
              }
            }
          }
        }
      }
    };
  }
  const { cloneDeep: F } = De();
  function k(D) {
    D.search.validatedForm = F(D.search.initialForm);
  }
  function A(D) {
    let x = F(D);
    const I = Lt(Zn.defaultOptions({ t: n }), Zn.commonOptions(e));
    r = Lt({}, I, x);
    const V = unref(r.settings);
    if (V) {
      const le = unref(V.plugins);
      forEach_default(le, (ye, me) => {
        if (ye.enabled === false)
          return;
        let Se = ye.handle, G = {};
        if (Se == null) {
          const C = cu(me);
          C != null && (Se = C.handle, G = C.opts);
        }
        if (Se == null)
          return;
        const p = ye.before ?? G.before, _ = Se(ye.props, e, r);
        p !== false ? x = Lt(_, x) : x = Lt(x, _);
      });
    }
    const O = Lt(I, c(), d(), f(), h2(), v(), g(), w(), y(), R(), x), { buildColumns: M } = Za(), X = M(O);
    return k(X), X;
  }
  function N(D) {
    s.value = A(D), ue.info("fast-crud inited, crudBinding=", s.value);
  }
  function $(D) {
    const x = Lt({}, r, D);
    return N(x), r = x, x;
  }
  N(r);
  function B2(D) {
    Lt(s.value, D);
  }
  return {
    appendCrudOptions: $,
    resetCrudOptions: N,
    appendCrudBinding: B2
  };
}
function ri(e) {
  const { createCrudOptions: t, crudExposeRef: n } = e, o = e.crudRef || ref(), r = e.crudBinding || ref({});
  let a = e.crudExpose;
  a || (a = ti({ crudRef: o, crudBinding: r }).crudExpose), n && !n.value && (n.value = a), e.context == null && (e.context = {});
  const i2 = e.context;
  e.onExpose && e.onExpose({ crudRef: o, crudBinding: r, crudExpose: a, context: i2 });
  const s = t({
    ...e,
    crudExpose: a,
    expose: a,
    context: i2
  });
  function u(l) {
    const c = { crudExpose: a, ...l, context: i2 };
    Lt(l.crudOptions, e.crudOptionsOverride);
    const d = du(c);
    return {
      ...l,
      ...d,
      crudRef: o,
      crudExpose: a,
      crudBinding: r,
      context: i2
    };
  }
  return s instanceof Promise ? s.then((l) => u(l)) : u(s);
}
function tp(e) {
  return ri(e);
}
function fu(e) {
  return ri(e);
}
function hu() {
  const e = ref(), t = ref(), n = {}, { crudExpose: o } = ti({ crudBinding: t, crudRef: e });
  return {
    crudRef: e,
    crudBinding: t,
    context: n,
    crudExpose: o
  };
}
function on(e, t, n = "modelValue") {
  const o = i.get();
  let r = e.dict;
  r && r.prototype && (r.clear(), r = shallowReactive(cloneDeep_default(e.dict)), r.clear());
  function a() {
    return computed(() => {
      let N = [];
      if (e.options ? N = e.options : r && r.data != null && (r.data instanceof Array || vt.logger.warn("dict.data类型错误，期望为数组，实际：" + r.data), N = r.data, e.transformDictData && (N = e.transformDictData(cloneDeep_default(r.data)))), o.type === "naive") {
        const $ = [];
        for (const B2 of N)
          $.push({
            ...B2,
            value: R(B2),
            label: k(B2)
          });
        return $;
      }
      return N;
    });
  }
  function i2() {
    return r;
  }
  const s = inject("get:scope", function() {
  });
  function u() {
    const N = e[n] || t.attrs[n];
    return {
      ...s(),
      componentRef: l,
      value: N
    };
  }
  const { proxy: l } = getCurrentInstance(), c = async (N = false) => {
    if (!r)
      return;
    if (r.getNodesByValues) {
      const B2 = u();
      if (B2.value == null)
        return;
      let D = B2.value;
      Array.isArray(B2.value) || (D = [B2.value]), await r.appendByValues(D);
      return;
    }
    if (r.loading)
      return;
    const $ = u();
    if (N) {
      await r.reloadDict($);
      return;
    }
    await r.loadDict($);
  };
  c();
  const d = async () => {
    await c(true);
  }, f = () => {
    r && r.clear();
  }, h2 = () => {
    r != null && (!r.prototype && !r.cloneable || watch(() => e[n], () => {
      d();
    }));
  };
  (() => {
    watch(() => r == null ? void 0 : r.data, () => {
      const N = u();
      t.attrs.onDictChange, t.emit("dict-change", { dict: r, ...N });
    }, {
      immediate: true
    });
  })();
  const g = () => {
    var N;
    return (N = i2()) == null ? void 0 : N.data;
  }, w = (N, $) => {
    let B2 = $;
    return i2() && (B2 = i2()[$]), N[B2];
  }, y = (N, $) => {
    let B2 = $;
    i2() && (B2 = i2()[$]), delete N[B2];
  }, R = (N) => w(N, "value"), F = (N) => w(N, "children"), k = (N) => {
    if (e.labelFormatter)
      return e.labelFormatter(N);
    const $ = w(N, "label");
    return $ == null ? "" : String($);
  };
  return {
    createComputedOptions: a,
    loadDict: c,
    reloadDict: d,
    clearDict: f,
    getDictData: g,
    getDict: i2,
    watchValue: h2,
    getValue: R,
    getLabel: k,
    getChildren: F,
    getColor: (N) => w(N, "color"),
    removePropValue: y,
    curDict: r
  };
}
var wn = typeof performance == "object" && performance && typeof performance.now == "function" ? performance : Date;
var ai = /* @__PURE__ */ new Set();
var yr = typeof process == "object" && process ? process : {};
var ii = (e, t, n, o) => {
  typeof yr.emitWarning == "function" ? yr.emitWarning(e, t, n, o) : console.error(`[${n}] ${t}: ${e}`);
};
var Mo = globalThis.AbortController;
var aa = globalThis.AbortSignal;
var ka;
if (typeof Mo > "u") {
  aa = class {
    constructor() {
      se(this, "onabort");
      se(this, "_onabort", []);
      se(this, "reason");
      se(this, "aborted", false);
    }
    addEventListener(o, r) {
      this._onabort.push(r);
    }
  }, Mo = class {
    constructor() {
      se(this, "signal", new aa());
      t();
    }
    abort(o) {
      var r, a;
      if (!this.signal.aborted) {
        this.signal.reason = o, this.signal.aborted = true;
        for (const i2 of this.signal._onabort)
          i2(o);
        (a = (r = this.signal).onabort) == null || a.call(r, o);
      }
    }
  };
  let e = ((ka = yr.env) == null ? void 0 : ka.LRU_CACHE_IGNORE_AC_WARNING) !== "1";
  const t = () => {
    e && (e = false, ii("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.", "NO_ABORT_CONTROLLER", "ENOTSUP", t));
  };
}
var mu = (e) => !ai.has(e);
var Ut = (e) => e && e === Math.floor(e) && e > 0 && isFinite(e);
var si = (e) => Ut(e) ? e <= Math.pow(2, 8) ? Uint8Array : e <= Math.pow(2, 16) ? Uint16Array : e <= Math.pow(2, 32) ? Uint32Array : e <= Number.MAX_SAFE_INTEGER ? So : null : null;
var So = class extends Array {
  constructor(t) {
    super(t), this.fill(0);
  }
};
var $n;
var dn = class dn2 {
  constructor(t, n) {
    se(this, "heap");
    se(this, "length");
    if (!m(dn2, $n))
      throw new TypeError("instantiate Stack using Stack.create(n)");
    this.heap = new n(t), this.length = 0;
  }
  static create(t) {
    const n = si(t);
    if (!n)
      return [];
    de(dn2, $n, true);
    const o = new dn2(t, n);
    return de(dn2, $n, false), o;
  }
  push(t) {
    this.heap[this.length++] = t;
  }
  pop() {
    return this.heap[--this.length];
  }
};
$n = /* @__PURE__ */ new WeakMap(), // private constructor
pe(dn, $n, false);
var wr = dn;
var _t;
var lt;
var Ct;
var St;
var On;
var En;
var qe;
var Ft;
var Ne;
var Te;
var ge;
var et;
var ut;
var Ge;
var We;
var Rt;
var Ue;
var Dt;
var kt;
var ct;
var $t;
var Jt;
var tt;
var oo;
var Cr;
var hn;
var qt;
var ro;
var dt;
var Ho;
var li;
var mn;
var Tn;
var ao;
var At;
var Kt;
var It;
var Yt;
var io;
var Sr;
var zm;
var An;
var Fo;
var In;
var Ro;
var $e;
var Ee;
var so;
var Fr;
var pn;
var qn;
var Vt;
var Gt;
var lo;
var Rr;
var Xr = class Xr2 {
  constructor(t) {
    pe(this, oo);
    pe(this, Ho);
    pe(this, At);
    pe(this, It);
    pe(this, io);
    pe(this, An);
    pe(this, In);
    pe(this, $e);
    pe(this, so);
    pe(this, pn);
    pe(this, Vt);
    pe(this, lo);
    pe(this, _t, void 0);
    pe(this, lt, void 0);
    pe(this, Ct, void 0);
    pe(this, St, void 0);
    pe(this, On, void 0);
    pe(this, En, void 0);
    se(this, "ttl");
    se(this, "ttlResolution");
    se(this, "ttlAutopurge");
    se(this, "updateAgeOnGet");
    se(this, "updateAgeOnHas");
    se(this, "allowStale");
    se(this, "noDisposeOnSet");
    se(this, "noUpdateTTL");
    se(this, "maxEntrySize");
    se(this, "sizeCalculation");
    se(this, "noDeleteOnFetchRejection");
    se(this, "noDeleteOnStaleGet");
    se(this, "allowStaleOnFetchAbort");
    se(this, "allowStaleOnFetchRejection");
    se(this, "ignoreFetchAbort");
    pe(this, qe, void 0);
    pe(this, Ft, void 0);
    pe(this, Ne, void 0);
    pe(this, Te, void 0);
    pe(this, ge, void 0);
    pe(this, et, void 0);
    pe(this, ut, void 0);
    pe(this, Ge, void 0);
    pe(this, We, void 0);
    pe(this, Rt, void 0);
    pe(this, Ue, void 0);
    pe(this, Dt, void 0);
    pe(this, kt, void 0);
    pe(this, ct, void 0);
    pe(this, $t, void 0);
    pe(this, Jt, void 0);
    pe(this, tt, void 0);
    pe(this, hn, () => {
    });
    pe(this, qt, () => {
    });
    pe(this, ro, () => {
    });
    pe(this, dt, () => false);
    pe(this, mn, (t2) => {
    });
    pe(this, Tn, (t2, n2, o2) => {
    });
    pe(this, ao, (t2, n2, o2, r2) => {
      if (o2 || r2)
        throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");
      return 0;
    });
    se(this, zm, "LRUCache");
    const { max: n = 0, ttl: o, ttlResolution: r = 1, ttlAutopurge: a, updateAgeOnGet: i2, updateAgeOnHas: s, allowStale: u, dispose: l, disposeAfter: c, noDisposeOnSet: d, noUpdateTTL: f, maxSize: h2 = 0, maxEntrySize: v = 0, sizeCalculation: g, fetchMethod: w, memoMethod: y, noDeleteOnFetchRejection: R, noDeleteOnStaleGet: F, allowStaleOnFetchRejection: k, allowStaleOnFetchAbort: A, ignoreFetchAbort: N } = t;
    if (n !== 0 && !Ut(n))
      throw new TypeError("max option must be a nonnegative integer");
    const $ = n ? si(n) : Array;
    if (!$)
      throw new Error("invalid max value: " + n);
    if (de(this, _t, n), de(this, lt, h2), this.maxEntrySize = v || m(this, lt), this.sizeCalculation = g, this.sizeCalculation) {
      if (!m(this, lt) && !this.maxEntrySize)
        throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");
      if (typeof this.sizeCalculation != "function")
        throw new TypeError("sizeCalculation set to non-function");
    }
    if (y !== void 0 && typeof y != "function")
      throw new TypeError("memoMethod must be a function if defined");
    if (de(this, En, y), w !== void 0 && typeof w != "function")
      throw new TypeError("fetchMethod must be a function if specified");
    if (de(this, On, w), de(this, Jt, !!w), de(this, Ne, /* @__PURE__ */ new Map()), de(this, Te, new Array(n).fill(void 0)), de(this, ge, new Array(n).fill(void 0)), de(this, et, new $(n)), de(this, ut, new $(n)), de(this, Ge, 0), de(this, We, 0), de(this, Rt, wr.create(n)), de(this, qe, 0), de(this, Ft, 0), typeof l == "function" && de(this, Ct, l), typeof c == "function" ? (de(this, St, c), de(this, Ue, [])) : (de(this, St, void 0), de(this, Ue, void 0)), de(this, $t, !!m(this, Ct)), de(this, tt, !!m(this, St)), this.noDisposeOnSet = !!d, this.noUpdateTTL = !!f, this.noDeleteOnFetchRejection = !!R, this.allowStaleOnFetchRejection = !!k, this.allowStaleOnFetchAbort = !!A, this.ignoreFetchAbort = !!N, this.maxEntrySize !== 0) {
      if (m(this, lt) !== 0 && !Ut(m(this, lt)))
        throw new TypeError("maxSize must be a positive integer if specified");
      if (!Ut(this.maxEntrySize))
        throw new TypeError("maxEntrySize must be a positive integer if specified");
      te(this, Ho, li).call(this);
    }
    if (this.allowStale = !!u, this.noDeleteOnStaleGet = !!F, this.updateAgeOnGet = !!i2, this.updateAgeOnHas = !!s, this.ttlResolution = Ut(r) || r === 0 ? r : 1, this.ttlAutopurge = !!a, this.ttl = o || 0, this.ttl) {
      if (!Ut(this.ttl))
        throw new TypeError("ttl must be a positive integer if specified");
      te(this, oo, Cr).call(this);
    }
    if (m(this, _t) === 0 && this.ttl === 0 && m(this, lt) === 0)
      throw new TypeError("At least one of max, maxSize, or ttl is required");
    if (!this.ttlAutopurge && !m(this, _t) && !m(this, lt)) {
      const B2 = "LRU_CACHE_UNBOUNDED";
      mu(B2) && (ai.add(B2), ii("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.", "UnboundedCacheWarning", B2, Xr2));
    }
  }
  /**
   * Do not call this method unless you need to inspect the
   * inner workings of the cache.  If anything returned by this
   * object is modified in any way, strange breakage may occur.
   *
   * These fields are private for a reason!
   *
   * @internal
   */
  static unsafeExposeInternals(t) {
    return {
      // properties
      starts: m(t, kt),
      ttls: m(t, ct),
      sizes: m(t, Dt),
      keyMap: m(t, Ne),
      keyList: m(t, Te),
      valList: m(t, ge),
      next: m(t, et),
      prev: m(t, ut),
      get head() {
        return m(t, Ge);
      },
      get tail() {
        return m(t, We);
      },
      free: m(t, Rt),
      // methods
      isBackgroundFetch: (n) => {
        var o;
        return te(o = t, $e, Ee).call(o, n);
      },
      backgroundFetch: (n, o, r, a) => {
        var i2;
        return te(i2 = t, In, Ro).call(i2, n, o, r, a);
      },
      moveToTail: (n) => {
        var o;
        return te(o = t, pn, qn).call(o, n);
      },
      indexes: (n) => {
        var o;
        return te(o = t, At, Kt).call(o, n);
      },
      rindexes: (n) => {
        var o;
        return te(o = t, It, Yt).call(o, n);
      },
      isStale: (n) => {
        var o;
        return m(o = t, dt).call(o, n);
      }
    };
  }
  // Protected read-only members
  /**
   * {@link LRUCache.OptionsBase.max} (read-only)
   */
  get max() {
    return m(this, _t);
  }
  /**
   * {@link LRUCache.OptionsBase.maxSize} (read-only)
   */
  get maxSize() {
    return m(this, lt);
  }
  /**
   * The total computed size of items in the cache (read-only)
   */
  get calculatedSize() {
    return m(this, Ft);
  }
  /**
   * The number of items stored in the cache (read-only)
   */
  get size() {
    return m(this, qe);
  }
  /**
   * {@link LRUCache.OptionsBase.fetchMethod} (read-only)
   */
  get fetchMethod() {
    return m(this, On);
  }
  get memoMethod() {
    return m(this, En);
  }
  /**
   * {@link LRUCache.OptionsBase.dispose} (read-only)
   */
  get dispose() {
    return m(this, Ct);
  }
  /**
   * {@link LRUCache.OptionsBase.disposeAfter} (read-only)
   */
  get disposeAfter() {
    return m(this, St);
  }
  /**
   * Return the number of ms left in the item's TTL. If item is not in cache,
   * returns `0`. Returns `Infinity` if item is in cache without a defined TTL.
   */
  getRemainingTTL(t) {
    return m(this, Ne).has(t) ? 1 / 0 : 0;
  }
  /**
   * Return a generator yielding `[key, value]` pairs,
   * in order from most recently used to least recently used.
   */
  *entries() {
    for (const t of te(this, At, Kt).call(this))
      m(this, ge)[t] !== void 0 && m(this, Te)[t] !== void 0 && !te(this, $e, Ee).call(this, m(this, ge)[t]) && (yield [m(this, Te)[t], m(this, ge)[t]]);
  }
  /**
   * Inverse order version of {@link LRUCache.entries}
   *
   * Return a generator yielding `[key, value]` pairs,
   * in order from least recently used to most recently used.
   */
  *rentries() {
    for (const t of te(this, It, Yt).call(this))
      m(this, ge)[t] !== void 0 && m(this, Te)[t] !== void 0 && !te(this, $e, Ee).call(this, m(this, ge)[t]) && (yield [m(this, Te)[t], m(this, ge)[t]]);
  }
  /**
   * Return a generator yielding the keys in the cache,
   * in order from most recently used to least recently used.
   */
  *keys() {
    for (const t of te(this, At, Kt).call(this)) {
      const n = m(this, Te)[t];
      n !== void 0 && !te(this, $e, Ee).call(this, m(this, ge)[t]) && (yield n);
    }
  }
  /**
   * Inverse order version of {@link LRUCache.keys}
   *
   * Return a generator yielding the keys in the cache,
   * in order from least recently used to most recently used.
   */
  *rkeys() {
    for (const t of te(this, It, Yt).call(this)) {
      const n = m(this, Te)[t];
      n !== void 0 && !te(this, $e, Ee).call(this, m(this, ge)[t]) && (yield n);
    }
  }
  /**
   * Return a generator yielding the values in the cache,
   * in order from most recently used to least recently used.
   */
  *values() {
    for (const t of te(this, At, Kt).call(this))
      m(this, ge)[t] !== void 0 && !te(this, $e, Ee).call(this, m(this, ge)[t]) && (yield m(this, ge)[t]);
  }
  /**
   * Inverse order version of {@link LRUCache.values}
   *
   * Return a generator yielding the values in the cache,
   * in order from least recently used to most recently used.
   */
  *rvalues() {
    for (const t of te(this, It, Yt).call(this))
      m(this, ge)[t] !== void 0 && !te(this, $e, Ee).call(this, m(this, ge)[t]) && (yield m(this, ge)[t]);
  }
  /**
   * Iterating over the cache itself yields the same results as
   * {@link LRUCache.entries}
   */
  [Symbol.iterator]() {
    return this.entries();
  }
  /**
   * Find a value for which the supplied fn method returns a truthy value,
   * similar to `Array.find()`. fn is called as `fn(value, key, cache)`.
   */
  find(t, n = {}) {
    for (const o of te(this, At, Kt).call(this)) {
      const r = m(this, ge)[o], a = te(this, $e, Ee).call(this, r) ? r.__staleWhileFetching : r;
      if (a !== void 0 && t(a, m(this, Te)[o], this))
        return this.get(m(this, Te)[o], n);
    }
  }
  /**
   * Call the supplied function on each item in the cache, in order from most
   * recently used to least recently used.
   *
   * `fn` is called as `fn(value, key, cache)`.
   *
   * If `thisp` is provided, function will be called in the `this`-context of
   * the provided object, or the cache if no `thisp` object is provided.
   *
   * Does not update age or recenty of use, or iterate over stale values.
   */
  forEach(t, n = this) {
    for (const o of te(this, At, Kt).call(this)) {
      const r = m(this, ge)[o], a = te(this, $e, Ee).call(this, r) ? r.__staleWhileFetching : r;
      a !== void 0 && t.call(n, a, m(this, Te)[o], this);
    }
  }
  /**
   * The same as {@link LRUCache.forEach} but items are iterated over in
   * reverse order.  (ie, less recently used items are iterated over first.)
   */
  rforEach(t, n = this) {
    for (const o of te(this, It, Yt).call(this)) {
      const r = m(this, ge)[o], a = te(this, $e, Ee).call(this, r) ? r.__staleWhileFetching : r;
      a !== void 0 && t.call(n, a, m(this, Te)[o], this);
    }
  }
  /**
   * Delete any stale entries. Returns true if anything was removed,
   * false otherwise.
   */
  purgeStale() {
    let t = false;
    for (const n of te(this, It, Yt).call(this, { allowStale: true }))
      m(this, dt).call(this, n) && (te(this, Vt, Gt).call(this, m(this, Te)[n], "expire"), t = true);
    return t;
  }
  /**
   * Get the extended info about a given entry, to get its value, size, and
   * TTL info simultaneously. Returns `undefined` if the key is not present.
   *
   * Unlike {@link LRUCache#dump}, which is designed to be portable and survive
   * serialization, the `start` value is always the current timestamp, and the
   * `ttl` is a calculated remaining time to live (negative if expired).
   *
   * Always returns stale values, if their info is found in the cache, so be
   * sure to check for expirations (ie, a negative {@link LRUCache.Entry#ttl})
   * if relevant.
   */
  info(t) {
    const n = m(this, Ne).get(t);
    if (n === void 0)
      return;
    const o = m(this, ge)[n], r = te(this, $e, Ee).call(this, o) ? o.__staleWhileFetching : o;
    if (r === void 0)
      return;
    const a = { value: r };
    if (m(this, ct) && m(this, kt)) {
      const i2 = m(this, ct)[n], s = m(this, kt)[n];
      if (i2 && s) {
        const u = i2 - (wn.now() - s);
        a.ttl = u, a.start = Date.now();
      }
    }
    return m(this, Dt) && (a.size = m(this, Dt)[n]), a;
  }
  /**
   * Return an array of [key, {@link LRUCache.Entry}] tuples which can be
   * passed to {@link LRLUCache#load}.
   *
   * The `start` fields are calculated relative to a portable `Date.now()`
   * timestamp, even if `performance.now()` is available.
   *
   * Stale entries are always included in the `dump`, even if
   * {@link LRUCache.OptionsBase.allowStale} is false.
   *
   * Note: this returns an actual array, not a generator, so it can be more
   * easily passed around.
   */
  dump() {
    const t = [];
    for (const n of te(this, At, Kt).call(this, { allowStale: true })) {
      const o = m(this, Te)[n], r = m(this, ge)[n], a = te(this, $e, Ee).call(this, r) ? r.__staleWhileFetching : r;
      if (a === void 0 || o === void 0)
        continue;
      const i2 = { value: a };
      if (m(this, ct) && m(this, kt)) {
        i2.ttl = m(this, ct)[n];
        const s = wn.now() - m(this, kt)[n];
        i2.start = Math.floor(Date.now() - s);
      }
      m(this, Dt) && (i2.size = m(this, Dt)[n]), t.unshift([o, i2]);
    }
    return t;
  }
  /**
   * Reset the cache and load in the items in entries in the order listed.
   *
   * The shape of the resulting cache may be different if the same options are
   * not used in both caches.
   *
   * The `start` fields are assumed to be calculated relative to a portable
   * `Date.now()` timestamp, even if `performance.now()` is available.
   */
  load(t) {
    this.clear();
    for (const [n, o] of t) {
      if (o.start) {
        const r = Date.now() - o.start;
        o.start = wn.now() - r;
      }
      this.set(n, o.value, o);
    }
  }
  /**
   * Add a value to the cache.
   *
   * Note: if `undefined` is specified as a value, this is an alias for
   * {@link LRUCache#delete}
   *
   * Fields on the {@link LRUCache.SetOptions} options param will override
   * their corresponding values in the constructor options for the scope
   * of this single `set()` operation.
   *
   * If `start` is provided, then that will set the effective start
   * time for the TTL calculation. Note that this must be a previous
   * value of `performance.now()` if supported, or a previous value of
   * `Date.now()` if not.
   *
   * Options object may also include `size`, which will prevent
   * calling the `sizeCalculation` function and just use the specified
   * number if it is a positive integer, and `noDisposeOnSet` which
   * will prevent calling a `dispose` function in the case of
   * overwrites.
   *
   * If the `size` (or return value of `sizeCalculation`) for a given
   * entry is greater than `maxEntrySize`, then the item will not be
   * added to the cache.
   *
   * Will update the recency of the entry.
   *
   * If the value is `undefined`, then this is an alias for
   * `cache.delete(key)`. `undefined` is never stored in the cache.
   */
  set(t, n, o = {}) {
    var f, h2, v, g, w;
    if (n === void 0)
      return this.delete(t), this;
    const { ttl: r = this.ttl, start: a, noDisposeOnSet: i2 = this.noDisposeOnSet, sizeCalculation: s = this.sizeCalculation, status: u } = o;
    let { noUpdateTTL: l = this.noUpdateTTL } = o;
    const c = m(this, ao).call(this, t, n, o.size || 0, s);
    if (this.maxEntrySize && c > this.maxEntrySize)
      return u && (u.set = "miss", u.maxEntrySizeExceeded = true), te(this, Vt, Gt).call(this, t, "set"), this;
    let d = m(this, qe) === 0 ? void 0 : m(this, Ne).get(t);
    if (d === void 0)
      d = m(this, qe) === 0 ? m(this, We) : m(this, Rt).length !== 0 ? m(this, Rt).pop() : m(this, qe) === m(this, _t) ? te(this, An, Fo).call(this, false) : m(this, qe), m(this, Te)[d] = t, m(this, ge)[d] = n, m(this, Ne).set(t, d), m(this, et)[m(this, We)] = d, m(this, ut)[d] = m(this, We), de(this, We, d), mo(this, qe)._++, m(this, Tn).call(this, d, c, u), u && (u.set = "add"), l = false;
    else {
      te(this, pn, qn).call(this, d);
      const y = m(this, ge)[d];
      if (n !== y) {
        if (m(this, Jt) && te(this, $e, Ee).call(this, y)) {
          y.__abortController.abort(new Error("replaced"));
          const { __staleWhileFetching: R } = y;
          R !== void 0 && !i2 && (m(this, $t) && ((f = m(this, Ct)) == null || f.call(this, R, t, "set")), m(this, tt) && ((h2 = m(this, Ue)) == null || h2.push([R, t, "set"])));
        } else
          i2 || (m(this, $t) && ((v = m(this, Ct)) == null || v.call(this, y, t, "set")), m(this, tt) && ((g = m(this, Ue)) == null || g.push([y, t, "set"])));
        if (m(this, mn).call(this, d), m(this, Tn).call(this, d, c, u), m(this, ge)[d] = n, u) {
          u.set = "replace";
          const R = y && te(this, $e, Ee).call(this, y) ? y.__staleWhileFetching : y;
          R !== void 0 && (u.oldValue = R);
        }
      } else
        u && (u.set = "update");
    }
    if (r !== 0 && !m(this, ct) && te(this, oo, Cr).call(this), m(this, ct) && (l || m(this, ro).call(this, d, r, a), u && m(this, qt).call(this, u, d)), !i2 && m(this, tt) && m(this, Ue)) {
      const y = m(this, Ue);
      let R;
      for (; R = y == null ? void 0 : y.shift(); )
        (w = m(this, St)) == null || w.call(this, ...R);
    }
    return this;
  }
  /**
   * Evict the least recently used item, returning its value or
   * `undefined` if cache is empty.
   */
  pop() {
    var t;
    try {
      for (; m(this, qe); ) {
        const n = m(this, ge)[m(this, Ge)];
        if (te(this, An, Fo).call(this, true), te(this, $e, Ee).call(this, n)) {
          if (n.__staleWhileFetching)
            return n.__staleWhileFetching;
        } else if (n !== void 0)
          return n;
      }
    } finally {
      if (m(this, tt) && m(this, Ue)) {
        const n = m(this, Ue);
        let o;
        for (; o = n == null ? void 0 : n.shift(); )
          (t = m(this, St)) == null || t.call(this, ...o);
      }
    }
  }
  /**
   * Check if a key is in the cache, without updating the recency of use.
   * Will return false if the item is stale, even though it is technically
   * in the cache.
   *
   * Check if a key is in the cache, without updating the recency of
   * use. Age is updated if {@link LRUCache.OptionsBase.updateAgeOnHas} is set
   * to `true` in either the options or the constructor.
   *
   * Will return `false` if the item is stale, even though it is technically in
   * the cache. The difference can be determined (if it matters) by using a
   * `status` argument, and inspecting the `has` field.
   *
   * Will not update item age unless
   * {@link LRUCache.OptionsBase.updateAgeOnHas} is set.
   */
  has(t, n = {}) {
    const { updateAgeOnHas: o = this.updateAgeOnHas, status: r } = n, a = m(this, Ne).get(t);
    if (a !== void 0) {
      const i2 = m(this, ge)[a];
      if (te(this, $e, Ee).call(this, i2) && i2.__staleWhileFetching === void 0)
        return false;
      if (m(this, dt).call(this, a))
        r && (r.has = "stale", m(this, qt).call(this, r, a));
      else
        return o && m(this, hn).call(this, a), r && (r.has = "hit", m(this, qt).call(this, r, a)), true;
    } else
      r && (r.has = "miss");
    return false;
  }
  /**
   * Like {@link LRUCache#get} but doesn't update recency or delete stale
   * items.
   *
   * Returns `undefined` if the item is stale, unless
   * {@link LRUCache.OptionsBase.allowStale} is set.
   */
  peek(t, n = {}) {
    const { allowStale: o = this.allowStale } = n, r = m(this, Ne).get(t);
    if (r === void 0 || !o && m(this, dt).call(this, r))
      return;
    const a = m(this, ge)[r];
    return te(this, $e, Ee).call(this, a) ? a.__staleWhileFetching : a;
  }
  async fetch(t, n = {}) {
    const {
      // get options
      allowStale: o = this.allowStale,
      updateAgeOnGet: r = this.updateAgeOnGet,
      noDeleteOnStaleGet: a = this.noDeleteOnStaleGet,
      // set options
      ttl: i2 = this.ttl,
      noDisposeOnSet: s = this.noDisposeOnSet,
      size: u = 0,
      sizeCalculation: l = this.sizeCalculation,
      noUpdateTTL: c = this.noUpdateTTL,
      // fetch exclusive options
      noDeleteOnFetchRejection: d = this.noDeleteOnFetchRejection,
      allowStaleOnFetchRejection: f = this.allowStaleOnFetchRejection,
      ignoreFetchAbort: h2 = this.ignoreFetchAbort,
      allowStaleOnFetchAbort: v = this.allowStaleOnFetchAbort,
      context: g,
      forceRefresh: w = false,
      status: y,
      signal: R
    } = n;
    if (!m(this, Jt))
      return y && (y.fetch = "get"), this.get(t, {
        allowStale: o,
        updateAgeOnGet: r,
        noDeleteOnStaleGet: a,
        status: y
      });
    const F = {
      allowStale: o,
      updateAgeOnGet: r,
      noDeleteOnStaleGet: a,
      ttl: i2,
      noDisposeOnSet: s,
      size: u,
      sizeCalculation: l,
      noUpdateTTL: c,
      noDeleteOnFetchRejection: d,
      allowStaleOnFetchRejection: f,
      allowStaleOnFetchAbort: v,
      ignoreFetchAbort: h2,
      status: y,
      signal: R
    };
    let k = m(this, Ne).get(t);
    if (k === void 0) {
      y && (y.fetch = "miss");
      const A = te(this, In, Ro).call(this, t, k, F, g);
      return A.__returned = A;
    } else {
      const A = m(this, ge)[k];
      if (te(this, $e, Ee).call(this, A)) {
        const x = o && A.__staleWhileFetching !== void 0;
        return y && (y.fetch = "inflight", x && (y.returnedStale = true)), x ? A.__staleWhileFetching : A.__returned = A;
      }
      const N = m(this, dt).call(this, k);
      if (!w && !N)
        return y && (y.fetch = "hit"), te(this, pn, qn).call(this, k), r && m(this, hn).call(this, k), y && m(this, qt).call(this, y, k), A;
      const $ = te(this, In, Ro).call(this, t, k, F, g), D = $.__staleWhileFetching !== void 0 && o;
      return y && (y.fetch = N ? "stale" : "refresh", D && N && (y.returnedStale = true)), D ? $.__staleWhileFetching : $.__returned = $;
    }
  }
  async forceFetch(t, n = {}) {
    const o = await this.fetch(t, n);
    if (o === void 0)
      throw new Error("fetch() returned undefined");
    return o;
  }
  memo(t, n = {}) {
    const o = m(this, En);
    if (!o)
      throw new Error("no memoMethod provided to constructor");
    const { context: r, forceRefresh: a, ...i2 } = n, s = this.get(t, i2);
    if (!a && s !== void 0)
      return s;
    const u = o(t, s, {
      options: i2,
      context: r
    });
    return this.set(t, u, i2), u;
  }
  /**
   * Return a value from the cache. Will update the recency of the cache
   * entry found.
   *
   * If the key is not found, get() will return `undefined`.
   */
  get(t, n = {}) {
    const { allowStale: o = this.allowStale, updateAgeOnGet: r = this.updateAgeOnGet, noDeleteOnStaleGet: a = this.noDeleteOnStaleGet, status: i2 } = n, s = m(this, Ne).get(t);
    if (s !== void 0) {
      const u = m(this, ge)[s], l = te(this, $e, Ee).call(this, u);
      return i2 && m(this, qt).call(this, i2, s), m(this, dt).call(this, s) ? (i2 && (i2.get = "stale"), l ? (i2 && o && u.__staleWhileFetching !== void 0 && (i2.returnedStale = true), o ? u.__staleWhileFetching : void 0) : (a || te(this, Vt, Gt).call(this, t, "expire"), i2 && o && (i2.returnedStale = true), o ? u : void 0)) : (i2 && (i2.get = "hit"), l ? u.__staleWhileFetching : (te(this, pn, qn).call(this, s), r && m(this, hn).call(this, s), u));
    } else
      i2 && (i2.get = "miss");
  }
  /**
   * Deletes a key out of the cache.
   *
   * Returns true if the key was deleted, false otherwise.
   */
  delete(t) {
    return te(this, Vt, Gt).call(this, t, "delete");
  }
  /**
   * Clear the cache entirely, throwing away all values.
   */
  clear() {
    return te(this, lo, Rr).call(this, "delete");
  }
};
zm = Symbol.toStringTag, _t = /* @__PURE__ */ new WeakMap(), lt = /* @__PURE__ */ new WeakMap(), Ct = /* @__PURE__ */ new WeakMap(), St = /* @__PURE__ */ new WeakMap(), On = /* @__PURE__ */ new WeakMap(), En = /* @__PURE__ */ new WeakMap(), qe = /* @__PURE__ */ new WeakMap(), Ft = /* @__PURE__ */ new WeakMap(), Ne = /* @__PURE__ */ new WeakMap(), Te = /* @__PURE__ */ new WeakMap(), ge = /* @__PURE__ */ new WeakMap(), et = /* @__PURE__ */ new WeakMap(), ut = /* @__PURE__ */ new WeakMap(), Ge = /* @__PURE__ */ new WeakMap(), We = /* @__PURE__ */ new WeakMap(), Rt = /* @__PURE__ */ new WeakMap(), Ue = /* @__PURE__ */ new WeakMap(), Dt = /* @__PURE__ */ new WeakMap(), kt = /* @__PURE__ */ new WeakMap(), ct = /* @__PURE__ */ new WeakMap(), $t = /* @__PURE__ */ new WeakMap(), Jt = /* @__PURE__ */ new WeakMap(), tt = /* @__PURE__ */ new WeakMap(), oo = /* @__PURE__ */ new WeakSet(), Cr = function() {
  const t = new So(m(this, _t)), n = new So(m(this, _t));
  de(this, ct, t), de(this, kt, n), de(this, ro, (a, i2, s = wn.now()) => {
    if (n[a] = i2 !== 0 ? s : 0, t[a] = i2, i2 !== 0 && this.ttlAutopurge) {
      const u = setTimeout(() => {
        m(this, dt).call(this, a) && te(this, Vt, Gt).call(this, m(this, Te)[a], "expire");
      }, i2 + 1);
      u.unref && u.unref();
    }
  }), de(this, hn, (a) => {
    n[a] = t[a] !== 0 ? wn.now() : 0;
  }), de(this, qt, (a, i2) => {
    if (t[i2]) {
      const s = t[i2], u = n[i2];
      if (!s || !u)
        return;
      a.ttl = s, a.start = u, a.now = o || r();
      const l = a.now - u;
      a.remainingTTL = s - l;
    }
  });
  let o = 0;
  const r = () => {
    const a = wn.now();
    if (this.ttlResolution > 0) {
      o = a;
      const i2 = setTimeout(() => o = 0, this.ttlResolution);
      i2.unref && i2.unref();
    }
    return a;
  };
  this.getRemainingTTL = (a) => {
    const i2 = m(this, Ne).get(a);
    if (i2 === void 0)
      return 0;
    const s = t[i2], u = n[i2];
    if (!s || !u)
      return 1 / 0;
    const l = (o || r()) - u;
    return s - l;
  }, de(this, dt, (a) => {
    const i2 = n[a], s = t[a];
    return !!s && !!i2 && (o || r()) - i2 > s;
  });
}, hn = /* @__PURE__ */ new WeakMap(), qt = /* @__PURE__ */ new WeakMap(), ro = /* @__PURE__ */ new WeakMap(), dt = /* @__PURE__ */ new WeakMap(), Ho = /* @__PURE__ */ new WeakSet(), li = function() {
  const t = new So(m(this, _t));
  de(this, Ft, 0), de(this, Dt, t), de(this, mn, (n) => {
    de(this, Ft, m(this, Ft) - t[n]), t[n] = 0;
  }), de(this, ao, (n, o, r, a) => {
    if (te(this, $e, Ee).call(this, o))
      return 0;
    if (!Ut(r))
      if (a) {
        if (typeof a != "function")
          throw new TypeError("sizeCalculation must be a function");
        if (r = a(o, n), !Ut(r))
          throw new TypeError("sizeCalculation return invalid (expect positive integer)");
      } else
        throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");
    return r;
  }), de(this, Tn, (n, o, r) => {
    if (t[n] = o, m(this, lt)) {
      const a = m(this, lt) - t[n];
      for (; m(this, Ft) > a; )
        te(this, An, Fo).call(this, true);
    }
    de(this, Ft, m(this, Ft) + t[n]), r && (r.entrySize = o, r.totalCalculatedSize = m(this, Ft));
  });
}, mn = /* @__PURE__ */ new WeakMap(), Tn = /* @__PURE__ */ new WeakMap(), ao = /* @__PURE__ */ new WeakMap(), At = /* @__PURE__ */ new WeakSet(), Kt = function* ({ allowStale: t = this.allowStale } = {}) {
  if (m(this, qe))
    for (let n = m(this, We); !(!te(this, io, Sr).call(this, n) || ((t || !m(this, dt).call(this, n)) && (yield n), n === m(this, Ge))); )
      n = m(this, ut)[n];
}, It = /* @__PURE__ */ new WeakSet(), Yt = function* ({ allowStale: t = this.allowStale } = {}) {
  if (m(this, qe))
    for (let n = m(this, Ge); !(!te(this, io, Sr).call(this, n) || ((t || !m(this, dt).call(this, n)) && (yield n), n === m(this, We))); )
      n = m(this, et)[n];
}, io = /* @__PURE__ */ new WeakSet(), Sr = function(t) {
  return t !== void 0 && m(this, Ne).get(m(this, Te)[t]) === t;
}, An = /* @__PURE__ */ new WeakSet(), Fo = function(t) {
  var a, i2;
  const n = m(this, Ge), o = m(this, Te)[n], r = m(this, ge)[n];
  return m(this, Jt) && te(this, $e, Ee).call(this, r) ? r.__abortController.abort(new Error("evicted")) : (m(this, $t) || m(this, tt)) && (m(this, $t) && ((a = m(this, Ct)) == null || a.call(this, r, o, "evict")), m(this, tt) && ((i2 = m(this, Ue)) == null || i2.push([r, o, "evict"]))), m(this, mn).call(this, n), t && (m(this, Te)[n] = void 0, m(this, ge)[n] = void 0, m(this, Rt).push(n)), m(this, qe) === 1 ? (de(this, Ge, de(this, We, 0)), m(this, Rt).length = 0) : de(this, Ge, m(this, et)[n]), m(this, Ne).delete(o), mo(this, qe)._--, n;
}, In = /* @__PURE__ */ new WeakSet(), Ro = function(t, n, o, r) {
  const a = n === void 0 ? void 0 : m(this, ge)[n];
  if (te(this, $e, Ee).call(this, a))
    return a;
  const i2 = new Mo(), { signal: s } = o;
  s == null || s.addEventListener("abort", () => i2.abort(s.reason), {
    signal: i2.signal
  });
  const u = {
    signal: i2.signal,
    options: o,
    context: r
  }, l = (g, w = false) => {
    const { aborted: y } = i2.signal, R = o.ignoreFetchAbort && g !== void 0;
    if (o.status && (y && !w ? (o.status.fetchAborted = true, o.status.fetchError = i2.signal.reason, R && (o.status.fetchAbortIgnored = true)) : o.status.fetchResolved = true), y && !R && !w)
      return d(i2.signal.reason);
    const F = h2;
    return m(this, ge)[n] === h2 && (g === void 0 ? F.__staleWhileFetching ? m(this, ge)[n] = F.__staleWhileFetching : te(this, Vt, Gt).call(this, t, "fetch") : (o.status && (o.status.fetchUpdated = true), this.set(t, g, u.options))), g;
  }, c = (g) => (o.status && (o.status.fetchRejected = true, o.status.fetchError = g), d(g)), d = (g) => {
    const { aborted: w } = i2.signal, y = w && o.allowStaleOnFetchAbort, R = y || o.allowStaleOnFetchRejection, F = R || o.noDeleteOnFetchRejection, k = h2;
    if (m(this, ge)[n] === h2 && (!F || k.__staleWhileFetching === void 0 ? te(this, Vt, Gt).call(this, t, "fetch") : y || (m(this, ge)[n] = k.__staleWhileFetching)), R)
      return o.status && k.__staleWhileFetching !== void 0 && (o.status.returnedStale = true), k.__staleWhileFetching;
    if (k.__returned === k)
      throw g;
  }, f = (g, w) => {
    var R;
    const y = (R = m(this, On)) == null ? void 0 : R.call(this, t, a, u);
    y && y instanceof Promise && y.then((F) => g(F === void 0 ? void 0 : F), w), i2.signal.addEventListener("abort", () => {
      (!o.ignoreFetchAbort || o.allowStaleOnFetchAbort) && (g(void 0), o.allowStaleOnFetchAbort && (g = (F) => l(F, true)));
    });
  };
  o.status && (o.status.fetchDispatched = true);
  const h2 = new Promise(f).then(l, c), v = Object.assign(h2, {
    __abortController: i2,
    __staleWhileFetching: a,
    __returned: void 0
  });
  return n === void 0 ? (this.set(t, v, { ...u.options, status: void 0 }), n = m(this, Ne).get(t)) : m(this, ge)[n] = v, v;
}, $e = /* @__PURE__ */ new WeakSet(), Ee = function(t) {
  if (!m(this, Jt))
    return false;
  const n = t;
  return !!n && n instanceof Promise && n.hasOwnProperty("__staleWhileFetching") && n.__abortController instanceof Mo;
}, so = /* @__PURE__ */ new WeakSet(), Fr = function(t, n) {
  m(this, ut)[n] = t, m(this, et)[t] = n;
}, pn = /* @__PURE__ */ new WeakSet(), qn = function(t) {
  t !== m(this, We) && (t === m(this, Ge) ? de(this, Ge, m(this, et)[t]) : te(this, so, Fr).call(this, m(this, ut)[t], m(this, et)[t]), te(this, so, Fr).call(this, m(this, We), t), de(this, We, t));
}, Vt = /* @__PURE__ */ new WeakSet(), Gt = function(t, n) {
  var r, a, i2, s;
  let o = false;
  if (m(this, qe) !== 0) {
    const u = m(this, Ne).get(t);
    if (u !== void 0)
      if (o = true, m(this, qe) === 1)
        te(this, lo, Rr).call(this, n);
      else {
        m(this, mn).call(this, u);
        const l = m(this, ge)[u];
        if (te(this, $e, Ee).call(this, l) ? l.__abortController.abort(new Error("deleted")) : (m(this, $t) || m(this, tt)) && (m(this, $t) && ((r = m(this, Ct)) == null || r.call(this, l, t, n)), m(this, tt) && ((a = m(this, Ue)) == null || a.push([l, t, n]))), m(this, Ne).delete(t), m(this, Te)[u] = void 0, m(this, ge)[u] = void 0, u === m(this, We))
          de(this, We, m(this, ut)[u]);
        else if (u === m(this, Ge))
          de(this, Ge, m(this, et)[u]);
        else {
          const c = m(this, ut)[u];
          m(this, et)[c] = m(this, et)[u];
          const d = m(this, et)[u];
          m(this, ut)[d] = m(this, ut)[u];
        }
        mo(this, qe)._--, m(this, Rt).push(u);
      }
  }
  if (m(this, tt) && ((i2 = m(this, Ue)) != null && i2.length)) {
    const u = m(this, Ue);
    let l;
    for (; l = u == null ? void 0 : u.shift(); )
      (s = m(this, St)) == null || s.call(this, ...l);
  }
  return o;
}, lo = /* @__PURE__ */ new WeakSet(), Rr = function(t) {
  var n, o, r;
  for (const a of te(this, It, Yt).call(this, { allowStale: true })) {
    const i2 = m(this, ge)[a];
    if (te(this, $e, Ee).call(this, i2))
      i2.__abortController.abort(new Error("deleted"));
    else {
      const s = m(this, Te)[a];
      m(this, $t) && ((n = m(this, Ct)) == null || n.call(this, i2, s, t)), m(this, tt) && ((o = m(this, Ue)) == null || o.push([i2, s, t]));
    }
  }
  if (m(this, Ne).clear(), m(this, ge).fill(void 0), m(this, Te).fill(void 0), m(this, ct) && m(this, kt) && (m(this, ct).fill(0), m(this, kt).fill(0)), m(this, Dt) && m(this, Dt).fill(0), de(this, Ge, 0), de(this, We, 0), m(this, Rt).length = 0, de(this, Ft, 0), de(this, qe, 0), m(this, tt) && m(this, Ue)) {
    const a = m(this, Ue);
    let i2;
    for (; i2 = a == null ? void 0 : a.shift(); )
      (r = m(this, St)) == null || r.call(this, ...i2);
  }
};
var _r = Xr;
var po = new _r({
  max: 500,
  maxSize: 5e3,
  ttl: 1e3 * 60 * 30,
  sizeCalculation: (e, t) => 1
});
var { UnMergeable: pu } = De();
function gu(e) {
  ui = e;
}
var ui = async (e) => (ue.warn("请配置 app.use(FsCrud,{dictRequest:(context)=>{ 你的字典请求方法 }})"), []);
var ci = class extends pu {
  //loadDict成功后的通知
  constructor(n) {
    super();
    se(this, "cache", false);
    se(this, "prototype", false);
    se(this, "immediate", true);
    se(this, "url");
    se(this, "getData");
    se(this, "value", "value");
    se(this, "label", "label");
    se(this, "labelBuilder");
    se(this, "children", "children");
    se(this, "color", "color");
    se(this, "isTree", false);
    se(this, "_data", null);
    se(this, "originalData");
    se(this, "dataMap", {});
    se(this, "loading", false);
    se(this, "custom", {});
    se(this, "getNodesByValues");
    se(this, "onReady");
    se(this, "notifies", []);
    se(this, "_unfetchValues", {});
    Object.defineProperty(this, "loading", {
      value: false,
      enumerable: false
    }), Object.defineProperty(this, "notifies", {
      value: false,
      enumerable: false
    }), Object.defineProperty(this, "originalData", {
      value: null,
      enumerable: false
    }), this.loading = false, merge_default(this, n), n.data != null && (this.originalData = n.data, this.setData(n.data)), this.toMap();
  }
  get data() {
    return this._data;
  }
  set data(n) {
    this._data = n, this.toMap();
  }
  isDynamic() {
    return this.url instanceof Function || this.getData instanceof Function || this.prototype;
  }
  setData(n) {
    this.data = n;
  }
  /**
   * 加载字典
   */
  async _loadDict(n) {
    if (this.data && !n.reload)
      return this.data;
    if (this.loading)
      return this._registerNotify();
    let o = null;
    if (this.getNodesByValues) {
      if (n.value) {
        let r = null;
        this.cache && this.url && (r = this.url + n.value);
        let a = null;
        if (r && (a = po.get(r)), a)
          o = a;
        else {
          const i2 = Array.isArray(n.value) ? n.value : [n.value];
          if (o = await this.getNodesByValues(i2, n), o != null && !(o instanceof Array)) {
            ue.error("getNodesByValues需要返回数组，当前返回值：", o);
            return;
          }
          r && po.set(r, o);
        }
      }
    } else if (this.originalData)
      o = this.originalData;
    else {
      this.loading = true;
      try {
        o = await this.getRemoteDictData(n);
      } finally {
        this.loading = false;
      }
    }
    this.data = o, this.onReady && this.onReady({ dict: this, ...n }), this._triggerNotify();
  }
  _triggerNotify() {
    this.notifies && this.notifies.length > 0 && (forEach_default(this.notifies, (n) => {
      n(this.data);
    }), this.notifies.length = 0);
  }
  _registerNotify() {
    let n = null;
    const o = new Promise((r) => {
      n = (a) => {
        r(a);
      };
    });
    return this.notifies || (this.notifies = []), this.notifies.push(n), o;
  }
  /**
   * 加载字典
   * @param context 当prototype=true时会传入
   */
  async loadDict(n) {
    return await this._loadDict({ ...n });
  }
  async reloadDict(n) {
    return await this.loadDict({ ...n, reload: true });
  }
  /**
   * 根据value获取nodes 追加数据
   * @param values
   */
  async appendByValues(n) {
    if (this.getNodesByValues == null) {
      ue.warn("请配置getNodesByValues");
      return;
    }
    for (const r of n)
      this.dataMap[r] || this._unfetchValues[r] || (this._unfetchValues[r] = {
        loading: false,
        value: r
      });
    await nextTick(), await nextTick(), await nextTick();
    const o = [];
    if (forEach_default(this._unfetchValues, (r) => {
      r.loading || (r.loading = true, o.push(r.value));
    }), o.length > 0) {
      const r = await this.getNodesByValues(o);
      this.setData([...this.data || [], ...r]);
      for (const a of o)
        delete this._unfetchValues[a];
      return Object.keys(this._unfetchValues).length === 0 && this._triggerNotify(), this.data;
    } else
      return this._registerNotify();
  }
  clear() {
    this.originalData = null, this.setData(null);
  }
  async getRemoteDictData(n) {
    let o, r, a;
    if (this.url && (a = this.url, a instanceof Function && (a = a({ ...n, dict: this })), r = a), this.getData != null)
      o = async () => {
        const i2 = await this.getData({ url: a, dict: this, ...n });
        return Array.isArray(i2) ? i2 : [];
      };
    else if (a)
      o = async () => {
        const i2 = await ui({ url: a, dict: this });
        return Array.isArray(i2) ? i2 : [];
      };
    else
      return [];
    if (this.cache && r) {
      let i2 = po.get(r);
      if (i2 == null)
        i2 = {
          loaded: false,
          loading: true,
          data: void 0,
          callback: []
        }, po.set(r, i2);
      else {
        if (i2.loaded)
          return i2.data;
        if (i2.loading)
          return new Promise((s) => {
            const u = (l) => {
              s(l);
            };
            i2.callback.push(u);
          });
      }
      try {
        i2.loaded = false, i2.loading = true;
        let s = await o();
        s = s || [], s instanceof Array || ue.warn("dict data 格式有误，期望格式为数组，实际格式为：", s), i2.data = s, i2.loaded = true, i2.loading = false;
        for (const u of i2.callback)
          u(s);
        return i2.callback = [], s;
      } catch (s) {
        i2.loading = false, i2.loaded = false, ue.error("load dict error:", s);
      }
    }
    return await o();
  }
  toMap() {
    if (this._data == null) {
      this.dataMap = {};
      return;
    }
    const n = {};
    this.data && this.buildMap(n, this.data || []), this.dataMap = n;
  }
  buildMap(n, o) {
    forEach_default(o, (r) => {
      n[this.getValue(r)] = r, this.isTree && this.getChildren(r) && this.buildMap(n, this.getChildren(r));
    });
  }
  getValue(n) {
    return n[this.value];
  }
  getLabel(n) {
    return this.labelBuilder ? this.labelBuilder(n) : n[this.label];
  }
  getChildren(n) {
    return n[this.children];
  }
  getColor(n) {
    return n[this.color];
  }
  getDictData() {
    return this.data;
  }
  getDictMap() {
    return this.dataMap;
  }
  getNodeByValue(n) {
    return this.dataMap[n];
  }
  getNodesFromDataMap(n) {
    if (n == null)
      return [];
    isArray_default(n) || (n = [n]);
    const o = [];
    return forEach_default(n, (r) => {
      const a = this.dataMap[r];
      a ? o.push(a) : o.push({ [this.value]: r });
    }), o;
  }
};
function vu(e) {
  const t = shallowReactive(new ci(e));
  return !t.prototype && t.immediate && t.loadDict(), t;
}
function bu() {
  return {
    dict: vu,
    setDictRequest: gu,
    Dict: ci
  };
}
function rp() {
  return {
    addTypes: Rn.addTypes,
    getType: Rn.getType,
    getTypes: Rn.getTypes,
    install: Rn.install
  };
}
async function yu(e) {
  await nextTick();
  const t = e.getModal();
  if (t == null)
    return;
  const n = t.querySelector(".ant-modal-header");
  if (n == null)
    return;
  const o = t, a = t.style;
  n.style.cursor = "move", n.onmousedown = (i2) => {
    const s = i2.clientX, u = i2.clientY;
    o.style.transform = "translate(0px, 0px)";
    let l, c;
    a.left.includes("%") ? (l = +document.body.clientWidth * (+a.left.replace(/%/g, "") / 100), c = +document.body.clientHeight * (+a.top.replace(/%/g, "") / 100)) : (l = +a.left.replace(/px/g, ""), c = +a.top.replace(/px/g, ""), c = c === 0 ? 100 : c), document.onmousemove = function(d) {
      const f = d.clientX - s, h2 = d.clientY - u;
      o.style.left = `${f + l}px`, o.style.top = `${h2 + c}px`;
    }, document.onmouseup = function(d) {
      document.onmousemove = null, document.onmouseup = null;
    };
  };
}
async function wu(e) {
  await nextTick();
  const t = e.getModal();
  if (t == null)
    return;
  const n = t.querySelector(".n-card-header");
  if (n == null)
    return;
  const o = t, a = t.style;
  n.style.cursor = "move", n.onmousedown = (i2) => {
    const s = i2.clientX, u = i2.clientY;
    o.style.transform = "translate(0px, 0px)";
    let l, c;
    a.left.includes("%") ? (l = +document.body.clientWidth * (+a.left.replace(/%/g, "") / 100), c = +document.body.clientHeight * (+a.top.replace(/%/g, "") / 100)) : (l = +a.left.replace(/px/g, ""), c = +a.top.replace(/px/g, "")), document.onmousemove = async function(d) {
      if (d.clientX == 0 && d.clientY == 0)
        return;
      const f = d.clientX - s, h2 = d.clientY - u;
      o.style.left = `${f + l}px`, o.style.top = `${h2 + c}px`;
    }, document.onmouseup = function(d) {
      document.onmousemove = null, document.onmouseup = null;
    };
  };
}
async function _u(e) {
  const { ui: t } = B();
  t.type === "antdv" ? await yu(e) : t.type === "element" || await wu(e);
}
function Cu() {
  return {
    dragModal: _u
  };
}
var Su = {
  logger: {
    off: {
      tableColumns: false
    }
  }
};
var Fu = defineComponent({
  name: "FsContainer",
  props: {
    /**
     * 是否固定高度
     */
    fixedHeight: {
      type: Boolean,
      default: true
    },
    /**
     * body的样式
     */
    bodyStyle: {
      type: Object,
      default: () => ({})
    },
    /**
     * inner的样式
     */
    innerStyle: {
      type: Object,
      default: () => ({})
    }
  },
  setup(e, t) {
    const { merge: n } = De(), o = computed(() => e.fixedHeight === false ? n({ position: "relative" }, e.innerStyle) : e.innerStyle), r = computed(() => e.fixedHeight === false ? n({ flex: "unset" }, e.bodyStyle) : e.bodyStyle);
    return {
      computedInnerStyle: o,
      computedBodyStyle: r
    };
  }
});
var Ru = { class: "fs-container" };
var Du = { class: "box" };
var ku = { class: "header" };
var $u = { class: "footer" };
function Ou(e, t, n, o, r, a) {
  return openBlock(), createElementBlock("div", Ru, [
    createBaseVNode("div", Du, [
      createBaseVNode("div", {
        class: "inner",
        style: normalizeStyle(e.computedInnerStyle)
      }, [
        createBaseVNode("div", ku, [
          renderSlot(e.$slots, "header")
        ]),
        createBaseVNode("div", {
          class: "body",
          style: normalizeStyle(e.computedBodyStyle)
        }, [
          renderSlot(e.$slots, "default")
        ], 4),
        createBaseVNode("div", $u, [
          renderSlot(e.$slots, "footer")
        ])
      ], 4)
    ]),
    renderSlot(e.$slots, "box")
  ]);
}
var Eu = ke(Fu, [["render", Ou]]);
var Tu = defineComponent({
  name: "FsLayoutDefault"
});
var Au = { class: "fs-crud-header" };
var Iu = { class: "fs-header-top" };
var Vu = { class: "fs-header-middle" };
var Pu = { class: "fs-header-bottom" };
var Mu = { class: "fs-crud-footer" };
function Bu(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-container");
  return openBlock(), createBlock(i2, { class: "fs-layout-default" }, {
    header: withCtx(() => [
      createBaseVNode("div", Au, [
        createBaseVNode("div", Iu, [
          renderSlot(e.$slots, "header-top")
        ]),
        renderSlot(e.$slots, "search"),
        createBaseVNode("div", Vu, [
          renderSlot(e.$slots, "header-middle")
        ]),
        renderSlot(e.$slots, "actionbar"),
        renderSlot(e.$slots, "toolbar"),
        createBaseVNode("div", Pu, [
          renderSlot(e.$slots, "header-bottom")
        ]),
        renderSlot(e.$slots, "tabs")
      ])
    ]),
    footer: withCtx(() => [
      createBaseVNode("div", Mu, [
        renderSlot(e.$slots, "footer-top"),
        renderSlot(e.$slots, "pagination"),
        renderSlot(e.$slots, "footer-bottom")
      ])
    ]),
    default: withCtx(() => [
      renderSlot(e.$slots, "default"),
      renderSlot(e.$slots, "table"),
      renderSlot(e.$slots, "form")
    ]),
    _: 3
  });
}
var ju = ke(Tu, [["render", Bu]]);
var Nu = defineComponent({
  name: "FsLayoutCard",
  setup() {
    const e = inject("get:crudBinding"), t = computed(() => {
      var r, a;
      return e == null ? true : (a = (r = e()) == null ? void 0 : r.search) == null ? void 0 : a.show;
    }), { ui: n } = B(), o = ref(n.card.name);
    return {
      searchShow: t,
      cardComponentName: o
    };
  }
});
var Lu = { class: "fs-header-top" };
var qu = { class: "fs-header-bottom" };
var xu = { class: "top-bar" };
var Hu = { class: "top-bar" };
var zu = { class: "fs-crud-footer" };
function Wu(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-container");
  return openBlock(), createBlock(i2, { class: "fs-layout-card" }, createSlots({
    default: withCtx(() => [
      createVNode(i2, null, {
        header: withCtx(() => [
          renderSlot(e.$slots, "tabs")
        ]),
        default: withCtx(() => [
          (openBlock(), createBlock(resolveDynamicComponent(e.cardComponentName), { class: "fs-layout-card-body" }, {
            title: withCtx(() => [
              createBaseVNode("div", xu, [
                renderSlot(e.$slots, "actionbar"),
                renderSlot(e.$slots, "toolbar")
              ])
            ]),
            header: withCtx(() => [
              createBaseVNode("div", Hu, [
                renderSlot(e.$slots, "actionbar"),
                renderSlot(e.$slots, "toolbar")
              ])
            ]),
            default: withCtx(() => [
              createVNode(i2, null, {
                footer: withCtx(() => [
                  createBaseVNode("div", zu, [
                    renderSlot(e.$slots, "footer-top"),
                    renderSlot(e.$slots, "pagination"),
                    renderSlot(e.$slots, "footer-bottom")
                  ])
                ]),
                default: withCtx(() => [
                  renderSlot(e.$slots, "default"),
                  renderSlot(e.$slots, "table"),
                  renderSlot(e.$slots, "form")
                ]),
                _: 3
              })
            ]),
            _: 3
          }))
        ]),
        _: 3
      })
    ]),
    _: 2
  }, [
    e.searchShow ? {
      name: "header",
      fn: withCtx(() => [
        createBaseVNode("div", Lu, [
          renderSlot(e.$slots, "header-top")
        ]),
        (openBlock(), createBlock(resolveDynamicComponent(e.cardComponentName), null, {
          default: withCtx(() => [
            renderSlot(e.$slots, "search")
          ]),
          _: 3
        })),
        createBaseVNode("div", qu, [
          renderSlot(e.$slots, "header-bottom")
        ])
      ]),
      key: "0"
    } : void 0
  ]), 1024);
}
var Uu = ke(Nu, [["render", Wu]]);
function Ku(e) {
  return typeof e == "function" || Object.prototype.toString.call(e) === "[object Object]" && !isVNode(e);
}
var Yu = defineComponent({
  name: "FsButton",
  inheritAttrs: false,
  props: {
    /**
     * 文字
     */
    text: {
      type: String,
      default: "",
      required: false
    },
    /**
     * 图标
     */
    icon: {
      type: [String, Object, Function],
      default: "",
      required: false
    },
    /**
     * 右边的图标
     */
    iconRight: {
      type: [String, Object, Function],
      default: "",
      required: false
    },
    /**
     * 是否圆形按钮，text需配置为null
     */
    circle: {
      type: Boolean,
      default: false,
      required: false
    },
    /**
     * tooltip配置，为空不显示tooltip
     */
    tooltip: {
      type: Object,
      default: void 0
    },
    /**
     * x-button的配置，当x-button的配置与fs-button的配置有冲突时可以配置在此处
     * 比如：n-button的text
     */
    buttonProps: {
      type: Object,
      default: void 0
    },
    className: {}
  },
  setup(e, t) {
    const {
      ui: n
    } = B(), {
      merge: o
    } = De(), r = (l, c = "fs-button-icon") => {
      if (l != null)
        return typeof l == "string" ? createVNode(resolveComponent("fs-icon"), {
          icon: l,
          class: c
        }, null) : typeof l == "function" ? l() : createVNode(resolveComponent("fs-icon"), mergeProps(l, {
          class: c
        }), null);
    }, a = () => {
      const l = e.icon, c = e.iconRight, d = n.type !== "element", f = !d;
      let h2;
      const v = {
        ...t.slots
      };
      (l && !d && !f || t.slots.default || e.text || c) && (v.default = () => {
        const R = [];
        return l && !d && !f && R.push(r(l)), t.slots.default && R.push(t.slots.default()), e.text && R.push(e.text), c && R.push(r(c, "fs-button-icon-right")), R;
      }), l && (d && !v.icon ? v.icon = () => r(l) : f && !v.icon && (h2 = r(l)));
      const g = e.circle ? n.button.circle : {}, w = resolveDynamicComponent(n.button.name), y = o({
        ...g,
        //icon,
        class: {
          "fs-button": true,
          "is-thin": !e.text && !t.slots.default
        }
      }, {
        class: e.className,
        ...t.attrs,
        ...e.buttonProps
      });
      return h2 && (y.icon = h2), h(w, y, v);
    };
    if (!e.tooltip)
      return a;
    const i2 = resolveDynamicComponent(n.tooltip.name), s = computed(() => omit_default(e.tooltip, "slots")), u = n.tooltip.trigger;
    return () => {
      var c;
      const l = {
        ...(c = e.tooltip) == null ? void 0 : c.slots,
        [u]: a
      };
      return createVNode(i2, s.value, Ku(l) ? l : {
        default: () => [l]
      });
    };
  }
});
var Gu = defineComponent({
  name: "FsIcon",
  inheritAttrs: false,
  props: {
    /**
     * icon名称
     */
    icon: {
      type: String,
      default: void 0,
      require: true
    }
  },
  setup(e, t) {
    const {
      ui: n
    } = B(), o = computed(() => {
      var a;
      if (e.icon && ((a = e.icon) == null ? void 0 : a.indexOf(":")) >= 0) {
        if (e.icon.startsWith("svg:")) {
          const s = resolveDynamicComponent("FsIconSvg");
          return () => {
            const u = e.icon.replace("svg:", "");
            return createVNode(s, mergeProps({
              class: "fs-icon",
              icon: u
            }, t.attrs), null);
          };
        }
        const i2 = resolveDynamicComponent("FsIconify");
        return () => createVNode(i2, mergeProps({
          class: "fs-icon",
          icon: e.icon
        }, t.attrs), null);
      }
      const r = resolveDynamicComponent(e.icon);
      return typeof r == "string" ? () => createVNode("span", {
        title: "error icon name"
      }, [r]) : n.icon.isComponent ? () => createVNode(r, mergeProps({
        class: "fs-icon"
      }, t.attrs), null) : () => createVNode(resolveComponent("el-icon"), mergeProps({
        class: "fs-icon"
      }, t.attrs), {
        default: () => [createVNode(r, null, null)]
      });
    });
    return () => o.value();
  }
});
var Xu = defineComponent({
  name: "FsIconify",
  components: { Icon },
  props: {
    /**
     * 图标名称
     */
    icon: {
      type: String
    },
    /**
     * 旋转
     */
    spin: {
      type: Boolean,
      default: false
    }
  },
  setup(e, t) {
    return { iconifyRef: ref(null) };
  }
});
function Qu(e, t, n, o, r, a) {
  const i2 = resolveComponent("Icon");
  return openBlock(), createElementBlock("span", {
    class: normalizeClass(["fs-iconify", { "fs-iconify-spin": e.spin }])
  }, [
    createVNode(i2, {
      ref: "iconifyRef",
      icon: e.icon,
      inline: true
    }, null, 8, ["icon"])
  ], 2);
}
var Ju = ke(Xu, [["render", Qu]]);
var Zu = defineComponent({
  name: "FsSvgIcon",
  props: {
    icon: {
      type: String,
      required: true
    },
    size: {
      type: [Number, String],
      default: 16
    },
    spin: {
      type: Boolean,
      default: false
    }
  },
  setup(e) {
    const t = computed(() => `#${e.icon}`), n = computed(() => {
      const { size: o } = e;
      let r = `${o}`;
      return r = `${r.replace("px", "")}px`, {
        width: r,
        height: r
      };
    });
    return { symbolId: t, getStyle: n };
  }
});
var ec = {
  class: "fs-icon-svg-content",
  "aria-hidden": "true"
};
var tc = ["xlink:href"];
function nc(e, t, n, o, r, a) {
  return openBlock(), createElementBlock("span", {
    class: normalizeClass(["fs-icon-svg", e.spin && "fs-icon-spin"])
  }, [
    (openBlock(), createElementBlock("svg", ec, [
      createBaseVNode("use", { "xlink:href": e.symbolId }, null, 8, tc)
    ]))
  ], 2);
}
var oc = ke(Zu, [["render", nc]]);
var rc = defineComponent({
  name: "FsLabel",
  props: {
    label: {
      type: String,
      default: ""
    },
    labelAttrs: {
      type: Object,
      default: () => ({})
    },
    contentAttrs: {
      type: Object,
      default: () => ({})
    }
  }
});
var ac = { class: "fs-label" };
function ic(e, t, n, o, r, a) {
  return openBlock(), createElementBlock("div", ac, [
    createBaseVNode("div", mergeProps({ class: "label" }, e.labelAttrs), [
      createTextVNode(toDisplayString(e.label) + " ", 1),
      renderSlot(e.$slots, "label")
    ], 16),
    createBaseVNode("div", mergeProps({ class: "content" }, e.contentAttrs), [
      renderSlot(e.$slots, "default")
    ], 16)
  ]);
}
var sc = ke(rc, [["render", ic]]);
var lc = {
  key: 0,
  class: "fs-loading"
};
var uc = { key: 0 };
var cc = defineComponent({
  name: "FsLoading"
});
var dc = defineComponent({
  ...cc,
  props: {
    loading: { type: Boolean },
    icon: {},
    text: {}
  },
  setup(e) {
    const { ui: t } = B();
    return (n, o) => {
      const r = resolveComponent("fs-icon");
      return n.loading ? (openBlock(), createElementBlock("div", lc, [
        createVNode(r, {
          class: "fs-icon-spin",
          icon: n.icon ?? unref(t).icons.refresh
        }, null, 8, ["icon"]),
        n.text ? (openBlock(), createElementBlock("span", uc, toDisplayString(n.text), 1)) : createCommentVNode("", true)
      ])) : createCommentVNode("", true);
    };
  }
});
var fc = defineComponent({
  name: "FsBox"
});
var hc = { class: "fs-box" };
function mc(e, t, n, o, r, a) {
  return openBlock(), createElementBlock("div", hc, [
    renderSlot(e.$slots, "default")
  ]);
}
var pc = ke(fc, [["render", mc]]);
function gc(e) {
  return typeof e == "function" || Object.prototype.toString.call(e) === "[object Object]" && !isVNode(e);
}
function ia(e, t) {
  if (e[t] instanceof Array) {
    const n = e[t];
    e[t] = (o) => {
      for (const r of n)
        r(o);
    };
  }
}
var vc = ["div", "span", "a", "p", "pre", "li", "ol", "ul"];
var bc = defineComponent({
  name: "FsComponentRender",
  inheritAttrs: false,
  props: {
    /**
     * modelValue
     */
    modelValue: {},
    /**
     * 组件名称
     */
    name: {},
    /**
     * 插槽
     */
    slots: {
      type: Object
    },
    /**
     * 子元素，同slots
     */
    children: {
      type: Object
    },
    /**
     * 事件监听
     */
    on: {
      type: Object
    },
    /**
     * 同 on
     */
    events: {
      type: Object
    },
    /**
     * 上下文scope
     */
    scope: {
      type: Object
    },
    /**
     * modelValue的属性名
     */
    vModel: {
      type: [String, Object]
    },
    /**
     * 组件参数，会与attrs合并
     */
    props: {},
    /**
     * 自定义render
     */
    render: {},
    /**
     * 当输入框的值上报为undefine时，转为null
     */
    undefineToNull: {
      type: Boolean,
      default: true
    }
  },
  emits: ["update:dict", "update:modelValue", "mounted"],
  setup(e, t) {
    vt.trace("fs-component-render");
    const {
      ui: n
    } = B(), {
      merge: o
    } = De();
    provide("get:scope", () => e.scope), onMounted(() => {
      t.emit("mounted", e.scope);
    });
    const r = ref(), a = computed(() => {
      const h2 = {
        name: "modelValue",
        trim: false,
        number: false,
        transform: void 0
      };
      e.vModel && (typeof e.vModel == "string" ? h2.name = e.vModel : o(h2, e.vModel));
      const v = e.modelValue ?? (n.type === "antdv" ? void 0 : null), g = "onUpdate:" + h2.name, w = {
        ref: r,
        // scope: props.scope,
        // fix element display false bug
        [h2.name]: v,
        [g]: (R) => {
          if (R && (h2.trim && (R = R.trim()), h2.number)) {
            const F = Number(R);
            isNaN(F) || (R = F);
          }
          h2.transform && (R = h2.transform(R)), R === void 0 && e.undefineToNull && (R = null), t.emit("update:modelValue", R);
        },
        ...e.props
      }, y = {
        ...e.events,
        ...e.on
      };
      return forEach_default(y, (R, F) => {
        const k = R;
        F.startsWith("on") || (F = camelCase_default("on_" + F)), w[F] = (A) => k({
          ...e.scope,
          $event: A
        });
      }), w;
    }), i2 = () => {
      const h2 = {}, v = (g, w) => {
        g instanceof Function ? h2[w] = (y) => g({
          ...e.scope,
          scope: y
        }) : h2[w] = () => g;
      };
      return forEach_default(e.children, v), forEach_default(e.slots, v), h2;
    }, s = computed(() => {
      const h2 = {
        isAsyncComponent: false,
        component: unref(e.name) || n.input.name
      };
      let v = h2.component;
      return vc.includes(v) || (typeof v == "string" && (v = resolveComponent(v)), (v == null ? void 0 : v.name) === "AsyncComponentWrapper" && (h2.isAsyncComponent = true)), h2.component = v, h2;
    }), u = i2;
    function l() {
      return s.value.isAsyncComponent ? d() : c();
    }
    function c() {
      return r.value;
    }
    async function d() {
      const h2 = c();
      return h2 ?? new Promise((v, g) => {
        f(v, g, 0);
      });
    }
    function f(h2, v, g) {
      setTimeout(() => {
        const w = c();
        if (w != null) {
          h2(w);
          return;
        }
        if (g++, g > 20) {
          v(new Error("异步组件加载超时"));
          return;
        }
        f(h2, v, g);
      }, 200);
    }
    return t.expose({
      props: e,
      getTargetRefSync: c,
      getTargetRef: l,
      getTargetRefAsync: d
    }), () => {
      let h2;
      const v = mergeProps(a.value, t.attrs);
      if (ia(v, "onChange"), ia(v, "onBlur"), e.render)
        return e.render({
          ...e.scope,
          attrs: v
        });
      const g = markRaw(s.value.component);
      return createVNode(g, v, gc(h2 = u()) ? h2 : {
        default: () => [h2]
      });
    };
  }
});
var yc = defineComponent({
  name: "FsSlotRender",
  inheritAttrs: false,
  props: {
    /**
     * 插槽
     */
    slots: {
      type: Function
    },
    /**
     * 上下文
     */
    scope: {
      type: Object
    }
  },
  setup(e) {
    return vt.trace("fs-slot-render"), () => e.slots(e.scope);
  }
});
var di = defineComponent({
  name: "FsRender",
  functional: true,
  props: {
    renderFunc: {
      type: Function
    },
    scope: {
      type: Object
    }
  },
  setup() {
    vt.trace("fs-render");
  },
  render() {
    return this.renderFunc(this.scope);
  }
});
var wc = defineComponent({
  name: "FsForm",
  props: {
    /**
     * 初始表单数据
     **/
    initialForm: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 字段模版
     * {
     *   key:{
     *     title: "字段名称",
     *     component:{
     *       name:"组件名称"
     *       ...组件参数
     *     }
     *   }
     * }
     * */
    columns: {
      type: Object,
      default: void 0
    },
    /**
     * 字段分组
     * {
     *   type:'xxx', //分组展示类型
     *   groups:{ //分组数据
     *     groupKey:{ title:'xxx',columns:['fieldKey','fieldKey']}
     *   }
     * }
     */
    group: {
      type: Object,
      default: void 0
    },
    /**
     * 重置表单后的操作
     */
    doReset: {
      type: Function,
      default: void 0
    },
    /**
     * 点击保存按钮，表单校验前执行操作（async）
     */
    beforeValidate: {
      type: Function,
      default: void 0
    },
    /**
     * 表单校验完完成后，提交前处理（async）
     */
    beforeSubmit: {
      type: Function,
      default: void 0
    },
    /**
     * 点击保存按钮时执行操作（async）
     */
    doSubmit: {
      type: Function,
      default: void 0
    },
    /**
     * 表单提交后处理（async）
     */
    afterSubmit: {
      type: Function,
      default: void 0
    },
    /**
     * 插槽内容
     */
    slots: {
      type: Object,
      default: () => ({})
    },
    /**
     * 布局方式【flex|grid】
     */
    display: {
      type: String,
      default: "flex"
      // flex
    },
    /**
     * 序号，编辑时会传入
     */
    index: {
      type: Number,
      default: void 0
    },
    /**
     * 模式 [add,edit,view,自定义]
     */
    mode: {
      type: String,
      default: void 0
    },
    /**
     * a-row配置
     */
    row: {
      type: Object,
      default: void 0
    },
    /**
     * el-col|a-col配置，可配置跨列
     */
    col: {
      type: Object,
      default: void 0
    },
    /**
     * formItem的公共配置
     */
    formItem: {
      type: Object,
      default: void 0
    },
    /**
     * helper位置：{position:'label'}
     */
    helper: {
      type: Object
    },
    watch: {
      type: Function,
      default: null
    }
  },
  emits: ["reset", "submit", "success", "validationError", "value-change", "init"],
  setup(e, t) {
    var p, _;
    const { merge: n } = De(), { ui: o } = B(), { AsyncComputeValue: r, doComputed: a } = Nt(), i2 = ref(), s = reactive({}), { proxy: u } = getCurrentInstance();
    vt.trace("fs-form"), forEach_default(e.columns, (C) => {
      C.value != null && (C.value instanceof r || C.value instanceof Go) && ue.warn("form.value配置不支持Compute/AsyncCompute类型的动态计算");
    });
    function l() {
      const C = {};
      return forEach_default(e.columns, (S, T) => {
        const ne = unref(S.value);
        ne !== void 0 && set_default(C, T, ne);
      }), n(C, cloneDeep_default(e.initialForm)), C;
    }
    const c = l();
    w(c);
    const d = computed(() => ({
      row: c,
      form: s,
      index: e.index,
      mode: e.mode || "add",
      attrs: t.attrs,
      getComponentRef: N
    }));
    function f() {
      return d.value;
    }
    const h2 = a(() => e.columns, f);
    function v(C) {
      C != null && forEach_default(e.columns, (S, T) => {
        let ne = get_default(C, T);
        S.valueBuilder && S.valueBuilder({
          value: ne,
          key: T,
          row: c,
          form: C,
          index: e.index,
          mode: e.mode
        });
      });
    }
    function g() {
      return s;
    }
    function w(C, S = {}) {
      if (v(C), S.mergeForm === false)
        for (const ne in s)
          delete s[ne];
      n(s, C);
      const { valueChange: T } = S;
      T && forEach_default(e.columns, (ne, P) => {
        const W = s[P];
        F(P, W);
      });
    }
    function y(...C) {
      return n({}, e.col, ...C);
    }
    function R(C) {
      return { key: C.key, ...d.value };
    }
    function F(C, S) {
      const T = { key: C, value: S, formRef: u, ...d.value, immediate: false };
      t.emit("value-change", T);
      let ne = e.columns[C].valueChange;
      ne && (ne instanceof Function ? ne(T) : ne.handle && ne.handle(T));
    }
    const k = ref({});
    function A(C) {
      return k.value[C];
    }
    function N(C, S = false) {
      var T;
      return (T = A(C)) == null ? void 0 : T.getComponentRef(S);
    }
    const $ = ref([]);
    forEach_default((p = e.group) == null ? void 0 : p.groups, (C, S) => {
      C.collapsed !== true && $.value.push(S);
    }), ((_ = e.group) == null ? void 0 : _.groupType) === "tabs" && ($.value = $.value.length > 0 ? $.value[0] : null);
    const B2 = a(
      () => e.group,
      f,
      null,
      (C) => {
        if (!C)
          return {};
        const S = {};
        forEach_default(C == null ? void 0 : C.groups, (P, W) => {
          forEach_default(P.columns, (oe) => {
            if (h2.value[oe] == null) {
              vt.logger.warn("无效的分组字段：" + oe);
              return;
            }
            S[oe] = W;
          });
        });
        const T = C.groupType;
        let ne = {
          parent: o.collapse.name,
          child: o.collapseItem.name
        };
        return T === "tabs" && (ne.parent = o.tabs.name, ne.child = o.tabPane.name), n(
          {
            wrapper: ne,
            groupedKeys: S
          },
          C
        );
      }
    ), D = computed(() => {
      const C = [];
      return forEach_default(h2.value, (S, T) => {
        var P, W;
        const ne = cloneDeep_default(e.formItem || {});
        S = n(ne, S), S.key = T, S.order == null && (S.order = nn.orderDefault), (((P = B2.value) == null ? void 0 : P.groupedKeys) == null || ((W = B2.value) == null ? void 0 : W.groupedKeys[T]) == null) && C.push(S), S.col = y(S.col);
      }), C.sort((S, T) => S.order - T.order), C;
    });
    function x() {
      return i2.value;
    }
    async function I() {
      const C = l(), S = toPairs_default(s);
      for (const T of S) {
        const ne = get_default(C, T[0]);
        ne == null ? unset_default(s, T[0]) : set_default(s, T[0], ne);
      }
      e.doReset && await e.doReset(d.value), t.emit("reset");
    }
    const V = ref(), O = ref({});
    function M(C) {
      var S, T;
      if ((S = B2.value) != null && S.groupedKeys)
        for (let ne in C) {
          const P = (T = B2.value) == null ? void 0 : T.groupedKeys[ne];
          P != null && (C["group." + P] = true);
        }
    }
    async function X() {
      const C = { ...d.value, form: s };
      if (C.mode !== "view") {
        if (e.beforeValidate && await e.beforeValidate(C) === false)
          return false;
        try {
          O.value = {}, await o.form.validateWrap(i2.value), V.value = true;
        } catch (S) {
          V.value = false;
          const T = o.form.transformValidateErrors(S);
          throw M(T), O.value = T, t.emit("validationError", d.value), S;
        }
      }
    }
    async function le() {
      await X();
      const C = cloneDeep_default(toRaw(s)), S = { ...d.value, form: C };
      if (ue.debug("form submit", JSON.stringify(s)), forEach_default(e.columns, (T, ne) => {
        let P = get_default(C, ne);
        T.valueResolve && T.valueResolve({
          value: P,
          key: ne,
          ...S
        });
      }), e.beforeSubmit && await e.beforeSubmit(S) === false)
        return false;
      if (forEach_default(e.columns, (T, ne) => {
        T.submit === false ? unset_default(C, ne) : T.submit === true && set_default(C, ne, C[ne]);
      }), e.doSubmit) {
        const T = await e.doSubmit(S);
        if (S.res = T, T === false)
          return false;
      }
      return t.emit("submit", S), e.afterSubmit && await e.afterSubmit(S) === false ? false : (t.emit("success", S), S);
    }
    onMounted(() => {
      forEach_default(h2.value, (C, S) => {
        if (C.valueChange == null)
          return;
        let T = C.valueChange;
        if (T && T.immediate === true && T.handle) {
          const ne = { key: S, value: s[S], formRef: u, ...d.value, immediate: true };
          T.handle && T.handle(ne);
        }
      });
    });
    function ye(C) {
      return !!(C && C.show !== false);
    }
    function me(C) {
      if (!C.columns || C.show === false)
        return false;
      for (let S of C.columns) {
        if (h2.value[S] == null)
          continue;
        if (ye(h2.value[S]))
          return true;
      }
      return false;
    }
    e.watch && watch(
      () => s,
      (C, S) => {
        e.watch && e.watch(d.value);
      },
      {
        deep: true,
        immediate: true
      }
    );
    function Se() {
      const C = JSON.stringify(c), S = JSON.stringify(s);
      return C !== S;
    }
    const G = computed(() => n({}, e.slots, t.slots));
    return t.emit("init", d.value), {
      get: (C, S) => get_default(C, S),
      set: (C, S, T) => {
        set_default(C, S, T), F(S, T);
      },
      ui: o,
      validRef: V,
      errorsRef: O,
      formRef: i2,
      computedColumns: h2,
      computedDefaultColumns: D,
      submit: le,
      reset: I,
      getFormRef: x,
      scope: d,
      buildItemScope: R,
      groupActiveKey: $,
      form: s,
      formItemRefs: k,
      getFormData: g,
      setFormData: w,
      getComponentRef: N,
      mergeCol: y,
      validate: X,
      computedGroup: B2,
      getContextFn: f,
      formItemShow: ye,
      groupItemShow: me,
      isDirty: Se,
      mergedSlots: G
    };
  }
});
function _c(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-form-item"), s = resolveComponent("fs-render");
  return openBlock(), createBlock(resolveDynamicComponent(e.ui.form.name), {
    ref: "formRef",
    class: normalizeClass(["fs-form", {
      "fs-form-grid": e.display === "grid",
      "fs-form-flex": e.display === "flex",
      "fs-form-invalid": e.validRef === false
    }]),
    onsubmit: "event.preventDefault();",
    model: e.form
  }, {
    default: withCtx(() => [
      (openBlock(), createBlock(resolveDynamicComponent(e.ui.row.name), mergeProps({ class: "fs-row" }, e.row), {
        default: withCtx(() => [
          (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedDefaultColumns, (u) => (openBlock(), createElementBlock(Fragment, {
            key: u == null ? void 0 : u.key
          }, [
            e.formItemShow(u) ? (openBlock(), createBlock(resolveDynamicComponent(e.ui.col.name), mergeProps({
              key: 0,
              class: "fs-col",
              ref_for: true
            }, u.col), {
              default: withCtx(() => [
                u.blank !== true ? (openBlock(), createBlock(i2, {
                  key: 0,
                  ref_for: true,
                  ref: (l) => {
                    l && (e.formItemRefs[u.key] = l);
                  },
                  item: u,
                  helper: e.helper,
                  "model-value": e.get(e.form, u.key),
                  "form-slot": e.mergedSlots["form_" + u.key],
                  "get-context-fn": e.getContextFn,
                  "onUpdate:modelValue": (l) => e.set(e.form, u.key, l)
                }, null, 8, ["item", "helper", "model-value", "form-slot", "get-context-fn", "onUpdate:modelValue"])) : createCommentVNode("", true)
              ]),
              _: 2
            }, 1040)) : createCommentVNode("", true)
          ], 64))), 128))
        ]),
        _: 1
      }, 16)),
      e.computedGroup.wrapper ? (openBlock(), createBlock(resolveDynamicComponent(e.computedGroup.wrapper.parent), mergeProps({
        key: 0,
        [e.ui.collapse.modelValue]: e.groupActiveKey,
        ["onUpdate:" + e.ui.collapse.modelValue]: t[0] || (t[0] = (u) => e.groupActiveKey = u),
        style: { width: "100%" }
      }, e.computedGroup), {
        default: withCtx(() => [
          (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedGroup.groups, (u, l) => (openBlock(), createElementBlock(Fragment, { key: l }, [
            e.groupItemShow(u) ? (openBlock(), createBlock(resolveDynamicComponent(e.computedGroup.wrapper.child), mergeProps({
              key: 0,
              [e.ui.collapse.keyName || ""]: l,
              ref_for: true
            }, u, {
              class: { "fs-form-group-error": e.errorsRef["group." + l] }
            }), createSlots({
              default: withCtx(() => [
                (openBlock(), createBlock(resolveDynamicComponent(e.ui.row.name), mergeProps({
                  class: "fs-row",
                  ref_for: true
                }, e.row), {
                  default: withCtx(() => [
                    (openBlock(true), createElementBlock(Fragment, null, renderList(u.columns, (c) => {
                      var d;
                      return openBlock(), createElementBlock(Fragment, { key: c }, [
                        e.formItemShow(e.computedColumns[c]) ? (openBlock(), createBlock(resolveDynamicComponent(e.ui.col.name), mergeProps({
                          key: 0,
                          class: "fs-col",
                          ref_for: true
                        }, e.mergeCol(u.col, (d = e.computedColumns[c]) == null ? void 0 : d.col)), {
                          default: withCtx(() => {
                            var f;
                            return [
                              e.computedColumns[c] && ((f = e.computedColumns[c]) == null ? void 0 : f.blank) !== true ? (openBlock(), createBlock(i2, {
                                key: 0,
                                ref_for: true,
                                ref: (h2) => {
                                  h2 && (e.formItemRefs[c] = h2);
                                },
                                item: e.computedColumns[c],
                                "model-value": e.get(e.form, c),
                                "form-slot": e.mergedSlots["form_" + c],
                                "get-context-fn": e.getContextFn,
                                "onUpdate:modelValue": (h2) => e.set(e.form, c, h2)
                              }, null, 8, ["item", "model-value", "form-slot", "get-context-fn", "onUpdate:modelValue"])) : createCommentVNode("", true)
                            ];
                          }),
                          _: 2
                        }, 1040)) : createCommentVNode("", true)
                      ], 64);
                    }), 128))
                  ]),
                  _: 2
                }, 1040))
              ]),
              _: 2
            }, [
              renderList(u.slots, (c, d) => ({
                name: d,
                fn: withCtx((f) => [
                  createVNode(s, {
                    "render-func": c,
                    scope: { ...f, hasError: e.errorsRef["group." + l] }
                  }, null, 8, ["render-func", "scope"])
                ])
              }))
            ]), 1040, ["class"])) : createCommentVNode("", true)
          ], 64))), 128))
        ]),
        _: 1
      }, 16)) : createCommentVNode("", true)
    ]),
    _: 1
  }, 8, ["class", "model"]);
}
var Cc = ke(wc, [["render", _c]]);
var Sc = defineComponent({
  name: "FsFormItem",
  components: { FsRender: di },
  props: {
    /**
     * 表单字段值(v-model)
     */
    modelValue: {},
    /**
     * 字段配置
     */
    item: {
      type: Object,
      default: void 0
    },
    /**
     * 字段组件插槽
     */
    formSlot: {
      type: Function,
      default: void 0
    },
    getContextFn: {
      type: Function,
      default: void 0
    },
    helper: {
      type: [String, Object]
    }
  },
  emits: ["update:modelValue"],
  setup(e, t) {
    const { ui: n } = B(), { merge: o } = De(), r = ref();
    vt.trace("fs-form-item");
    const a = () => {
      const v = e.getContextFn ? e.getContextFn() : {};
      return { value: e.modelValue, key: e.item.key, ...v };
    };
    function i2(v) {
      t.emit("update:modelValue", v);
    }
    function s(v = false) {
      var g, w;
      return v ? (g = r.value) == null ? void 0 : g.getTargetRefAsync() : (w = r.value) == null ? void 0 : w.getTargetRef();
    }
    const u = computed(() => {
      var v, g, w;
      return ((g = (v = e.item) == null ? void 0 : v.helper) == null ? void 0 : g.position) || ((w = e.helper) == null ? void 0 : w.position);
    }), l = computed(() => {
      var v, g;
      return o({}, (v = e.item.helper) == null ? void 0 : v.tooltip, (g = e.helper) == null ? void 0 : g.tooltip);
    }), c = computed(() => {
      if (e.item != null)
        return e.item.key.indexOf(".") >= 0 ? e.item.key.split(".") : e.item.key;
    }), d = computed(() => e.item.label || e.item.title), f = computed(() => d.value instanceof Function);
    return {
      ui: n,
      updateModelValue: i2,
      scopeFunc: a,
      getComponentRef: s,
      componentRenderRef: r,
      computedHelperPosition: u,
      computedHelperTooltip: l,
      computedKey: c,
      computedLabelIsRender: f,
      computedLabel: d,
      computedLabelRender: () => d.value(a())
    };
  }
});
var Fc = {
  key: 0,
  class: "fs-form-item-label-text"
};
var Rc = ["title"];
var Dc = { class: "fs-form-helper-tooltip" };
var kc = { class: "fs-form-item-label-icon" };
var $c = { class: "fs-form-item-content" };
var Oc = { class: "fs-form-item-render" };
var Ec = { class: "fs-form-item-component" };
function Tc(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-render"), s = resolveComponent("fs-form-helper"), u = resolveComponent("fs-icon"), l = resolveComponent("fs-slot-render"), c = resolveComponent("fs-component-render");
  return e.item ? (openBlock(), createBlock(resolveDynamicComponent(e.ui.formItem.name), mergeProps({
    key: 0,
    class: "fs-form-item",
    [e.ui.formItem.prop || ""]: e.computedKey
  }, e.item, {
    path: e.item.key,
    "rule-path": e.item.key,
    title: ""
  }), {
    label: withCtx(() => [
      e.computedLabelIsRender ? (openBlock(), createElementBlock("span", Fc, [
        createVNode(i2, { "render-func": e.computedLabelRender }, null, 8, ["render-func"])
      ])) : (openBlock(), createElementBlock("span", {
        key: 1,
        class: "fs-form-item-label-text",
        title: e.computedLabel
      }, toDisplayString(e.computedLabel), 9, Rc)),
      e.item.helper && e.computedHelperPosition === "label" ? (openBlock(), createBlock(resolveDynamicComponent(e.ui.tooltip.name), normalizeProps(mergeProps({ key: 2 }, e.computedHelperTooltip)), {
        [e.ui.tooltip.content]: withCtx(() => [
          createBaseVNode("span", Dc, [
            createVNode(s, {
              helper: e.item.helper,
              scope: e.scopeFunc()
            }, null, 8, ["helper", "scope"])
          ])
        ]),
        [e.ui.tooltip.trigger]: withCtx(() => [
          createBaseVNode("span", kc, [
            createVNode(u, {
              class: "fs-form-item-label-icon-inner",
              icon: e.ui.icons.question
            }, null, 8, ["icon"])
          ])
        ]),
        _: 2
      }, 1040)) : createCommentVNode("", true)
    ]),
    default: withCtx(() => {
      var d;
      return [
        createBaseVNode("div", $c, [
          e.item.topRender ? (openBlock(), createBlock(i2, {
            key: 0,
            "render-func": e.item.topRender,
            scope: e.scopeFunc()
          }, null, 8, ["render-func", "scope"])) : createCommentVNode("", true),
          createBaseVNode("div", Oc, [
            e.item.prefixRender ? (openBlock(), createBlock(i2, {
              key: 0,
              "render-func": e.item.prefixRender,
              scope: e.scopeFunc()
            }, null, 8, ["render-func", "scope"])) : createCommentVNode("", true),
            createBaseVNode("div", Ec, [
              e.formSlot ? (openBlock(), createBlock(l, {
                key: 0,
                slots: e.formSlot,
                scope: e.scopeFunc()
              }, null, 8, ["slots", "scope"])) : ((d = e.item.component) == null ? void 0 : d.show) !== false ? (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                e.item.conditionalRender && e.item.conditionalRender.match && e.item.conditionalRender.match(e.scopeFunc()) ? (openBlock(), createBlock(i2, {
                  key: 0,
                  "render-func": e.item.conditionalRender.render,
                  scope: e.scopeFunc()
                }, null, 8, ["render-func", "scope"])) : e.item.render ? (openBlock(), createBlock(i2, {
                  key: 1,
                  "render-func": e.item.render,
                  scope: e.scopeFunc()
                }, null, 8, ["render-func", "scope"])) : (openBlock(), createBlock(c, mergeProps({
                  key: 2,
                  ref: "componentRenderRef"
                }, e.item.component, {
                  "model-value": e.modelValue,
                  scope: e.scopeFunc(),
                  "onUpdate:modelValue": e.updateModelValue
                }), null, 16, ["model-value", "scope", "onUpdate:modelValue"]))
              ], 64)) : createCommentVNode("", true)
            ]),
            e.item.suffixRender ? (openBlock(), createBlock(i2, {
              key: 1,
              "render-func": e.item.suffixRender,
              scope: e.scopeFunc()
            }, null, 8, ["render-func", "scope"])) : createCommentVNode("", true)
          ]),
          e.item.bottomRender ? (openBlock(), createBlock(i2, {
            key: 1,
            "render-func": e.item.bottomRender,
            scope: e.scopeFunc()
          }, null, 8, ["render-func", "scope"])) : createCommentVNode("", true),
          e.item.helper && e.computedHelperPosition !== "label" ? (openBlock(), createBlock(s, {
            key: 2,
            helper: e.item.helper,
            scope: e.scopeFunc()
          }, null, 8, ["helper", "scope"])) : createCommentVNode("", true)
        ])
      ];
    }),
    _: 1
  }, 16, ["path", "rule-path"])) : createCommentVNode("", true);
}
var Ac = ke(Sc, [["render", Tc]]);
var Ic = defineComponent({
  name: "FsFormHelper",
  props: {
    /**
     * 帮助说明, text: 说明文字, render: function(scope) 自定义渲染
     */
    helper: {
      type: [
        /**
         * text: 说明文字,
         * render: function , 自定义render
         */
        Object,
        /**
         * 说明文本
         */
        String
      ]
    },
    scope: {}
  },
  setup(e) {
    vt.trace("fs-form-item");
  }
});
var Vc = { class: "fs-form-helper" };
var Pc = { key: 0 };
var Mc = { key: 2 };
function Bc(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-render");
  return openBlock(), createElementBlock("div", Vc, [
    typeof e.helper == "string" ? (openBlock(), createElementBlock("pre", Pc, toDisplayString(e.helper), 1)) : e.helper.render ? (openBlock(), createBlock(i2, {
      key: 1,
      "render-func": e.helper.render,
      scope: e.scope
    }, null, 8, ["render-func", "scope"])) : e.helper.text ? (openBlock(), createElementBlock("pre", Mc, toDisplayString(e.helper.text), 1)) : createCommentVNode("", true)
  ]);
}
var jc = ke(Ic, [["render", Bc]]);
function Nc(e) {
  return typeof e == "function" || Object.prototype.toString.call(e) === "[object Object]" && !isVNode(e);
}
var to = defineComponent({
  name: "FsFormWrapper",
  props: {
    /**
     * 表单配置
     * {
     *     wrapper:{ //表单包装配置
     *         is: 'el-dialog'//el-dialog|a-modal|el-drawer|a-drawer,
     *         draggable: false, //是否支持拖动
     *         inner:false //是否在页面内部打开
     *     }
     *     ...FsForm配置
     * }
     */
    options: {},
    /**
     * 插槽
     */
    slots: {},
    /**
     * 是否在内部打开对话框
     */
    inner: {},
    /**
     * 内部打开对话框的wrapper
     */
    innerWrapper: {},
    id: {},
    zIndex: {}
  },
  emits: ["reset", "submit", "validationError", "value-change", "open", "opened", "mounted", "closed", "inner-change"],
  setup(e, t) {
    var ne;
    vt.trace("fs-form-wrapper");
    const {
      t: n
    } = ot(), {
      merge: o
    } = De(), r = ref(false), a = ref(), i2 = ref(), s = ref(), u = ref(), l = ref(), c = ref(false), d = ref(), f = ref(), h2 = ref(), v = ref(), g = e.id || Math.floor(Math.random() * 1e6) + "", w = "fs-form-wrapper_" + g, y = ref({});
    function R() {
      var P;
      return {
        formWrapperId: g,
        formWrapperIdClass: w,
        close: $,
        doClose: B2,
        onClosed: D,
        onOpened: x,
        open: F,
        title: v,
        fullscreenEnabled: p,
        fullscreen: G,
        formWrapperIs: a,
        formWrapperOpen: r,
        formWrapperBind: s,
        computedButtons: Se,
        onValueChange: I,
        innerBind: S,
        formWrapperSlots: y,
        wrapper: s.value,
        options: i2.value,
        formRef: l.value,
        form: M(),
        wrapperBindRef: s,
        formOptionsRef: i2,
        setFormData: X,
        getFormData: M,
        reset: O,
        loading: c,
        toggleFullscreen: _,
        submit: V,
        mode: (P = i2.value) == null ? void 0 : P.mode
      };
    }
    const F = async (P) => {
      var Ce;
      const {
        wrapper: W
      } = P;
      W.onOpen && W.onOpen(P), v.value = unref(W.title), a.value = P.wrapper.is, u.value = W;
      const oe = C.formWrapper.customClass(a.value), Z = `fs-form-wrapper ${w} ${W[oe] || ""} `;
      return s.value = {
        ...omit_default(W, "title", "onOpen", "onClosed", "onOpened", "is", "inner", "beforeClose"),
        [oe]: Z
      }, i2.value = {
        ...omit_default(P, "wrapper", "slots"),
        slots: {
          ...e.slots,
          ...P.slots,
          ...t.slots
        }
      }, y.value = {
        ...e.slots,
        ...(Ce = P.wrapper) == null ? void 0 : Ce.slots,
        ...t.slots
      }, d.value = () => {
        W.onClosed && W.onClosed(R());
      }, f.value = () => {
        W.onOpened && W.onOpened(R());
      }, h2.value = W.beforeClose, W.fullscreen != null && (G.value = W.fullscreen), t.emit("inner-change", !!u.value.inner), await nextTick(), r.value = true, await nextTick(), x(), R();
    };
    async function k() {
      var oe;
      const P = u.value.saveRemind;
      if (((oe = l.value) == null ? void 0 : oe.isDirty()) && P) {
        let Z = false;
        if (P instanceof Function)
          Z = await P();
        else
          try {
            await C.messageBox.confirm({
              title: n("fs.form.saveRemind.title"),
              message: n("fs.form.saveRemind.content"),
              confirmButtonText: n("fs.form.saveRemind.ok"),
              cancelButtonText: n("fs.form.saveRemind.cancel")
            }), Z = true;
          } catch {
            Z = false;
          }
        Z && await V();
      }
    }
    async function A() {
      return h2.value && h2.value(R()) == false ? false : (await k(), true);
    }
    const N = computed(() => C.type == "element" ? {
      beforeClose: (P) => {
        A().then((W) => {
          W && P();
        });
      }
    } : {}), $ = async () => {
      r.value = false;
    }, B2 = async () => await A() == false ? false : ($(), true), D = () => {
      d.value && d.value(), t.emit("closed"), i2.value = null;
    }, x = () => {
      if (f.value && f.value(), s.value.draggable || s.value.dragenabled) {
        const {
          dragModal: P
        } = Cu();
        P({
          getModal: () => document.querySelector(`.${w}`)
        });
      }
    }, I = (P) => {
      t.emit("value-change", P);
    };
    async function V() {
      c.value = true;
      try {
        if (await l.value.submit() === false)
          return;
        $();
      } finally {
        c.value = false;
      }
    }
    async function O() {
      await l.value.reset();
    }
    function M() {
      var P;
      return (P = l.value) == null ? void 0 : P.getFormData();
    }
    function X(P, W) {
      var oe;
      (oe = l.value) == null || oe.setFormData(P, W);
    }
    const {
      doComputed: le
    } = Nt();
    function ye() {
      var P, W, oe;
      return {
        row: (P = i2.value) == null ? void 0 : P.initialForm,
        form: M(),
        index: (W = i2.value) == null ? void 0 : W.index,
        mode: (oe = i2.value) == null ? void 0 : oe.mode,
        attrs: i2.value,
        getComponentRef(...Z) {
          var Ce;
          (Ce = l.value) == null || Ce.getComponentRef(...Z);
        }
      };
    }
    const me = le(() => {
      var P;
      return (P = s.value) == null ? void 0 : P.buttons;
    }, ye), Se = computed(() => {
      const P = {
        cancel: {},
        reset: {},
        ok: {
          loading: c.value
        }
      }, W = o(P, me.value), oe = [];
      return forEach_default(W, (Z, Ce) => {
        Z.key = Ce, oe.push(Z), Z.onClick == null && Z.click != null && (Z.onClick = () => {
          Z.click(R());
        });
      }), sortBy_default(oe, (Z) => Z.order ?? nn.orderDefault);
    });
    onMounted(async () => {
      e.options != null && await F(e.options), t.emit("mounted", getCurrentInstance().exposed);
    });
    const G = ref(false), p = computed(() => {
      var P;
      return !((P = a.value) != null && P.endsWith("drawer"));
    });
    function _() {
      G.value = !G.value;
    }
    const C = i.get(), S = computed(() => u.value.inner ? C.formWrapper.buildInnerBind({
      getInnerWrapper() {
        if (u.value.innerContainerSelector) {
          const P = document.querySelector(u.value.innerContainerSelector);
          if (P)
            return P.classList.add("fs-form-inner-wrapper"), P;
          console.error(`找不到选择器为${u.value.innerContainerSelector}的元素`);
        }
        return e.innerWrapper;
      }
    }) : {});
    t.expose({
      formWrapperId: g,
      formWrapperIdClass: w,
      close: $,
      doClose: B2,
      onClosed: D,
      onOpened: x,
      open: F,
      title: v,
      fullscreenEnabled: p,
      fullscreen: G,
      toggleFullscreen: _,
      formOptions: i2,
      formWrapperIs: a,
      formWrapperOpen: r,
      formWrapperBind: s,
      formRef: l,
      submit: V,
      reset: O,
      computedButtons: Se,
      loading: c,
      getFormData: M,
      setFormData: X,
      onValueChange: I,
      innerBind: S,
      formWrapperSlots: y,
      form: M(),
      wrapperBindRef: s,
      formOptionsRef: i2,
      mode: (ne = i2.value) == null ? void 0 : ne.mode
    });
    const T = useSlots();
    return () => {
      if (!s.value)
        return null;
      const P = i.get();
      let W = {};
      const oe = {
        ...T,
        ...y.value
      }, Z = (Et, sn, Je = oe) => Je[Et] ? Je[Et](sn) : null, Ce = a.value || "el-dialog";
      if (i2.value) {
        const {
          index: Et,
          mode: sn
        } = i2.value || {}, Je = {
          _self: this,
          index: Et,
          mode: sn,
          getFormData: M
        };
        W = {
          [P.formWrapper.titleSlotName]: () => {
            let Bn = null;
            return p.value && (Bn = createVNode(resolveComponent("fs-icon"), {
              class: "fs-fullscreen-icon",
              onClick: _,
              icon: G.value ? P.icons.fullScreen : P.icons.unFullScreen
            }, null)), createVNode("div", {
              class: "fs-form-header"
            }, [createVNode("div", {
              class: "fs-form-header-left"
            }, [Z("form-header-left", Je), v.value, Z("form-header-right", Je)]), createVNode("div", {
              class: "fs-form-header-action"
            }, [Z("form-header-action-left", Je), Bn, Z("form-header-action-right", Je)])]);
          },
          default: () => {
            const Bn = [];
            return forEach_default(Se.value, (Qr) => {
              Qr.show !== false && Bn.push(createVNode(resolveComponent("fs-button"), Qr, null));
            }), createVNode("div", {
              class: "fs-form-wrapper-body"
            }, [createVNode("div", {
              class: "fs-form-body"
            }, [Z("form-body-top", Je), createVNode("div", {
              class: "fs-form-content"
            }, [Z("form-body-left", Je), createVNode(resolveComponent("fs-form"), mergeProps({
              ref: l
            }, i2.value, {
              onValueChange: I
            }), null), Z("form-body-right", Je)]), Z("form-body-bottom", Je)]), createVNode("div", {
              class: "fs-form-footer-btns"
            }, [Z("form-footer-left", Je), Bn, Z("form-footer-right", Je)])]);
          }
        };
      }
      if (P.formWrapper.hasContentWrap) {
        const Et = P.formWrapper.hasContentWrap(Ce), sn = W;
        if (Et) {
          const Je = resolveDynamicComponent(Et);
          W = {
            default: () => createVNode(Je, null, Nc(sn) ? sn : {
              default: () => [sn]
            })
          };
        }
      }
      const ze = P.formWrapper.visible, rn = {
        [ze]: r.value,
        ["onUpdate:" + ze]: async (Et) => {
          if (Et === false && r.value)
            return await B2();
          r.value = Et;
        }
      }, yn = P.formWrapper.buildOnClosedBind(Ce, D), an = P.formWrapper.customClass(Ce), ji = `${G.value ? "fs-fullscreen" : ""} ${s.value[an] || ""}`, Ni = {
        [an]: ji
      }, Li = {
        fullscreen: G.value
      }, qi = {
        zIndex: s.value.zIndex || e.zIndex
      }, xi = resolveDynamicComponent(Ce);
      return createVNode(xi, mergeProps(s.value, Ni, rn, yn, Li, S.value, qi, N.value), W);
    };
  }
});
var Lc = defineComponent({
  name: "FsFormProvider"
});
var qc = defineComponent({
  ...Lc,
  setup(e) {
    const t = ref({});
    return provide("use:form:wrapper", () => ({
      open(n) {
        const o = n.id || Math.floor(Math.random() * 1e6) + "";
        return new Promise((r, a) => {
          t.value[o] = {
            id: o,
            // zIndex: getMaxZIndex() + 1,
            async onMounted(i2) {
              await i2.open(n), r(i2);
            },
            onClosed() {
              n.id || delete t.value[o];
            }
          };
        });
      }
    })), (n, o) => (openBlock(), createElementBlock(Fragment, null, [
      renderSlot(n.$slots, "default"),
      (openBlock(true), createElementBlock(Fragment, null, renderList(t.value, (r) => (openBlock(), createBlock(unref(to), mergeProps({
        key: r.id,
        ref_for: true
      }, r), null, 16))), 128))
    ], 64));
  }
});
vt.trace("fs-table");
function xc(e, t) {
  provide("get:columns", () => e.table.columns), provide("update:columns", (n) => {
    t.emit("update:columns", n);
  }), provide("get:crudBinding", () => e);
}
function Hc(e, t) {
  const n = ref(), o = () => n.value, r = () => n.value ? n.value.getForm() : (ue.warn("请使用expose.getSearchFormData代替"), {}), a = () => n.value ? n.value.getValidatedForm() : (ue.warn("请使用expose.getSearchValidatedFormData代替"), {});
  function i2({ form: s, mergeForm: u = false }) {
    n.value && n.value.setForm(s, u);
  }
  return {
    searchRef: n,
    getSearchRef: o,
    getSearchFormData: r,
    setSearchFormData: i2,
    getSearchValidatedFormData: a
  };
}
function zc(e, t, n) {
  return {
    tabsBinding: computed(() => {
      var r, a, i2, s, u, l, c;
      if (t.tabs && t.tabs.show && t.tabs.name) {
        let d = null;
        const f = { ...t.tabs };
        return (r = t.search) != null && r.columns && ((s = (i2 = (a = t.search) == null ? void 0 : a.columns[t.tabs.name]) == null ? void 0 : i2.component) != null && s.dict) && (d = (c = (l = (u = t.search) == null ? void 0 : u.columns[t.tabs.name]) == null ? void 0 : l.component) == null ? void 0 : c.dict, f.value == null && (f.value = d.value), f.label == null && (f.label = d.label), f.options == null && (f.options = d.data || [])), {
          ...f,
          modelValue: t.search.validatedForm && t.search.validatedForm[t.tabs.name],
          "onUpdate:modelValue": (h2) => {
            n.emit("tab-change", { [t.tabs.name]: h2 });
          }
        };
      }
      return {
        show: false
      };
    })
  };
}
function go(e, t) {
  if (!e)
    return {};
  const n = {};
  return forEach_default(e, (o, r) => {
    r.startsWith(t) && (n[r] = o);
  }), n;
}
function Wc(e, t, { tableRef: n, containerRef: o }) {
  var l;
  const r = i.get();
  let a = r.table;
  if (((l = e.table) == null ? void 0 : l.tableVersion) === "v2" && (a = r.tableV2), a.hasMaxHeight(e.table))
    return {};
  if (!a.fixedHeaderNeedComputeBodyHeight)
    return {};
  const i2 = ref(null);
  function s() {
    var v;
    const c = (v = n == null ? void 0 : n.value) == null ? void 0 : v.$el;
    if (c == null || c.querySelector == null)
      return;
    const d = c.querySelector(a.headerDomSelector);
    if (d == null)
      return;
    const f = c.getBoundingClientRect().height, h2 = d.getBoundingClientRect().height;
    i2.value = f - h2 - 2 + (e.table.maxHeightAdjust || 0), ue.debug("table max height recomputed ", i2.value);
  }
  function u() {
    const c = n.value.$el;
    if (c == null)
      return;
    const d = c.parentNode;
    new ResizeObserver(function(h2) {
      ue.debug("table resized", h2), h2.length > 0 && h2[0].contentRect.height > 0 && (s(), setTimeout(() => {
        s();
      }, 200), setTimeout(() => {
        s();
      }, 500));
    }).observe(d);
  }
  return onMounted(async () => {
    await nextTick(), await nextTick(), u();
  }), { maxHeightRef: i2, computeBodyHeight: s };
}
function Uc(e, t) {
  const n = i.get(), o = ref(), r = ref(), a = ref(), { maxHeightRef: i2, computeBodyHeight: s } = Wc(e, t, { tableRef: o, containerRef: a }), { merge: u } = De(), l = toRef(e, "table"), c = computed(() => {
    var $;
    let A = {};
    if ((i2 == null ? void 0 : i2.value) != null) {
      let B2 = n.table;
      (($ = e.table) == null ? void 0 : $.tableVersion) === "v2" && (B2 = n.tableV2), A = B2.buildMaxHeight(i2.value);
    }
    const N = vt.dash.omit(l, "loading", "columns", "columnsMap");
    return u(A, { ...t.attrs, ...N });
  }), d = toRef(e, "toolbar"), f = computed(() => go(t.slots, "cell")), h2 = computed(() => go(t.slots, "form")), v = computed(() => go(t.slots, "search")), g = computed(() => go(t.slots, "toolbar")), w = ref(), y = computed(() => {
    const A = { compact: e.toolbar.compact !== false };
    return e.customClass && (A[e.customClass] = true), A;
  }), R = ref(), F = ref(false);
  return {
    tableRef: o,
    containerRef: a,
    toolbarRef: r,
    computedTable: c,
    computedToolbar: d,
    computedCellSlots: f,
    formWrapperRef: w,
    isFormInner: F,
    onFormInnerChange: (A) => {
      F.value = A;
    },
    computedFormSlots: h2,
    computedSearchSlots: v,
    computedToolbarSlots: g,
    computeBodyHeight: s,
    computedClass: y,
    innerWrapperRef: R
  };
}
var Kc = defineComponent({
  name: "FsCrud",
  inheritAttrs: false,
  props: {
    /**
     * 表格id
     */
    id: {
      type: String,
      default: ""
    },
    /**
     * 表格配置，见FsTable
     */
    table: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 表格数据
     */
    // eslint-disable-next-line vue/require-default-prop
    data: {
      type: Array
    },
    /**
     * 操作列配置，见FsRowHandle
     */
    rowHandle: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 查询框配置，见FsSearch
     */
    search: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 工具条配置，见FsToolbar
     */
    toolbar: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 动作条配置，见FsActionbar
     */
    actionbar: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * tabs filter
     */
    tabs: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 添加表单对话框配置，见FsFormWrapper
     */
    addForm: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 编辑表单对话框配置，见FsFormWrapper
     */
    editForm: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 查看表单对话框配置，见FsFormWrapper
     */
    viewForm: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 翻页配置,支持el-pagination|a-pagination配置
     */
    pagination: {
      type: Object,
      default() {
        return { show: false };
      }
    },
    /**
     * 容器配置，见FsContainer
     */
    container: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * crud包裹容器的class
     */
    customClass: {},
    /**
     * 不要传到fs-table去
     */
    form: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 可选择
     */
    selection: {
      type: Object,
      default() {
        return { show: false };
      }
    }
  },
  emits: ["update:search", "update:compact", "update:columns", "form-value-change", "update:modelValue", "tab-change"],
  setup(e, t) {
    const { ui: n } = B();
    xc(e, t);
    const o = Hc(), r = zc(o, e, t), a = Uc(e, t);
    return {
      ui: n,
      ...o,
      ...a,
      ...r
    };
  }
});
var Yc = { class: "fs-crud-search" };
var Gc = {
  key: 0,
  class: "fs-crud-actionbar"
};
var Xc = {
  key: 0,
  class: "fs-crud-toolbar"
};
var Qc = { class: "fs-crud-pagination" };
var Jc = { class: "fs-pagination-left" };
var Zc = { class: "fs-pagination" };
var ed = { class: "fs-pagination-right" };
function td(e, t, n, o, r, a) {
  var d;
  const i2 = resolveComponent("fs-actionbar"), s = resolveComponent("fs-toolbar"), u = resolveComponent("fs-tabs-filter"), l = resolveComponent("fs-table"), c = resolveComponent("fs-form-wrapper");
  return openBlock(), createBlock(resolveDynamicComponent(((d = e.container) == null ? void 0 : d.is) || "fs-layout-default"), mergeProps({
    ref: "containerRef",
    class: "fs-crud-container"
  }, e.container, { class: e.computedClass }), {
    "header-top": withCtx(() => [
      renderSlot(e.$slots, "header-top")
    ]),
    "header-bottom": withCtx(() => [
      renderSlot(e.$slots, "header-bottom")
    ]),
    "header-middle": withCtx(() => [
      renderSlot(e.$slots, "header-middle")
    ]),
    search: withCtx(() => [
      createBaseVNode("div", Yc, [
        (openBlock(), createBlock(resolveDynamicComponent(e.search.is || "fs-search"), mergeProps({ ref: "searchRef" }, e.search, { slots: e.computedSearchSlots }), null, 16, ["slots"]))
      ])
    ]),
    actionbar: withCtx(() => [
      e.actionbar && e.actionbar.show !== false ? (openBlock(), createElementBlock("div", Gc, [
        renderSlot(e.$slots, "actionbar-left"),
        createVNode(i2, normalizeProps(guardReactiveProps(e.actionbar)), null, 16),
        renderSlot(e.$slots, "actionbar-right")
      ])) : createCommentVNode("", true)
    ]),
    toolbar: withCtx(() => [
      e.toolbar && e.toolbar.show !== false ? (openBlock(), createElementBlock("div", Xc, [
        renderSlot(e.$slots, "toolbar-left"),
        createVNode(s, mergeProps({ ref: "toolbarRef" }, e.toolbar, {
          slots: e.computedToolbarSlots,
          columns: e.table.columns
        }), null, 16, ["slots", "columns"]),
        renderSlot(e.$slots, "toolbar-right")
      ])) : createCommentVNode("", true)
    ]),
    tabs: withCtx(() => [
      e.tabsBinding.show ? (openBlock(), createBlock(u, mergeProps({
        key: 0,
        ref: "tabsRef",
        class: "fs-tabs"
      }, e.tabsBinding), null, 16)) : createCommentVNode("", true)
    ]),
    table: withCtx(() => [
      createVNode(l, mergeProps({
        ref: "tableRef",
        class: "fs-crud-table"
      }, e.computedTable, {
        columns: e.table.columns,
        loading: e.table.loading,
        "row-handle": e.rowHandle,
        data: e.data,
        "cell-slots": e.computedCellSlots
      }), null, 16, ["columns", "loading", "row-handle", "data", "cell-slots"])
    ]),
    form: withCtx(() => [
      createBaseVNode("div", {
        ref: "innerWrapperRef",
        class: normalizeClass(["fs-form-wrapper-container", { "fs-form-inner-wrapper": e.isFormInner }])
      }, [
        createVNode(c, {
          ref: "formWrapperRef",
          slots: e.computedFormSlots,
          "inner-wrapper": e.innerWrapperRef,
          onInnerChange: e.onFormInnerChange,
          onValueChange: t[0] || (t[0] = (f) => e.$emit("form-value-change", f))
        }, null, 8, ["slots", "inner-wrapper", "onInnerChange"])
      ], 2)
    ]),
    pagination: withCtx(() => [
      createBaseVNode("div", Qc, [
        createBaseVNode("div", Jc, [
          renderSlot(e.$slots, "pagination-left")
        ]),
        createBaseVNode("div", Zc, [
          e.pagination.show !== false ? (openBlock(), createBlock(resolveDynamicComponent(e.ui.pagination.name), normalizeProps(mergeProps({ key: 0 }, e.pagination)), null, 16)) : createCommentVNode("", true)
        ]),
        createBaseVNode("div", ed, [
          renderSlot(e.$slots, "pagination-right")
        ])
      ])
    ]),
    "footer-top": withCtx(() => [
      renderSlot(e.$slots, "footer-top")
    ]),
    "footer-bottom": withCtx(() => [
      renderSlot(e.$slots, "footer-bottom")
    ]),
    default: withCtx(() => [
      renderSlot(e.$slots, "default")
    ]),
    _: 3
  }, 16, ["class"]);
}
var nd = ke(Kc, [["render", td]]);
var od = defineComponent({
  name: "FsRowHandle",
  props: {
    /**
     * 按钮折叠配置
     */
    dropdown: {
      type: Object
    },
    /**
     * 按钮配置
     * {
     *   view:{...FsButton,click:Function,order:1},
     *   edit:{...FsButton,click:Function,order:2},
     *   remove:{...FsButton,click:Function,order:3},
     *   ...自定义
     * }
     */
    buttons: {
      type: Object
    },
    /**
     * 按钮分组,上面的buttons为默认分组
     *  {
     *    groupKey:{buttonKey:{},buttonKey2:{}}
     *  }
     */
    group: {
      type: Object
    },
    /**
     * 当前激活分组
     */
    active: {
      type: String,
      default: "default"
    },
    /**
     * scope
     */
    scope: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["handle"],
  setup(e, t) {
    const { ui: n } = B(), { merge: o } = De(), { t: r } = ot(), a = computed(() => ({
      ...e.scope,
      row: e.scope[n.tableColumn.row],
      index: e.scope[n.tableColumn.index]
    })), i2 = (w) => {
      const y = e.scope[n.tableColumn.index], R = e.scope[n.tableColumn.row], F = { key: w.key, row: R, btn: w, index: y, ...e.scope };
      if (w.click)
        return w.click(F);
      t.emit("handle", F);
    }, { doComputed: s } = Nt(), u = computed(() => ({
      dropdown: e.dropdown,
      buttons: e.buttons,
      active: e.active,
      group: e.group
    })), l = s(
      () => u.value,
      () => {
        const w = e.scope[n.tableColumn.index], y = e.scope[n.tableColumn.row];
        return { ...e.scope, index: w, row: y };
      }
    ), c = computed(() => {
      let w = null;
      if (l.value.active == null || l.value.active === "default") {
        const R = {
          view: {
            key: "view",
            text: r("fs.rowHandle.view.text"),
            title: r("fs.rowHandle.view.text")
          },
          copy: {
            key: "copy",
            text: r("fs.rowHandle.copy.text"),
            title: r("fs.rowHandle.copy.text")
          },
          edit: {
            key: "edit",
            type: "primary",
            text: r("fs.rowHandle.edit.text"),
            title: r("fs.rowHandle.edit.text")
          },
          remove: {
            key: "remove",
            ...n.button.colors("danger"),
            text: r("fs.rowHandle.remove.text"),
            title: r("fs.rowHandle.remove.text")
          }
        };
        w = o(R, l.value.buttons);
      } else
        w = l.value.group[l.value.active];
      const y = [];
      return forEach_default(w, (R, F) => {
        R.key = F, R.show !== false && y.push(R);
      }), sortBy_default(y, (R) => R.order ?? nn.orderDefault);
    }), d = computed(() => l.value.dropdown == null || l.value.dropdown.atLeast == null || l.value.dropdown.atLeast <= 0 || c.value.length <= l.value.dropdown.atLeast ? 0 : l.value.dropdown.atLeast || 0);
    function f(w, y) {
      return w.dropdown === true || d.value > 0 && d.value < y;
    }
    const h2 = computed(() => {
      let w = 0;
      for (const y of c.value) {
        if (f(y, w))
          return true;
        w++;
      }
      return false;
    });
    function v(w) {
      for (let y of c.value)
        if (w === y.key) {
          i2(y);
          return;
        }
    }
    const g = computed(() => {
      const w = {};
      if (n.dropdown.renderMode !== "slot") {
        const y = c.value, R = [];
        forEach_default(y, (F, k) => {
          F.show !== false && f(F, k) && R.push({
            [n.dropdown.value]: F.key,
            [n.dropdown.label]: F.text,
            title: F.title
          });
        }), w.options = R;
      }
      return {
        ...omit_default(e.dropdown, "more", "atLeast"),
        ...n.dropdown.command(v),
        ...w
      };
    });
    return {
      ui: n,
      hasDropdownBtn: h2,
      computedHandleBtns: c,
      doDropdownItemClick: v,
      computedDropdownAtLeast: d,
      doClick: i2,
      isDropdownBtn: f,
      scopeRef: a,
      computedDropdownBinding: g
    };
  }
});
var rd = { class: "fs-row-handle" };
var ad = {
  key: 0,
  class: "row-handle-btn fs-handle-row-dropdown"
};
function id(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-render"), s = resolveComponent("fs-button"), u = resolveComponent("fs-icon");
  return openBlock(), createElementBlock("div", rd, [
    renderSlot(e.$slots, "cell-rowHandle-left", normalizeProps(guardReactiveProps(e.scopeRef))),
    (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedHandleBtns, (l, c) => (openBlock(), createElementBlock(Fragment, { key: c }, [
      l.show !== false && !e.isDropdownBtn(l, c) ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        l.render ? (openBlock(), createBlock(i2, {
          key: 0,
          "render-func": l.render,
          scope: e.scopeRef
        }, null, 8, ["render-func", "scope"])) : (openBlock(), createBlock(s, mergeProps({
          key: 1,
          class: "row-handle-btn",
          ref_for: true
        }, l, {
          onClick: withModifiers((d) => e.doClick(l), ["stop"])
        }), null, 16, ["onClick"]))
      ], 64)) : createCommentVNode("", true)
    ], 64))), 128)),
    renderSlot(e.$slots, "cell-rowHandle-middle", normalizeProps(guardReactiveProps(e.scope))),
    e.hasDropdownBtn ? (openBlock(), createElementBlock("span", ad, [
      (openBlock(), createBlock(resolveDynamicComponent(e.ui.dropdown.name), normalizeProps(guardReactiveProps(e.computedDropdownBinding)), {
        [e.ui.dropdown.slotName]: withCtx(() => [
          e.ui.dropdown.renderMode === "slot" ? (openBlock(), createBlock(resolveDynamicComponent(e.ui.dropdownMenu.name), normalizeProps(mergeProps({ key: 0 }, e.ui.dropdownMenu.command(e.doDropdownItemClick))), {
            default: withCtx(() => [
              (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedHandleBtns, (l, c) => (openBlock(), createElementBlock(Fragment, { key: c }, [
                l.show !== false && e.isDropdownBtn(l, c) ? (openBlock(), createBlock(resolveDynamicComponent(e.ui.dropdownItem.name), mergeProps({
                  key: 0,
                  [e.ui.dropdownItem.command || ""]: l.key,
                  ref_for: true
                }, l.dropdownItem, {
                  disabled: l.disabled
                }), {
                  default: withCtx(() => [
                    createBaseVNode("div", mergeProps({
                      class: "fs-row-handle-dropdown-item",
                      ref_for: true
                    }, l), [
                      l.icon ? (openBlock(), createBlock(u, {
                        key: 0,
                        icon: l.icon
                      }, null, 8, ["icon"])) : createCommentVNode("", true),
                      createTextVNode(" " + toDisplayString(l.text || l.title), 1)
                    ], 16)
                  ]),
                  _: 2
                }, 1040, ["disabled"])) : createCommentVNode("", true)
              ], 64))), 128))
            ]),
            _: 1
          }, 16)) : createCommentVNode("", true)
        ]),
        default: withCtx(() => [
          createVNode(s, normalizeProps(guardReactiveProps(e.dropdown.more)), null, 16)
        ]),
        _: 2
      }, 1040))
    ])) : createCommentVNode("", true),
    renderSlot(e.$slots, "cell-rowHandle-right", normalizeProps(guardReactiveProps(e.scope)))
  ]);
}
var sd = ke(od, [["render", id]]);
function fn() {
  return fn = Object.assign ? Object.assign.bind() : function(e) {
    for (var t = 1; t < arguments.length; t++) {
      var n = arguments[t];
      for (var o in n)
        Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
    }
    return e;
  }, fn.apply(this, arguments);
}
function ld(e, t) {
  e.prototype = Object.create(t.prototype), e.prototype.constructor = e, no(e, t);
}
function Dr(e) {
  return Dr = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(n) {
    return n.__proto__ || Object.getPrototypeOf(n);
  }, Dr(e);
}
function no(e, t) {
  return no = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(o, r) {
    return o.__proto__ = r, o;
  }, no(e, t);
}
function ud() {
  if (typeof Reflect > "u" || !Reflect.construct || Reflect.construct.sham)
    return false;
  if (typeof Proxy == "function")
    return true;
  try {
    return Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    })), true;
  } catch {
    return false;
  }
}
function Do(e, t, n) {
  return ud() ? Do = Reflect.construct.bind() : Do = function(r, a, i2) {
    var s = [null];
    s.push.apply(s, a);
    var u = Function.bind.apply(r, s), l = new u();
    return i2 && no(l, i2.prototype), l;
  }, Do.apply(null, arguments);
}
function cd(e) {
  return Function.toString.call(e).indexOf("[native code]") !== -1;
}
function kr(e) {
  var t = typeof Map == "function" ? /* @__PURE__ */ new Map() : void 0;
  return kr = function(o) {
    if (o === null || !cd(o))
      return o;
    if (typeof o != "function")
      throw new TypeError("Super expression must either be null or a function");
    if (typeof t < "u") {
      if (t.has(o))
        return t.get(o);
      t.set(o, r);
    }
    function r() {
      return Do(o, arguments, Dr(this).constructor);
    }
    return r.prototype = Object.create(o.prototype, {
      constructor: {
        value: r,
        enumerable: false,
        writable: true,
        configurable: true
      }
    }), no(r, o);
  }, kr(e);
}
var dd = /%[sdj%]/g;
var fi = function() {
};
typeof process < "u" && process.env && true && typeof window < "u" && typeof document < "u" && (fi = function(t, n) {
  typeof console < "u" && console.warn && typeof ASYNC_VALIDATOR_NO_WARNING > "u" && n.every(function(o) {
    return typeof o == "string";
  }) && console.warn(t, n);
});
function $r(e) {
  if (!e || !e.length)
    return null;
  var t = {};
  return e.forEach(function(n) {
    var o = n.field;
    t[o] = t[o] || [], t[o].push(n);
  }), t;
}
function pt(e) {
  for (var t = arguments.length, n = new Array(t > 1 ? t - 1 : 0), o = 1; o < t; o++)
    n[o - 1] = arguments[o];
  var r = 0, a = n.length;
  if (typeof e == "function")
    return e.apply(null, n);
  if (typeof e == "string") {
    var i2 = e.replace(dd, function(s) {
      if (s === "%%")
        return "%";
      if (r >= a)
        return s;
      switch (s) {
        case "%s":
          return String(n[r++]);
        case "%d":
          return Number(n[r++]);
        case "%j":
          try {
            return JSON.stringify(n[r++]);
          } catch {
            return "[Circular]";
          }
          break;
        default:
          return s;
      }
    });
    return i2;
  }
  return e;
}
function fd(e) {
  return e === "string" || e === "url" || e === "hex" || e === "email" || e === "date" || e === "pattern";
}
function He(e, t) {
  return !!(e == null || t === "array" && Array.isArray(e) && !e.length || fd(t) && typeof e == "string" && !e);
}
function hd(e, t, n) {
  var o = [], r = 0, a = e.length;
  function i2(s) {
    o.push.apply(o, s || []), r++, r === a && n(o);
  }
  e.forEach(function(s) {
    t(s, i2);
  });
}
function sa(e, t, n) {
  var o = 0, r = e.length;
  function a(i2) {
    if (i2 && i2.length) {
      n(i2);
      return;
    }
    var s = o;
    o = o + 1, s < r ? t(e[s], a) : n([]);
  }
  a([]);
}
function md(e) {
  var t = [];
  return Object.keys(e).forEach(function(n) {
    t.push.apply(t, e[n] || []);
  }), t;
}
var la = function(e) {
  ld(t, e);
  function t(n, o) {
    var r;
    return r = e.call(this, "Async Validation Error") || this, r.errors = n, r.fields = o, r;
  }
  return t;
}(kr(Error));
function pd(e, t, n, o, r) {
  if (t.first) {
    var a = new Promise(function(f, h2) {
      var v = function(y) {
        return o(y), y.length ? h2(new la(y, $r(y))) : f(r);
      }, g = md(e);
      sa(g, n, v);
    });
    return a.catch(function(f) {
      return f;
    }), a;
  }
  var i2 = t.firstFields === true ? Object.keys(e) : t.firstFields || [], s = Object.keys(e), u = s.length, l = 0, c = [], d = new Promise(function(f, h2) {
    var v = function(w) {
      if (c.push.apply(c, w), l++, l === u)
        return o(c), c.length ? h2(new la(c, $r(c))) : f(r);
    };
    s.length || (o(c), f(r)), s.forEach(function(g) {
      var w = e[g];
      i2.indexOf(g) !== -1 ? sa(w, n, v) : hd(w, n, v);
    });
  });
  return d.catch(function(f) {
    return f;
  }), d;
}
function gd(e) {
  return !!(e && e.message !== void 0);
}
function vd(e, t) {
  for (var n = e, o = 0; o < t.length; o++) {
    if (n == null)
      return n;
    n = n[t[o]];
  }
  return n;
}
function ua(e, t) {
  return function(n) {
    var o;
    return e.fullFields ? o = vd(t, e.fullFields) : o = t[n.field || e.fullField], gd(n) ? (n.field = n.field || e.fullField, n.fieldValue = o, n) : {
      message: typeof n == "function" ? n() : n,
      fieldValue: o,
      field: n.field || e.fullField
    };
  };
}
function ca(e, t) {
  if (t) {
    for (var n in t)
      if (t.hasOwnProperty(n)) {
        var o = t[n];
        typeof o == "object" && typeof e[n] == "object" ? e[n] = fn({}, e[n], o) : e[n] = o;
      }
  }
  return e;
}
var hi = function(t, n, o, r, a, i2) {
  t.required && (!o.hasOwnProperty(t.field) || He(n, i2 || t.type)) && r.push(pt(a.messages.required, t.fullField));
};
var bd = function(t, n, o, r, a) {
  (/^\s+$/.test(n) || n === "") && r.push(pt(a.messages.whitespace, t.fullField));
};
var vo;
var yd = function() {
  if (vo)
    return vo;
  var e = "[a-fA-F\\d:]", t = function(k) {
    return k && k.includeBoundaries ? "(?:(?<=\\s|^)(?=" + e + ")|(?<=" + e + ")(?=\\s|$))" : "";
  }, n = "(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}", o = "[a-fA-F\\d]{1,4}", r = (`
(?:
(?:` + o + ":){7}(?:" + o + `|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8
(?:` + o + ":){6}(?:" + n + "|:" + o + `|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4
(?:` + o + ":){5}(?::" + n + "|(?::" + o + `){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4
(?:` + o + ":){4}(?:(?::" + o + "){0,1}:" + n + "|(?::" + o + `){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4
(?:` + o + ":){3}(?:(?::" + o + "){0,2}:" + n + "|(?::" + o + `){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4
(?:` + o + ":){2}(?:(?::" + o + "){0,3}:" + n + "|(?::" + o + `){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4
(?:` + o + ":){1}(?:(?::" + o + "){0,4}:" + n + "|(?::" + o + `){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4
(?::(?:(?::` + o + "){0,5}:" + n + "|(?::" + o + `){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4
)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1
`).replace(/\s*\/\/.*$/gm, "").replace(/\n/g, "").trim(), a = new RegExp("(?:^" + n + "$)|(?:^" + r + "$)"), i2 = new RegExp("^" + n + "$"), s = new RegExp("^" + r + "$"), u = function(k) {
    return k && k.exact ? a : new RegExp("(?:" + t(k) + n + t(k) + ")|(?:" + t(k) + r + t(k) + ")", "g");
  };
  u.v4 = function(F) {
    return F && F.exact ? i2 : new RegExp("" + t(F) + n + t(F), "g");
  }, u.v6 = function(F) {
    return F && F.exact ? s : new RegExp("" + t(F) + r + t(F), "g");
  };
  var l = "(?:(?:[a-z]+:)?//)", c = "(?:\\S+(?::\\S*)?@)?", d = u.v4().source, f = u.v6().source, h2 = "(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)", v = "(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*", g = "(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))", w = "(?::\\d{2,5})?", y = '(?:[/?#][^\\s"]*)?', R = "(?:" + l + "|www\\.)" + c + "(?:localhost|" + d + "|" + f + "|" + h2 + v + g + ")" + w + y;
  return vo = new RegExp("(?:^" + R + "$)", "i"), vo;
};
var da = {
  // http://emailregex.com/
  email: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,
  // url: new RegExp(
  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$',
  //   'i',
  // ),
  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i
};
var xn = {
  integer: function(t) {
    return xn.number(t) && parseInt(t, 10) === t;
  },
  float: function(t) {
    return xn.number(t) && !xn.integer(t);
  },
  array: function(t) {
    return Array.isArray(t);
  },
  regexp: function(t) {
    if (t instanceof RegExp)
      return true;
    try {
      return !!new RegExp(t);
    } catch {
      return false;
    }
  },
  date: function(t) {
    return typeof t.getTime == "function" && typeof t.getMonth == "function" && typeof t.getYear == "function" && !isNaN(t.getTime());
  },
  number: function(t) {
    return isNaN(t) ? false : typeof t == "number";
  },
  object: function(t) {
    return typeof t == "object" && !xn.array(t);
  },
  method: function(t) {
    return typeof t == "function";
  },
  email: function(t) {
    return typeof t == "string" && t.length <= 320 && !!t.match(da.email);
  },
  url: function(t) {
    return typeof t == "string" && t.length <= 2048 && !!t.match(yd());
  },
  hex: function(t) {
    return typeof t == "string" && !!t.match(da.hex);
  }
};
var wd = function(t, n, o, r, a) {
  if (t.required && n === void 0) {
    hi(t, n, o, r, a);
    return;
  }
  var i2 = ["integer", "float", "array", "regexp", "object", "method", "email", "number", "date", "url", "hex"], s = t.type;
  i2.indexOf(s) > -1 ? xn[s](n) || r.push(pt(a.messages.types[s], t.fullField, t.type)) : s && typeof n !== t.type && r.push(pt(a.messages.types[s], t.fullField, t.type));
};
var _d = function(t, n, o, r, a) {
  var i2 = typeof t.len == "number", s = typeof t.min == "number", u = typeof t.max == "number", l = /[\uD800-\uDBFF][\uDC00-\uDFFF]/g, c = n, d = null, f = typeof n == "number", h2 = typeof n == "string", v = Array.isArray(n);
  if (f ? d = "number" : h2 ? d = "string" : v && (d = "array"), !d)
    return false;
  v && (c = n.length), h2 && (c = n.replace(l, "_").length), i2 ? c !== t.len && r.push(pt(a.messages[d].len, t.fullField, t.len)) : s && !u && c < t.min ? r.push(pt(a.messages[d].min, t.fullField, t.min)) : u && !s && c > t.max ? r.push(pt(a.messages[d].max, t.fullField, t.max)) : s && u && (c < t.min || c > t.max) && r.push(pt(a.messages[d].range, t.fullField, t.min, t.max));
};
var _n = "enum";
var Cd = function(t, n, o, r, a) {
  t[_n] = Array.isArray(t[_n]) ? t[_n] : [], t[_n].indexOf(n) === -1 && r.push(pt(a.messages[_n], t.fullField, t[_n].join(", ")));
};
var Sd = function(t, n, o, r, a) {
  if (t.pattern) {
    if (t.pattern instanceof RegExp)
      t.pattern.lastIndex = 0, t.pattern.test(n) || r.push(pt(a.messages.pattern.mismatch, t.fullField, n, t.pattern));
    else if (typeof t.pattern == "string") {
      var i2 = new RegExp(t.pattern);
      i2.test(n) || r.push(pt(a.messages.pattern.mismatch, t.fullField, n, t.pattern));
    }
  }
};
var _e = {
  required: hi,
  whitespace: bd,
  type: wd,
  range: _d,
  enum: Cd,
  pattern: Sd
};
var Fd = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n, "string") && !t.required)
      return o();
    _e.required(t, n, r, i2, a, "string"), He(n, "string") || (_e.type(t, n, r, i2, a), _e.range(t, n, r, i2, a), _e.pattern(t, n, r, i2, a), t.whitespace === true && _e.whitespace(t, n, r, i2, a));
  }
  o(i2);
};
var Rd = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n) && !t.required)
      return o();
    _e.required(t, n, r, i2, a), n !== void 0 && _e.type(t, n, r, i2, a);
  }
  o(i2);
};
var Dd = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (n === "" && (n = void 0), He(n) && !t.required)
      return o();
    _e.required(t, n, r, i2, a), n !== void 0 && (_e.type(t, n, r, i2, a), _e.range(t, n, r, i2, a));
  }
  o(i2);
};
var kd = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n) && !t.required)
      return o();
    _e.required(t, n, r, i2, a), n !== void 0 && _e.type(t, n, r, i2, a);
  }
  o(i2);
};
var $d = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n) && !t.required)
      return o();
    _e.required(t, n, r, i2, a), He(n) || _e.type(t, n, r, i2, a);
  }
  o(i2);
};
var Od = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n) && !t.required)
      return o();
    _e.required(t, n, r, i2, a), n !== void 0 && (_e.type(t, n, r, i2, a), _e.range(t, n, r, i2, a));
  }
  o(i2);
};
var Ed = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n) && !t.required)
      return o();
    _e.required(t, n, r, i2, a), n !== void 0 && (_e.type(t, n, r, i2, a), _e.range(t, n, r, i2, a));
  }
  o(i2);
};
var Td = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (n == null && !t.required)
      return o();
    _e.required(t, n, r, i2, a, "array"), n != null && (_e.type(t, n, r, i2, a), _e.range(t, n, r, i2, a));
  }
  o(i2);
};
var Ad = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n) && !t.required)
      return o();
    _e.required(t, n, r, i2, a), n !== void 0 && _e.type(t, n, r, i2, a);
  }
  o(i2);
};
var Id = "enum";
var Vd = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n) && !t.required)
      return o();
    _e.required(t, n, r, i2, a), n !== void 0 && _e[Id](t, n, r, i2, a);
  }
  o(i2);
};
var Pd = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n, "string") && !t.required)
      return o();
    _e.required(t, n, r, i2, a), He(n, "string") || _e.pattern(t, n, r, i2, a);
  }
  o(i2);
};
var Md = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n, "date") && !t.required)
      return o();
    if (_e.required(t, n, r, i2, a), !He(n, "date")) {
      var u;
      n instanceof Date ? u = n : u = new Date(n), _e.type(t, u, r, i2, a), u && _e.range(t, u.getTime(), r, i2, a);
    }
  }
  o(i2);
};
var Bd = function(t, n, o, r, a) {
  var i2 = [], s = Array.isArray(n) ? "array" : typeof n;
  _e.required(t, n, r, i2, a, s), o(i2);
};
var nr = function(t, n, o, r, a) {
  var i2 = t.type, s = [], u = t.required || !t.required && r.hasOwnProperty(t.field);
  if (u) {
    if (He(n, i2) && !t.required)
      return o();
    _e.required(t, n, r, s, a, i2), He(n, i2) || _e.type(t, n, r, s, a);
  }
  o(s);
};
var jd = function(t, n, o, r, a) {
  var i2 = [], s = t.required || !t.required && r.hasOwnProperty(t.field);
  if (s) {
    if (He(n) && !t.required)
      return o();
    _e.required(t, n, r, i2, a);
  }
  o(i2);
};
var Un = {
  string: Fd,
  method: Rd,
  number: Dd,
  boolean: kd,
  regexp: $d,
  integer: Od,
  float: Ed,
  array: Td,
  object: Ad,
  enum: Vd,
  pattern: Pd,
  date: Md,
  url: nr,
  hex: nr,
  email: nr,
  required: Bd,
  any: jd
};
function Or() {
  return {
    default: "Validation error on field %s",
    required: "%s is required",
    enum: "%s must be one of %s",
    whitespace: "%s cannot be empty",
    date: {
      format: "%s date %s is invalid for format %s",
      parse: "%s date could not be parsed, %s is invalid ",
      invalid: "%s date %s is invalid"
    },
    types: {
      string: "%s is not a %s",
      method: "%s is not a %s (function)",
      array: "%s is not an %s",
      object: "%s is not an %s",
      number: "%s is not a %s",
      date: "%s is not a %s",
      boolean: "%s is not a %s",
      integer: "%s is not an %s",
      float: "%s is not a %s",
      regexp: "%s is not a valid %s",
      email: "%s is not a valid %s",
      url: "%s is not a valid %s",
      hex: "%s is not a valid %s"
    },
    string: {
      len: "%s must be exactly %s characters",
      min: "%s must be at least %s characters",
      max: "%s cannot be longer than %s characters",
      range: "%s must be between %s and %s characters"
    },
    number: {
      len: "%s must equal %s",
      min: "%s cannot be less than %s",
      max: "%s cannot be greater than %s",
      range: "%s must be between %s and %s"
    },
    array: {
      len: "%s must be exactly %s in length",
      min: "%s cannot be less than %s in length",
      max: "%s cannot be greater than %s in length",
      range: "%s must be between %s and %s in length"
    },
    pattern: {
      mismatch: "%s value %s does not match pattern %s"
    },
    clone: function() {
      var t = JSON.parse(JSON.stringify(this));
      return t.clone = this.clone, t;
    }
  };
}
var Er = Or();
var co = function() {
  function e(n) {
    this.rules = null, this._messages = Er, this.define(n);
  }
  var t = e.prototype;
  return t.define = function(o) {
    var r = this;
    if (!o)
      throw new Error("Cannot configure a schema with no rules");
    if (typeof o != "object" || Array.isArray(o))
      throw new Error("Rules must be an object");
    this.rules = {}, Object.keys(o).forEach(function(a) {
      var i2 = o[a];
      r.rules[a] = Array.isArray(i2) ? i2 : [i2];
    });
  }, t.messages = function(o) {
    return o && (this._messages = ca(Or(), o)), this._messages;
  }, t.validate = function(o, r, a) {
    var i2 = this;
    r === void 0 && (r = {}), a === void 0 && (a = function() {
    });
    var s = o, u = r, l = a;
    if (typeof u == "function" && (l = u, u = {}), !this.rules || Object.keys(this.rules).length === 0)
      return l && l(null, s), Promise.resolve(s);
    function c(g) {
      var w = [], y = {};
      function R(k) {
        if (Array.isArray(k)) {
          var A;
          w = (A = w).concat.apply(A, k);
        } else
          w.push(k);
      }
      for (var F = 0; F < g.length; F++)
        R(g[F]);
      w.length ? (y = $r(w), l(w, y)) : l(null, s);
    }
    if (u.messages) {
      var d = this.messages();
      d === Er && (d = Or()), ca(d, u.messages), u.messages = d;
    } else
      u.messages = this.messages();
    var f = {}, h2 = u.keys || Object.keys(this.rules);
    h2.forEach(function(g) {
      var w = i2.rules[g], y = s[g];
      w.forEach(function(R) {
        var F = R;
        typeof F.transform == "function" && (s === o && (s = fn({}, s)), y = s[g] = F.transform(y)), typeof F == "function" ? F = {
          validator: F
        } : F = fn({}, F), F.validator = i2.getValidationMethod(F), F.validator && (F.field = g, F.fullField = F.fullField || g, F.type = i2.getType(F), f[g] = f[g] || [], f[g].push({
          rule: F,
          value: y,
          source: s,
          field: g
        }));
      });
    });
    var v = {};
    return pd(f, u, function(g, w) {
      var y = g.rule, R = (y.type === "object" || y.type === "array") && (typeof y.fields == "object" || typeof y.defaultField == "object");
      R = R && (y.required || !y.required && g.value), y.field = g.field;
      function F(N, $) {
        return fn({}, $, {
          fullField: y.fullField + "." + N,
          fullFields: y.fullFields ? [].concat(y.fullFields, [N]) : [N]
        });
      }
      function k(N) {
        N === void 0 && (N = []);
        var $ = Array.isArray(N) ? N : [N];
        !u.suppressWarning && $.length && e.warning("async-validator:", $), $.length && y.message !== void 0 && ($ = [].concat(y.message));
        var B2 = $.map(ua(y, s));
        if (u.first && B2.length)
          return v[y.field] = 1, w(B2);
        if (!R)
          w(B2);
        else {
          if (y.required && !g.value)
            return y.message !== void 0 ? B2 = [].concat(y.message).map(ua(y, s)) : u.error && (B2 = [u.error(y, pt(u.messages.required, y.field))]), w(B2);
          var D = {};
          y.defaultField && Object.keys(g.value).map(function(V) {
            D[V] = y.defaultField;
          }), D = fn({}, D, g.rule.fields);
          var x = {};
          Object.keys(D).forEach(function(V) {
            var O = D[V], M = Array.isArray(O) ? O : [O];
            x[V] = M.map(F.bind(null, V));
          });
          var I = new e(x);
          I.messages(u.messages), g.rule.options && (g.rule.options.messages = u.messages, g.rule.options.error = u.error), I.validate(g.value, g.rule.options || u, function(V) {
            var O = [];
            B2 && B2.length && O.push.apply(O, B2), V && V.length && O.push.apply(O, V), w(O.length ? O : null);
          });
        }
      }
      var A;
      if (y.asyncValidator)
        A = y.asyncValidator(y, g.value, k, g.source, u);
      else if (y.validator) {
        try {
          A = y.validator(y, g.value, k, g.source, u);
        } catch (N) {
          console.error == null || console.error(N), u.suppressValidatorError || setTimeout(function() {
            throw N;
          }, 0), k(N.message);
        }
        A === true ? k() : A === false ? k(typeof y.message == "function" ? y.message(y.fullField || y.field) : y.message || (y.fullField || y.field) + " fails") : A instanceof Array ? k(A) : A instanceof Error && k(A.message);
      }
      A && A.then && A.then(function() {
        return k();
      }, function(N) {
        return k(N);
      });
    }, function(g) {
      c(g);
    }, s);
  }, t.getType = function(o) {
    if (o.type === void 0 && o.pattern instanceof RegExp && (o.type = "pattern"), typeof o.validator != "function" && o.type && !Un.hasOwnProperty(o.type))
      throw new Error(pt("Unknown rule type %s", o.type));
    return o.type || "string";
  }, t.getValidationMethod = function(o) {
    if (typeof o.validator == "function")
      return o.validator;
    var r = Object.keys(o), a = r.indexOf("message");
    return a !== -1 && r.splice(a, 1), r.length === 1 && r[0] === "required" ? Un.required : Un[this.getType(o)] || void 0;
  }, e;
}();
co.register = function(t, n) {
  if (typeof n != "function")
    throw new Error("Cannot register a validator by type, validator is not a function");
  Un[t] = n;
};
co.warning = fi;
co.messages = Er;
co.validators = Un;
function Nd(e) {
  const t = {};
  for (const n in e) {
    const o = e[n].getForm(), r = o.rules || o.rule, a = n.split(".");
    let i2 = t;
    if (r)
      for (let s = 0; s < a.length; s++) {
        const u = a[s];
        i2[u] || (i2[u] = { type: "object", fields: {} }), s == a.length - 1 ? i2[u] = r : i2 = i2[u].fields;
      }
  }
  return new co(t);
}
function Tr(e, t) {
  forEach_default(e, (n) => {
    n.children ? Tr(n.children, t) : t(n);
  });
}
function Ld(e, t) {
  const n = i.get();
  function o() {
    if (e.data)
      return e.data;
    if (t.value) {
      let r = n.table;
      return e.tableVersion === "v2" && (r = n.tableV2), t.value[r.data] || [];
    }
    return [];
  }
  return {
    getData: o,
    insert(r, a) {
      o().splice(r, 0, a);
    },
    unshift(r) {
      o().unshift(r);
    },
    remove(r) {
      o().splice(r, 1);
    },
    get(r) {
      return o()[r];
    }
  };
}
function qd(e, t, n) {
  const o = Ld(e, n), r = reactive([]);
  function a(p) {
    return typeof e.rowKey == "string" ? p[e.rowKey] : e.rowKey(p);
  }
  function i2(p) {
    return p[e.editable.rowKey];
  }
  function s(p) {
    for (const _ in r) {
      const C = r[_], S = C.cells, T = C.rowData;
      if (p({ rowData: T, row: C, cells: S }) === "break")
        return;
    }
  }
  function u(p) {
    s(({ rowData: _, row: C, cells: S }) => {
      forEach_default(S, (T, ne) => {
        p({ rowData: _, row: C, cells: S, cell: T, key: ne });
      });
    });
  }
  const { merge: l } = De(), c = computed(() => l({
    enabled: false,
    //模式，free，row，cell
    mode: "free",
    rowKey: "id",
    addForm: {},
    editForm: {},
    //是否排他式激活，激活一个，其他自动提交或取消
    exclusive: true,
    //排他式激活时，其他的效果，cancel，save
    exclusiveEffect: "cancel",
    //激活触发方式,onClick,onDbClick
    activeTrigger: "onClick",
    //默认激活
    activeDefault: false,
    isEditable(p) {
      return true;
    }
  }, e.editable));
  function d(p, _, C, S) {
    function T(Z) {
      return get_default(p, Z);
    }
    function ne(Z, Ce) {
      set_default(p, Z, Ce);
    }
    const P = computed(() => {
      var Z;
      return ((Z = S.editable) == null ? void 0 : Z.updateCell) || c.value.updateCell;
    }), W = computed(() => {
      var Z;
      return ((Z = S.editable) == null ? void 0 : Z.showAction) || c.value.showAction;
    }), oe = reactive({
      mode: C < 0 ? "add" : "edit",
      oldValue: void 0,
      newValue: void 0,
      loading: false,
      isEditing: c.value.activeDefault,
      activeTrigger: c.value.activeTrigger,
      column: S,
      updateCell: P,
      showAction: W,
      isEditable: () => {
        var ze;
        let Z = (ze = S == null ? void 0 : S.editable) == null ? void 0 : ze.disabled;
        Z instanceof Function && (Z = config.disabled({ column: item, editableId: C, row: rowData }));
        let Ce = null;
        return Z != null && (Ce = !Z), Ce ?? (c.value.isEditable({ editableId: C, key: _, row: p }) || false);
      },
      isChanged: () => oe.newValue !== oe.oldValue,
      getForm: () => {
        let Z = c.value[oe.mode + "Form"];
        return Z == null && (Z = c.value.editForm), Z[_];
      },
      active: (Z = {}) => {
        (Z.exclusive ?? c.value.exclusive) && ((Z.exclusiveEffect ?? c.value.exclusiveEffect) === "save" ? k() : A()), oe.isEditing = true, oe.oldValue = T(_);
        const ze = c.value.editForm[_];
        ze && I(ze, p);
      },
      inactive: () => {
        oe.isEditing = false, oe.newValue = T(_);
      },
      resume: () => {
        oe.isEditing && (oe.isEditing = false, ne(_, oe.oldValue), delete oe.newValue, delete oe.oldValue);
      },
      cancel: () => {
        oe.resume();
      },
      persist: () => {
        oe.isEditing = false, delete oe.newValue, delete oe.oldValue;
      },
      save: async () => {
        const Z = unref(oe.updateCell);
        if (!Z) {
          ue.warn("没有配置table.editable.updateCell方法,无法保存，相关文档：http://fast-crud.docmirror.cn/api/crud-options/table.html#editable");
          return;
        }
        oe.loading = true;
        try {
          const Ce = await Z({ editableId: C, row: p, key: _, value: T(_) }), ze = p[c.value.rowKey];
          (ze == null || ze <= 0) && ((Ce && Ce[c.value.rowKey]) == null ? ue.error(`对于添加的行，updateCell方法需要返回{'id':value}，如果你配置了别的rowKey，需要返回{[rowKey]:id}。
当前返回值:${JSON.stringify(Ce)}`) : p[c.value.rowKey] = Ce[c.value.rowKey]), oe.persist();
        } finally {
          oe.loading = false;
        }
      }
    });
    return oe;
  }
  function f(p, _) {
    const C = {};
    Tr(e.columns, (W) => {
      C[W.key] = d(_, W.key, p, W);
    });
    const S = computed(() => Nd(C)), T = a(_), ne = T == null || T < 0, P = reactive({
      isAdd: ne,
      rowData: _,
      editableId: p,
      isEditing: false,
      loading: false,
      cells: C,
      validator: S,
      inactive: () => {
        P.isEditing = false, forEach_default(P.cells, (W) => {
          W.isEditing && W.inactive();
        });
      },
      active: () => {
        P.isEditing = true, forEach_default(P.cells, (W) => {
          W.active({ exclusive: false });
        });
      },
      persist: () => {
        P.isEditing = false, P.inactive(), delete P.isAdd, forEach_default(P.cells, (W) => {
          W.persist();
        });
      },
      resume: () => {
        P.isEditing = false, forEach_default(P.cells, (W) => {
          W.resume();
        });
      },
      cancel: () => {
        P.resume();
      },
      validate: async (W) => {
        try {
          return forEach_default(P.cells, (oe, Z) => {
            oe.validateErrors = [];
          }), W == null && (W = P.rowData), await P.validator.validate(W), true;
        } catch (oe) {
          const { errors: Z, fields: Ce } = oe;
          return forEach_default(Ce, (ze, rn) => {
            const yn = P.cells[rn];
            yn && (yn.validateErrors = ze);
          }), Ce;
        }
      },
      getRowData: () => {
        const W = cloneDeep_default(P.rowData);
        return delete W[e.editable.rowKey], delete W.children, W;
      },
      save: async (W) => {
        const { doSave: oe } = W, Z = P.rowData, { merge: Ce } = De();
        if (await P.validate() !== true)
          return;
        function rn(an) {
          an && (a(an) == null && console.error("保存接口没有返回rowKey,无法更新该行的id,newRow:", an), Ce(Z, an));
        }
        P.loading = true;
        const yn = P.getRowData();
        try {
          await oe({ isAdd: P.isAdd, row: yn, setData: rn }), P.persist();
        } finally {
          P.loading = false;
        }
      }
    });
    if (watch(() => _, async (W, oe, Z) => {
      await P.validate();
    }, {
      deep: true
    }), _.children && _.children.length > 0)
      for (const W of _.children) {
        W[e.editable.rowKey] || (W[e.editable.rowKey] = v());
        const oe = W[e.editable.rowKey];
        r[oe] = f(oe, W);
      }
    return ne && P.active(), P;
  }
  let h2 = 0;
  function v() {
    return h2++, h2;
  }
  function g(p) {
    p == null && (p = o.getData());
    const _ = Object.assign({}, r);
    forOwn_default(r, (C, S) => {
      delete r[S];
    }), forEach_default(p, (C) => {
      C[e.editable.rowKey] || (C[e.editable.rowKey] = v());
      const S = C[e.editable.rowKey];
      _[S] ? r[S] = _[S] : r[S] = f(S, C);
    }), c.value.onSetup && c.value.onSetup();
  }
  function w(p) {
    const _ = e.editable.rowKey, C = [];
    for (const S of p) {
      const T = { [_]: S[_] };
      S.children && S.children.length && (T.children = w(S.children)), C.push(T);
    }
    return C;
  }
  watch(() => {
    const p = e.data;
    let _ = [];
    return p != null && p instanceof Array && (_ = w(p)), JSON.stringify(_);
  }, (p, _) => {
    c.value.enabled && g(e.data);
  }, {
    immediate: true
  }), watch(() => c.value.enabled, (p) => {
    var _;
    p && (((_ = o.getData()) == null ? void 0 : _.length) > 0 && g(), c.value.onEnabled && c.value.onEnabled({ ...c.value }));
  }, {
    immediate: true
  }), watch(() => c.value.mode, () => {
    c.value.onEnabled && c.value.onEnabled({ ...c.value });
  });
  function y(p, _) {
    var C;
    if (_ != null)
      return (C = r[p]) == null ? void 0 : C.cells[_];
  }
  function R(p = {}) {
    u(({ cell: _ }) => {
      _.active({ ...p, exclusive: false });
    });
  }
  function F() {
    u(({ cell: p }) => {
      p.isEditing && p.inactive();
    });
  }
  async function k() {
    u(({ cell: p }) => {
      p.isEditing && p.save();
    });
  }
  function A() {
    u(({ cell: p }) => {
      p.isEditing && p.cancel();
    });
  }
  function N() {
    F(), s(({ row: p }) => {
      delete p.isAdd;
    }), u(({ cell: p }) => {
      delete p.newValue, delete p.oldValue;
    });
  }
  function $() {
    u(({ cell: p }) => {
      p.resume();
    });
  }
  function B2() {
    let p = false;
    return s(({ cells: _ }) => {
      forEach_default(_, (C) => {
        if (C.isChanged())
          return p = true, "break";
      });
    }), p;
  }
  let D = 0;
  function x(p, _) {
    Tr(p, (C) => {
      I(C, _);
    });
  }
  function I(p, _) {
    const C = get_default(_, p.key), S = unref(p.value);
    S != null && C == null && set_default(_, p.key, S);
  }
  async function V(p = { row: void 0, active: true }) {
    let _ = p.row || { [c.value.rowKey]: --D, [e.rowKey]: D };
    if (x(c.value.addForm, _), p.addRowFunc) {
      const C = await p.addRowFunc({ row: p.row });
      C && (_ = C);
    } else if (e.editable.addRow) {
      const C = await e.editable.addRow(o.getData(), _);
      C && (_ = C);
    } else
      o.unshift(_);
    if (p.active ?? e.editable.activeDefault) {
      await nextTick();
      const C = i2(_), S = le(C);
      S && S.active();
    }
  }
  function O(p, _) {
    for (let C = 0; C < _.length; C++) {
      const S = _[C];
      if (i2(S) === p)
        return remove_default(_, S), true;
      if (S.children && S.children.length > 0 && O(p, S.children))
        return;
    }
    return false;
  }
  function M(p) {
    delete r[p], O(p, o.getData());
  }
  function X(p) {
    const { cols: _ } = p;
    s(({ cells: C }) => {
      forEach_default(_, (S) => {
        C[S].active({ ...p, exclusive: false });
      });
    });
  }
  function le(p) {
    return r[p];
  }
  function ye() {
    const p = [];
    return s(({ row: _ }) => {
      _.isEditing && p.push(_);
    }), p;
  }
  async function me() {
    const p = {};
    let _ = false;
    for (const C in r) {
      const S = r[C], T = await S.validate();
      T != true && (p[S.editableId] = T, _ = true);
    }
    return _ ? p : true;
  }
  function Se(p) {
    for (const _ of p)
      delete _[e.editable.rowKey], _.children && _.children.length > 0 && Se(_.children);
    return p;
  }
  function G(p) {
    return p == null && (p = cloneDeep_default(o.getData())), p == null ? [] : Se(p);
  }
  return {
    editable: {
      options: c,
      setupEditable: g,
      inactive: F,
      active: R,
      persist: N,
      saveEach: k,
      cancelAll: A,
      resume: $,
      addRow: V,
      removeRow: M,
      getEditableRow: le,
      activeCols: X,
      hasDirty: B2,
      getEditableCell: y,
      eachRows: s,
      eachCells: u,
      validate: me,
      getCleanTableData: G,
      getActiveRows: ye
    }
  };
}
function xd(e) {
  const t = (o, r) => get_default(e, `value[${o}].${r}`);
  provide("componentRef:get", t);
  const n = (o, r, a) => set_default(e, `value[${o}].${r}`, a);
  return provide("componentRef:set", n), {
    getter: t,
    setter: n
  };
}
function Hd({
  props: e,
  ui: t,
  sortedColumns: n,
  renderRowHandle: o,
  renderCellComponent: r
}) {
  resolveDynamicComponent(t.table.name);
  const a = resolveDynamicComponent(t.tableColumn.name), i2 = resolveDynamicComponent(t.tableColumnGroup.name);
  t.tableColumn;
  const s = {}, u = (l) => {
    const c = {
      ...l.columnSlots
    }, d = "cell_" + l.key;
    let f = a;
    if (l.children)
      c.default = () => {
        const v = [];
        return forEach_default(l.children, (g) => {
          g.show !== false && v.push(u(g));
        }), v;
      }, f = i2;
    else if (l.type != null) {
      ue.debug("cell render column.type:", l.type);
      const v = e.cellSlots && e.cellSlots[d];
      v && (c.default = v);
    } else
      c.default = (v) => r(l, v);
    const h2 = {
      ...l
    };
    return delete h2.children, createVNode(f, mergeProps({
      ref: "tableColumnRef"
    }, h2, {
      label: l.title,
      prop: l.key,
      dataIndex: l.key
    }), c);
  };
  return s.default = () => {
    const l = [];
    if (forEach_default(n, (c) => {
      c.show !== false && l.push(u(c));
    }), e.rowHandle && e.rowHandle.show !== false) {
      const c = {
        default: o
      };
      l.push(createVNode(a, mergeProps({
        ref: "tableColumnRef"
      }, e.rowHandle, {
        label: e.rowHandle.title,
        prop: e.rowHandle.key || "rowHandle"
      }), c));
    }
    return l;
  }, e.slots && forEach_default(e.slots, (l, c) => {
    s[c] = l;
  }), s;
}
function mi(e, t) {
  var c;
  const {
    props: n,
    renderRowHandle: o,
    renderCellComponent: r,
    sortedColumns: a
  } = e, {
    ui: i2
  } = B(), s = a ?? {}, u = [];
  let l = i2.table;
  n.tableVersion === "v2" && (l = i2.tableV2);
  for (const d in s) {
    const f = s[d];
    if (f.show === false)
      continue;
    const h2 = {
      ...f
    };
    if (h2._parent = t, h2.dataIndex = f.key, u.push(h2), f.children != null) {
      const g = {
        ...e,
        sortedColumns: f.children
      };
      delete g.renderRowHandle, h2.children = mi(g, h2);
    } else if (f.type == null) {
      const g = h2[l.renderMethod], w = {
        ...h2
      };
      delete w[l.renderMethod], g ? h2[l.renderMethod] = (y, R, F) => {
        const k = l.rebuildRenderScope(y, R, F);
        return g(k, () => r(w, k));
      } : h2[l.renderMethod] = (y, R, F) => {
        const k = l.rebuildRenderScope(y, R, F);
        return r(w, k);
      };
    }
  }
  if (o && ((c = n.rowHandle) == null ? void 0 : c.show) !== false) {
    const d = {
      key: "_rowHandle",
      ...n.rowHandle
    };
    d[l.renderMethod] = (f, h2, v) => {
      const g = l.rebuildRenderScope(f, h2, v);
      return o(g);
    }, u.push(d);
  }
  return ue.debug("table columns:", u), u;
}
var zd = defineComponent({
  name: "FsTable",
  inheritAttrs: false,
  props: {
    tableVersion: {
      type: String
    },
    /**
     * table插槽
     */
    slots: {
      type: Object
    },
    /**
     * 单元格插槽
     */
    cellSlots: {
      type: Object
    },
    /**
     * 列配置，支持el-table-column|a-table-column配置
     */
    columns: {
      type: Object,
      default: void 0
    },
    /**
     * 操作列
     */
    rowHandle: {
      type: Object
    },
    /**
     * 是否显示表格
     */
    show: {
      type: Boolean,
      default: true
    },
    /**
     * 表格数据
     */
    data: {
      type: Array,
      default: () => []
    },
    conditionalRender: {
      type: Object
    },
    /**
     * 行编辑，批量编辑
     */
    editable: {
      type: Object,
      default() {
        return {};
      }
    },
    loading: {
      type: Boolean,
      default: false
    },
    /**
     * 当前sort状态
     */
    sort: {
      type: Object
    },
    request: {
      type: Object
    },
    rowKey: {
      type: [String, Function],
      default: "id"
    }
  },
  emits: ["row-handle", "value-change", "pagination-change", "filter-change", "sort-change", "data-change"],
  setup(e, t) {
    vt.trace("fs-table");
    const n = ref(), o = ref([]);
    xd(o);
    const r = ($, B2) => {
      if (!B2 || $ == null || $ > o.value.length)
        return;
      const x = o.value[$][B2];
      return x == null ? void 0 : x.getTargetRef();
    }, {
      ui: a
    } = B(), i2 = getCurrentInstance();
    watch(() => e.data, ($) => {
      t.emit("data-change", {
        data: $
      });
    });
    let s = a.table, u = a.tableColumn;
    e.tableVersion === "v2" && (s = a.tableV2, u = a.tableColumnV2);
    function l($ = 0) {
      s.scrollTo({
        top: $,
        tableRef: n,
        fsTableRef: i2
      });
    }
    const c = resolveDynamicComponent(s.name), d = qd(e, t, n), f = ($, B2) => {
      const D = B2[u.row], x = D, I = B2[u.index];
      return B2.index = I, {
        ...B2,
        key: $.key,
        value: get_default(D, $.key),
        row: D,
        form: x,
        getComponentRef: (V) => r(I, V)
      };
    };
    function h2($) {
      t.emit("row-handle", $);
    }
    const v = s.onChange({
      onSortChange: ($) => {
        t.emit("sort-change", $);
      },
      onFilterChange: ($) => {
        t.emit("filter-change", $);
      },
      onPagination: () => {
      },
      bubbleUp: ($) => {
        $(t.attrs);
      }
    }), g = ($) => {
      $.index = $[u.index];
      const B2 = "cell-rowHandle", D = {};
      if (e.cellSlots)
        for (const x in e.cellSlots)
          x.startsWith(B2) && (D[x] = e.cellSlots[x]);
      return createVNode(resolveComponent("fs-row-handle"), mergeProps(e.rowHandle, {
        scope: $,
        onHandle: h2
      }), D);
    }, w = ($, B2) => {
      var ye, me, Se, G, p, _, C, S, T, ne, P, W;
      const D = "cell_" + $.key, x = B2.row = B2[u.row], I = {
        modelValue: get_default(B2[u.row], $.key),
        "onUpdate:modelValue": (oe) => {
          var Ce;
          set_default(B2[u.row], $.key, oe);
          const Z = f($, B2);
          t.emit("value-change", Z), $.valueChange && ($.valueChange instanceof Function ? $.valueChange(Z) : (Ce = $.valueChange) == null || Ce.handle(Z));
        }
      }, V = (oe) => {
        const Z = B2[u.index], Ce = $.key;
        let ze = o.value[Z];
        ze == null && (o.value[Z] = ze = {}), ze[Ce] = oe;
      }, O = B2[u.index], M = x[(ye = e.editable) == null ? void 0 : ye.rowKey], X = e.cellSlots && e.cellSlots[D], le = f($, B2);
      if (((G = (Se = (me = d.editable) == null ? void 0 : me.options) == null ? void 0 : Se.value) == null ? void 0 : G.enabled) === true) {
        const oe = d.editable.getEditableCell(M, $.key);
        return createVNode(resolveComponent("fs-editable-cell"), mergeProps({
          ref: V,
          key: $.key,
          columnKey: $.key,
          index: O,
          editableId: M,
          item: $,
          editableCell: oe,
          editableOpts: (_ = (p = d.editable) == null ? void 0 : p.options) == null ? void 0 : _.value,
          scope: le,
          slots: X,
          disabled: (T = (S = (C = d.editable) == null ? void 0 : C.options) == null ? void 0 : S.value) == null ? void 0 : T.disabled,
          readonly: (W = (P = (ne = d.editable) == null ? void 0 : ne.options) == null ? void 0 : P.value) == null ? void 0 : W.readonly
        }, I), null);
      } else
        return createVNode(resolveComponent("fs-cell"), mergeProps({
          ref: V,
          key: $.key,
          item: $,
          scope: le,
          slots: X
        }, I, {
          conditionalRender: e.conditionalRender
        }), null);
    }, {
      expose: y
    } = t;
    y({
      tableRef: n,
      componentRefs: o,
      getComponentRef: r,
      ...d,
      scrollTo: l
    });
    const R = s.renderMode, F = computed(() => ({
      [s.data]: e.data
    })), {
      merge: k
    } = De(), A = computed(() => k({}, t.attrs, v)), N = computed(() => e.columns);
    if (R === "slot") {
      const $ = computed(() => Hd({
        props: e,
        ui: a,
        sortedColumns: N.value,
        renderRowHandle: g,
        renderCellComponent: w
      }));
      return () => {
        if (e.show === false)
          return;
        const B2 = createVNode(c, mergeProps({
          ref: n,
          loading: e.loading,
          rowKey: e.rowKey
        }, A.value, F.value), $.value);
        if (typeof s.vLoading == "string") {
          const D = resolveDirective(s.vLoading);
          return withDirectives(B2, [[D, e.loading]]);
        }
        return B2;
      };
    } else {
      const $ = computed(() => mi({
        props: e,
        ctx: t,
        ui: a,
        getContextFn: f,
        sortedColumns: N.value,
        componentRefs: o,
        renderRowHandle: g,
        renderCellComponent: w,
        columns: e.columns
      })), B2 = computed(() => {
        const x = [];
        function I(V) {
          V.forEach((O) => {
            O.children ? I(O.children) : x.push(O);
          });
        }
        return I($.value), x;
      }), D = computed(() => s.buildMultiHeadersBind ? s.buildMultiHeadersBind({
        treeColumns: $.value,
        flatColumns: B2.value
      }) : {
        bind: {},
        slots: {}
      });
      return () => {
        var O;
        if (e.show === false)
          return;
        const x = {
          ...e.slots,
          ...(O = D.value) == null ? void 0 : O.slots
        }, I = s.columnsIsFlat, V = (M = {}) => {
          var X;
          return createVNode(c, mergeProps({
            ref: n,
            loading: e.loading,
            rowKey: e.rowKey
          }, A.value, {
            columns: I ? B2.value : $.value
          }, F.value, (X = D.value) == null ? void 0 : X.bind, M), x);
        };
        if (e.tableVersion === "v2" && a.type === "element") {
          const M = {
            default({
              width: X,
              height: le
            }) {
              return V({
                width: X,
                height: le
              });
            }
          };
          return createVNode(resolveComponent("el-auto-resizer"), null, M);
        }
        return V();
      };
    }
  }
});
var Wd = defineComponent({
  name: "FsCell",
  props: {
    item: {},
    /**
     * scope
     */
    scope: {
      default() {
        return {};
      }
    },
    /**
     * 插槽
     */
    slots: {},
    /**
     * 条件渲染，符合条件的情况下优先渲染
     */
    conditionalRender: {
      type: Object
    }
  },
  setup(e, t) {
    const {
      doComputed: n
    } = Nt(), a = n(() => e.item.component, () => e.scope), i2 = ref();
    function s() {
      return i2.value.getTargetRef();
    }
    t.expose({
      getTargetRef: s,
      targetRef: i2
    });
    const u = computed(() => {
      let c = e.item.showTitle;
      const d = e.scope.value;
      return c === true && (c = d), c;
    }), l = computed(() => (d) => createVNode("span", {
      class: "fs-cell",
      title: u.value
    }, [d]));
    return () => {
      var v, g;
      const c = e.scope.value, d = l.value, f = {
        ...e.scope,
        props: e.item
      }, h2 = e.item.conditionalRender ?? e.conditionalRender;
      if (h2 && h2.match && h2.match(f))
        return d(h2.render(f));
      if (e.slots)
        return d(e.slots(f));
      if (e.item.formatter)
        return d(e.item.formatter(f));
      if (e.item.cellRender)
        return d(e.item.cellRender(f));
      if (e.item.render)
        console.warn("column.render 配置已废弃，请使用column.cellRender代替");
      else
        return (v = a.value) != null && v.name ? ((g = a.value) == null ? void 0 : g.show) === false ? void 0 : createVNode(resolveComponent("fs-component-render"), mergeProps({
          title: u.value,
          ref: i2
        }, a.value, {
          scope: f
        }), null) : d(toString_default(c));
    };
  }
});
var Ud = defineComponent({
  name: "FsEditableCell",
  inheritAttrs: false,
  props: {
    /**
     * 组件配置
     */
    item: {},
    scope: {},
    index: {},
    editableId: {},
    columnKey: {},
    editableCell: {
      type: Object
    },
    editableOpts: {
      type: Object
    },
    slots: {},
    disabled: {},
    readonly: {}
  },
  setup(e, t) {
    i.get();
    const {
      doComputed: n
    } = Nt();
    if (e.index === -1)
      return () => {
      };
    const r = n(() => {
      var h2;
      return (h2 = e.editableCell) == null ? void 0 : h2.getForm();
    }, () => e.scope), a = computed(() => {
      var h2;
      return r.value && r.value.show !== false && ((h2 = e.editableCell) == null ? void 0 : h2.isEditable());
    });
    function i2(h2) {
      h2 && a.value && e.editableCell.active();
    }
    async function s() {
      var h2;
      if (((h2 = e.editableOpts) == null ? void 0 : h2.mode) === "free") {
        await e.editableCell.persist();
        return;
      }
      await e.editableCell.save();
    }
    function u() {
      e.editableCell.cancel();
    }
    const l = computed(() => {
      var h2, v;
      return (((h2 = e.editableOpts) == null ? void 0 : h2.mode) === "cell" || ((v = e.editableOpts) == null ? void 0 : v.mode) === "free") && e.editableCell.showAction !== false;
    }), c = computed(() => e.editableCell.isChanged && e.editableCell.isChanged()), d = () => e.scope, f = {
      default: () => createVNode(resolveComponent("fs-cell"), mergeProps({
        ref: "targetRef",
        item: e.item,
        scope: e.scope,
        slots: e.slots
      }, t.attrs), null),
      edit: () => {
        var v, g;
        let h2 = null;
        return (v = e.editableCell) != null && v.isEditing && (r.value.blank === false || ((g = r.value.component) == null ? void 0 : g.show) === false ? h2 = null : r.value.conditionalRender && r.value.conditionalRender.match && r.value.conditionalRender.match(d()) ? h2 = createVNode(resolveComponent("fs-render"), mergeProps({
          "render-func": r.value.conditionalRender.render,
          scope: d()
        }, t.attrs), null) : r.value.render ? h2 = createVNode(resolveComponent("fs-render"), mergeProps({
          "render-func": r.value.render,
          scope: d()
        }, t.attrs), null) : h2 = createVNode(resolveComponent("fs-component-render"), mergeProps({
          ref: "targetInputRef"
        }, r.value.component, t.attrs, {
          scope: e.scope
        }), null)), h2;
      }
    };
    return () => {
      var g;
      if (!a.value || e.disabled || e.readonly)
        return createVNode(resolveComponent("fs-cell"), mergeProps({
          ref: "targetRef",
          item: e.item,
          scope: e.scope
        }, t.attrs), null);
      const h2 = e.editableCell, v = l.value ? (g = e.editableOpts) == null ? void 0 : g.activeTrigger : false;
      return createVNode(resolveComponent("fs-editable"), {
        ref: "editableRef",
        class: "fs-editable-cell",
        editing: h2 == null ? void 0 : h2.isEditing,
        showAction: l.value,
        dirty: c.value,
        "onUpdate:editing": i2,
        onSubmit: s,
        onCancel: u,
        loading: h2 == null ? void 0 : h2.loading,
        trigger: v,
        validateErrors: h2 == null ? void 0 : h2.validateErrors
      }, f);
    };
  },
  methods: {
    getTargetRef() {
      var e;
      return ((e = this.$refs.targetInputRef) == null ? void 0 : e.getTargetRef()) || this.$refs.targetRef;
    }
  }
});
var Kd = { class: "fs-editable" };
var Yd = { class: "fs-editable-input" };
var Gd = {
  key: 0,
  class: "fs-editable-dirty"
};
var Xd = {
  key: 0,
  class: "fs-editable-action fs-editable-icon"
};
var Qd = { class: "fs-editable-input" };
var Jd = { class: "fs-editable-action" };
var Zd = { class: "error-icon" };
var ef = defineComponent({
  __name: "fs-editable",
  props: {
    disabled: { type: Boolean, default: false },
    editing: { type: Boolean, default: false },
    dirty: { type: Boolean, default: false },
    trigger: { type: [String, Boolean], default: "onClick" },
    loading: { type: Boolean, default: false },
    showAction: { type: Boolean, default: true },
    validateErrors: { default: () => [] }
  },
  emits: ["update:editing", "submit", "cancel"],
  setup(e, { emit: t }) {
    const n = e, { ui: o } = B(), r = t, a = computed(() => n.trigger ? {
      [n.trigger]: () => {
        r("update:editing", true);
      }
    } : {});
    function i2() {
      r("submit");
    }
    function s() {
      r("cancel");
    }
    function u() {
      var c;
      return ((c = n.validateErrors) == null ? void 0 : c.length) > 0;
    }
    function l() {
      var c, d;
      if (((c = n.validateErrors) == null ? void 0 : c.length) !== 0)
        return (d = n.validateErrors) == null ? void 0 : d.map((f) => f.message).join(",");
    }
    return (c, d) => {
      const f = resolveComponent("fs-icon");
      return openBlock(), createElementBlock("div", Kd, [
        !c.editing || c.disabled ? (openBlock(), createElementBlock("div", mergeProps({
          key: 0,
          class: "fs-editable-inner fs-editable-pointer"
        }, a.value), [
          createBaseVNode("div", Yd, [
            c.dirty ? (openBlock(), createElementBlock("div", Gd)) : createCommentVNode("", true),
            renderSlot(c.$slots, "default")
          ]),
          c.trigger && !c.disabled ? (openBlock(), createElementBlock("div", Xd, [
            createVNode(f, {
              icon: unref(o).icons.edit
            }, null, 8, ["icon"])
          ])) : createCommentVNode("", true)
        ], 16)) : (openBlock(), createElementBlock("div", {
          key: 1,
          class: normalizeClass(["fs-editable-inner", { "fs-validate-error": u() }])
        }, [
          createBaseVNode("div", Qd, [
            renderSlot(c.$slots, "edit")
          ]),
          createBaseVNode("div", Jd, [
            (openBlock(), createBlock(resolveDynamicComponent(unref(o).tooltip.name), null, {
              [unref(o).tooltip.content]: withCtx(() => [
                createBaseVNode("span", Zd, toDisplayString(l()), 1)
              ]),
              [unref(o).tooltip.trigger]: withCtx(() => [
                createVNode(f, {
                  class: normalizeClass({ hidden: !u(), "error-icon": true }),
                  size: "mini",
                  icon: unref(o).icons.info
                }, null, 8, ["class", "icon"])
              ]),
              _: 2
            }, 1024)),
            c.showAction ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
              c.loading ? (openBlock(), createBlock(f, {
                key: 0,
                size: "mini",
                spin: true,
                icon: unref(o).icons.refresh
              }, null, 8, ["icon"])) : (openBlock(), createBlock(f, {
                key: 1,
                size: "mini",
                icon: unref(o).icons.check,
                onClick: i2
              }, null, 8, ["icon"])),
              createVNode(f, {
                class: normalizeClass({ hidden: c.loading }),
                size: "mini",
                icon: unref(o).icons.close,
                onClick: s
              }, null, 8, ["class", "icon"])
            ], 64)) : createCommentVNode("", true)
          ])
        ], 2))
      ]);
    };
  }
});
var tf = defineComponent({
  name: "FsActionbar",
  props: {
    /**
     * 按钮配置
     * {
     *  add:{
     *    ...FsButton,
     *    show:true
     *  },
     *  custom:{...}
     * }
     */
    buttons: {}
  },
  emits: ["action"],
  setup(e, t) {
    function n(r, a, i2) {
      const s = { key: r, btn: a, $event: i2 };
      if (a.click) {
        a.click(s);
        return;
      }
      if (a.onClick) {
        a.onClick(s);
        return;
      }
      t.emit("action", s);
    }
    const o = computed(() => {
      let r = [];
      for (let i2 in e.buttons)
        r.push({
          // @ts-ignore
          ...e.buttons[i2],
          _key: i2
        });
      r = sortBy_default(r, (i2) => i2.order ?? nn.orderDefault);
      const a = {};
      return r.forEach((i2) => {
        let s = i2._key;
        delete i2._key, a[s] = i2;
      }), a;
    });
    return {
      onClick: n,
      computedButtons: o
    };
  }
});
var nf = { class: "fs-actionbar" };
function of(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-button");
  return openBlock(), createElementBlock("div", nf, [
    (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedButtons, (s, u) => (openBlock(), createElementBlock(Fragment, { key: u }, [
      s.show !== false ? (openBlock(), createBlock(i2, mergeProps({
        key: 0,
        ref_for: true
      }, s, {
        onClick: (l) => e.onClick(u, s, l)
      }), null, 16, ["onClick"])) : createCommentVNode("", true)
    ], 64))), 128))
  ]);
}
var rf = ke(tf, [["render", of]]);
var ap = class {
  constructor() {
    se(this, "valueChange");
    se(this, "mergeForm");
  }
};
var Ur = "ColumnsFilterContext";
var af = defineComponent({
  __name: "index",
  props: {
    container: { default: () => ({ is: "fs-columns-filter-layout-default" }) },
    is: {},
    show: { type: Boolean },
    mode: { default: "default" },
    columns: {},
    originalColumns: {},
    storage: { type: [Boolean, String, Object], default: true },
    text: {},
    onReset: {},
    onSubmit: {}
  },
  emits: ["update:columns", "update:show", "reset", "submit"],
  setup(e, { expose: t, emit: n }) {
    const o = e, r = n, { t: a } = ot(), i2 = i.get(), s = ref(false), u = () => {
      s.value = true;
    }, l = computed(() => y(o.originalColumns)), c = ref([]), d = computed(() => {
      const G = {};
      return v(l.value, (p) => {
        G[p.__key] = p;
      }), G;
    }), { merge: f } = De(), h2 = computed(() => {
      const G = {
        title: a("fs.toolbar.columnFilter.title"),
        fixed: a("fs.toolbar.columnFilter.fixed"),
        order: a("fs.toolbar.columnFilter.order"),
        reset: a("fs.toolbar.columnFilter.reset"),
        confirm: a("fs.toolbar.columnFilter.confirm"),
        unnamed: a("fs.toolbar.columnFilter.unnamed")
      };
      return f(G, o.text), G;
    });
    function v(G, p) {
      if (G)
        if (Array.isArray(G))
          for (const _ of G)
            p(_), _.children && v(_.children, p);
        else
          for (const _ in G) {
            const C = G[_];
            p(C), C.children && v(C.children, p);
          }
    }
    function g(G) {
      return {
        key: G.key,
        title: G.title,
        fixed: G.fixed ?? false,
        show: G.show ?? true,
        __show: G.columnSetShow !== false,
        __disabled: G.columnSetDisabled ?? false
      };
    }
    function w(G) {
      const p = {};
      return forEach_default(G, (_) => {
        const C = omit_default(_, "children", "__show", "__disabled", "__parent", "__key");
        _.children && _.children.length > 0 && (C.children = w(_.children)), p[_.key] = C;
      }), p;
    }
    function y(G, p) {
      const _ = [];
      return forEach_default(G, (C) => {
        const S = g(C);
        S.__parent = p, S.__key = `${(p == null ? void 0 : p.key) || ""}.${C.key}`, _.push(S), C.children && (S.children = y(C.children, S));
      }), _;
    }
    function R(G) {
      c.value = y(G);
    }
    async function F() {
      c.value = y(o.originalColumns), await k(true), await X(), r("reset");
    }
    async function k(G = false) {
      G || await O(c.value);
      const p = cloneDeep_default(c.value);
      return v(p, (_) => {
        _ && (delete _.__disabled, delete _.__show, delete _.__parent, delete _.__key);
      }), D(p), s.value = false, p;
    }
    async function A(G = false) {
      const p = await k(G);
      r("submit", { columns: p });
    }
    const N = computed(() => o.originalColumns);
    provide(Ur, {
      originalColumns: N,
      originalColumnsMap: d,
      currentColumns: c,
      text: h2,
      active: s,
      submit: A,
      reset: F
    });
    async function $() {
      await A(false), r("update:show", false);
    }
    async function B2() {
      await F(), r("update:show", false);
    }
    function D(G) {
      r("update:columns", w(G));
    }
    const x = inject("get:crudBinding", () => ({ id: "" })), I = ref();
    function V() {
      const G = typeof o.storage == "object" ? o.storage : null, p = typeof o.storage == "string" ? o.storage : "";
      if (I.value == null) {
        const _ = useRoute();
        I.value = new Ba({
          $router: _,
          tableName: "columnsFilter",
          keyType: p,
          remoteStorage: G
        });
      }
      return I.value.id = x().id, I.value;
    }
    async function O(G) {
      if (o.storage === false)
        return;
      const p = cloneDeep_default(G);
      v(p, (_) => {
        delete _.__parent;
      }), await V().updateTableValue(p);
    }
    async function M() {
      if (o.storage !== false)
        return await V().getTableValue();
    }
    async function X() {
      await V().clearTableValue();
    }
    function le(G) {
      const p = [];
      v(G, (C) => {
        const S = pick_default(C, "key", "__show", "__disabled");
        p.push(JSON.stringify(S));
      }), p.sort();
      let _ = "";
      for (const C of p)
        _ += C;
      return _;
    }
    watch(
      () => o.columns,
      (G) => {
        R(G);
      }
    );
    const ye = async () => {
      R(o.columns);
      const G = await M();
      if (G) {
        const p = le(G);
        if (le(l.value) !== p)
          return;
        c.value = G, await nextTick(), await A(true);
      }
    };
    async function me(G) {
      await G(c), await k();
    }
    watch(
      () => l.value,
      async (G) => {
        await ye();
      },
      {
        immediate: true
      }
    ), t({
      start: u,
      save: k,
      update: me,
      original: l,
      columns: c
    });
    function Se(G) {
      return G.label || G.title || G.key || h2.value.unnamed;
    }
    return (G, p) => {
      var C;
      const _ = resolveComponent("fs-button");
      return G.mode === "simple" ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        (openBlock(), createBlock(resolveDynamicComponent(unref(i2).row.name), { class: "fs-table-columns-filter-simple" }, {
          default: withCtx(() => [
            (openBlock(true), createElementBlock(Fragment, null, renderList(c.value, (S, T) => {
              var ne;
              return withDirectives((openBlock(), createBlock(resolveDynamicComponent(unref(i2).col.name), {
                key: T,
                span: 6
              }, {
                default: withCtx(() => {
                  var P;
                  return [
                    (openBlock(), createBlock(resolveDynamicComponent(unref(i2).checkbox.name), normalizeProps({
                      [unref(i2).checkbox.modelValue]: S.show,
                      ["onUpdate:" + unref(i2).checkbox.modelValue]: (W) => S.show = W,
                      disabled: ((P = l.value[S.key]) == null ? void 0 : P.__disabled) === true,
                      class: "item-label",
                      title: Se(S)
                    }), {
                      default: withCtx(() => [
                        createTextVNode(toDisplayString(Se(S)), 1)
                      ]),
                      _: 2
                    }, 1040, ["disabled", "title"]))
                  ];
                }),
                _: 2
              }, 1024)), [
                [vShow, ((ne = l.value[S.key]) == null ? void 0 : ne.__show) !== false]
              ]);
            }), 128))
          ]),
          _: 1
        })),
        (openBlock(), createBlock(resolveDynamicComponent(unref(i2).divider.name))),
        (openBlock(), createBlock(resolveDynamicComponent(unref(i2).row.name), null, {
          default: withCtx(() => [
            createVNode(_, {
              style: { "margin-right": "5px" },
              type: "primary",
              icon: unref(i2).icons.check,
              text: h2.value.confirm,
              onClick: p[0] || (p[0] = (S) => $())
            }, null, 8, ["icon", "text"]),
            createVNode(_, {
              icon: unref(i2).icons.refresh,
              text: h2.value.reset,
              onClick: B2
            }, null, 8, ["icon", "text"])
          ]),
          _: 1
        }))
      ], 64)) : (openBlock(), createBlock(resolveDynamicComponent(((C = G.container) == null ? void 0 : C.is) || "fs-columns-filter-layout-default"), normalizeProps(mergeProps({ key: 1 }, G.container)), {
        buttons: withCtx(() => [
          (openBlock(), createBlock(resolveDynamicComponent(unref(i2).row.name), {
            class: "fs-drawer-footer",
            gutter: 10
          }, {
            default: withCtx(() => [
              (openBlock(), createBlock(resolveDynamicComponent(unref(i2).col.name), { span: 12 }, {
                default: withCtx(() => [
                  createVNode(_, {
                    icon: unref(i2).icons.refresh,
                    text: h2.value.reset,
                    block: "",
                    onClick: F
                  }, null, 8, ["icon", "text"])
                ]),
                _: 1
              })),
              (openBlock(), createBlock(resolveDynamicComponent(unref(i2).col.name), { span: 12 }, {
                default: withCtx(() => [
                  createVNode(_, {
                    type: "primary",
                    icon: unref(i2).icons.check,
                    text: h2.value.confirm,
                    block: "",
                    onClick: p[1] || (p[1] = (S) => A(false))
                  }, null, 8, ["icon", "text"])
                ]),
                _: 1
              }))
            ]),
            _: 1
          }))
        ]),
        _: 1
      }, 16));
    };
  }
});
var sf = defineComponent({
  name: "FsToolbar",
  components: { FsTableColumnsFilter: af },
  props: {
    /**
     * 按钮配置
     *{
     *   search:{}, 查询
     *   refresh:{}, 刷新
     *   compact:{}, 紧凑模式
     *   export:{}, 导出
     *   columns:{} 列设置
     *}
     **/
    buttons: {
      type: Object
    },
    /**
     * 当前是否紧凑模式
     */
    compact: {
      type: Boolean,
      default: true
    },
    /**
     * 列配置
     */
    columns: {
      type: Object,
      default: void 0
    },
    /**
     * 是否保存用户列设置
     * 传string则表示传入缓存的主key
     */
    storage: {
      type: [String, Boolean],
      default: true
    },
    /**
     * 插槽
     */
    slots: {},
    /**
     * 列设置配置
     */
    columnsFilter: {
      type: Object
    }
  },
  emits: ["update:columns"],
  setup(e, t) {
    ot();
    const n = ref(), { ui: o } = B(), { merge: r } = De(), a = computed(() => {
      const u = {
        columns: {
          click: () => {
            n.value.start();
          }
        }
      };
      r(u, e.buttons);
      let l = [];
      for (let d in u)
        l.push({
          ...u[d],
          _key: d
        });
      l = sortBy_default(l, (d) => d.order ?? nn.orderDefault);
      const c = {};
      return l.forEach((d) => {
        let f = d._key;
        delete d._key, c[f] = d;
      }), c;
    }), i2 = ref(false);
    return {
      ui: o,
      columnsFilterRef: n,
      computedButtons: a,
      popoverVisible: i2,
      handleSimpleClick: () => {
        o.type !== "element" && (i2.value = !i2.value);
      }
    };
  }
});
var lf = { class: "fs-toolbar" };
function uf(e, t, n, o, r, a) {
  var s;
  const i2 = resolveComponent("fs-button");
  return openBlock(), createElementBlock("div", lf, [
    (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedButtons, (u, l) => {
      var c;
      return openBlock(), createElementBlock(Fragment, { key: l }, [
        u.show !== false ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
          l === "columns" && e.columnsFilter && ((c = e.columnsFilter) == null ? void 0 : c.mode) === "simple" ? (openBlock(), createBlock(resolveDynamicComponent(e.ui.popover.name), normalizeProps({
            key: 0,
            [e.ui.popover.visible]: e.popoverVisible,
            ["onUpdate:" + e.ui.popover.visible]: t[2] || (t[2] = (d) => e.popoverVisible = d),
            "display-directive": "show",
            placement: "bottom",
            width: 760,
            trigger: "click"
          }), {
            [e.ui.popover.triggerSlotName]: withCtx(() => [
              createVNode(i2, mergeProps({ ref_for: true }, u, { onClick: e.handleSimpleClick }), null, 16, ["onClick"])
            ]),
            [e.ui.popover.contentSlotName]: withCtx(() => [
              e.columns ? (openBlock(), createBlock(resolveDynamicComponent(e.columnsFilter.is || "fs-table-columns-filter"), mergeProps({
                key: 0,
                ref_for: true,
                ref: "columnsFilterRef",
                show: e.popoverVisible,
                "onUpdate:show": t[0] || (t[0] = (d) => e.popoverVisible = d),
                mode: "simple"
              }, e.columnsFilter, {
                columns: e.columns,
                storage: e.storage,
                "onUpdate:columns": t[1] || (t[1] = (d) => e.$emit("update:columns", d))
              }), null, 16, ["show", "columns", "storage"])) : createCommentVNode("", true)
            ]),
            _: 2
          }, 1040)) : (openBlock(), createBlock(i2, mergeProps({
            key: 1,
            ref_for: true
          }, u, {
            onClick: (d) => u.click()
          }), null, 16, ["onClick"]))
        ], 64)) : createCommentVNode("", true)
      ], 64);
    }), 128)),
    e.columns && ((s = e.columnsFilter) == null ? void 0 : s.mode) !== "simple" ? (openBlock(), createBlock(resolveDynamicComponent(e.columnsFilter.is || "fs-table-columns-filter"), mergeProps({
      key: 0,
      ref: "columnsFilterRef",
      storage: e.storage
    }, e.columnsFilter, {
      columns: e.columns,
      "onUpdate:columns": t[3] || (t[3] = (u) => e.$emit("update:columns", u))
    }), null, 16, ["storage", "columns"])) : createCommentVNode("", true)
  ]);
}
var cf = ke(sf, [["render", uf]]);
function fa(e, t) {
  var n = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o = Object.getOwnPropertySymbols(e);
    t && (o = o.filter(function(r) {
      return Object.getOwnPropertyDescriptor(e, r).enumerable;
    })), n.push.apply(n, o);
  }
  return n;
}
function jt(e) {
  for (var t = 1; t < arguments.length; t++) {
    var n = arguments[t] != null ? arguments[t] : {};
    t % 2 ? fa(Object(n), true).forEach(function(o) {
      df(e, o, n[o]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(n)) : fa(Object(n)).forEach(function(o) {
      Object.defineProperty(e, o, Object.getOwnPropertyDescriptor(n, o));
    });
  }
  return e;
}
function ko(e) {
  "@babel/helpers - typeof";
  return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? ko = function(t) {
    return typeof t;
  } : ko = function(t) {
    return t && typeof Symbol == "function" && t.constructor === Symbol && t !== Symbol.prototype ? "symbol" : typeof t;
  }, ko(e);
}
function df(e, t, n) {
  return t in e ? Object.defineProperty(e, t, {
    value: n,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[t] = n, e;
}
function zt() {
  return zt = Object.assign || function(e) {
    for (var t = 1; t < arguments.length; t++) {
      var n = arguments[t];
      for (var o in n)
        Object.prototype.hasOwnProperty.call(n, o) && (e[o] = n[o]);
    }
    return e;
  }, zt.apply(this, arguments);
}
function ff(e, t) {
  if (e == null)
    return {};
  var n = {}, o = Object.keys(e), r, a;
  for (a = 0; a < o.length; a++)
    r = o[a], !(t.indexOf(r) >= 0) && (n[r] = e[r]);
  return n;
}
function hf(e, t) {
  if (e == null)
    return {};
  var n = ff(e, t), o, r;
  if (Object.getOwnPropertySymbols) {
    var a = Object.getOwnPropertySymbols(e);
    for (r = 0; r < a.length; r++)
      o = a[r], !(t.indexOf(o) >= 0) && Object.prototype.propertyIsEnumerable.call(e, o) && (n[o] = e[o]);
  }
  return n;
}
var mf = "1.14.0";
function Ht(e) {
  if (typeof window < "u" && window.navigator)
    return !!navigator.userAgent.match(e);
}
var Wt = Ht(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i);
var fo = Ht(/Edge/i);
var ha = Ht(/firefox/i);
var Kn = Ht(/safari/i) && !Ht(/chrome/i) && !Ht(/android/i);
var pi = Ht(/iP(ad|od|hone)/i);
var pf = Ht(/chrome/i) && Ht(/android/i);
var gi = {
  capture: false,
  passive: false
};
function Re(e, t, n) {
  e.addEventListener(t, n, !Wt && gi);
}
function Fe(e, t, n) {
  e.removeEventListener(t, n, !Wt && gi);
}
function Bo(e, t) {
  if (t) {
    if (t[0] === ">" && (t = t.substring(1)), e)
      try {
        if (e.matches)
          return e.matches(t);
        if (e.msMatchesSelector)
          return e.msMatchesSelector(t);
        if (e.webkitMatchesSelector)
          return e.webkitMatchesSelector(t);
      } catch {
        return false;
      }
    return false;
  }
}
function gf(e) {
  return e.host && e !== document && e.host.nodeType ? e.host : e.parentNode;
}
function Tt(e, t, n, o) {
  if (e) {
    n = n || document;
    do {
      if (t != null && (t[0] === ">" ? e.parentNode === n && Bo(e, t) : Bo(e, t)) || o && e === n)
        return e;
      if (e === n)
        break;
    } while (e = gf(e));
  }
  return null;
}
var ma = /\s+/g;
function it(e, t, n) {
  if (e && t)
    if (e.classList)
      e.classList[n ? "add" : "remove"](t);
    else {
      var o = (" " + e.className + " ").replace(ma, " ").replace(" " + t + " ", " ");
      e.className = (o + (n ? " " + t : "")).replace(ma, " ");
    }
}
function fe(e, t, n) {
  var o = e && e.style;
  if (o) {
    if (n === void 0)
      return document.defaultView && document.defaultView.getComputedStyle ? n = document.defaultView.getComputedStyle(e, "") : e.currentStyle && (n = e.currentStyle), t === void 0 ? n : n[t];
    !(t in o) && t.indexOf("webkit") === -1 && (t = "-webkit-" + t), o[t] = n + (typeof n == "string" ? "" : "px");
  }
}
function kn(e, t) {
  var n = "";
  if (typeof e == "string")
    n = e;
  else
    do {
      var o = fe(e, "transform");
      o && o !== "none" && (n = o + " " + n);
    } while (!t && (e = e.parentNode));
  var r = window.DOMMatrix || window.WebKitCSSMatrix || window.CSSMatrix || window.MSCSSMatrix;
  return r && new r(n);
}
function vi(e, t, n) {
  if (e) {
    var o = e.getElementsByTagName(t), r = 0, a = o.length;
    if (n)
      for (; r < a; r++)
        n(o[r], r);
    return o;
  }
  return [];
}
function Bt() {
  var e = document.scrollingElement;
  return e || document.documentElement;
}
function xe(e, t, n, o, r) {
  if (!(!e.getBoundingClientRect && e !== window)) {
    var a, i2, s, u, l, c, d;
    if (e !== window && e.parentNode && e !== Bt() ? (a = e.getBoundingClientRect(), i2 = a.top, s = a.left, u = a.bottom, l = a.right, c = a.height, d = a.width) : (i2 = 0, s = 0, u = window.innerHeight, l = window.innerWidth, c = window.innerHeight, d = window.innerWidth), (t || n) && e !== window && (r = r || e.parentNode, !Wt))
      do
        if (r && r.getBoundingClientRect && (fe(r, "transform") !== "none" || n && fe(r, "position") !== "static")) {
          var f = r.getBoundingClientRect();
          i2 -= f.top + parseInt(fe(r, "border-top-width")), s -= f.left + parseInt(fe(r, "border-left-width")), u = i2 + a.height, l = s + a.width;
          break;
        }
      while (r = r.parentNode);
    if (o && e !== window) {
      var h2 = kn(r || e), v = h2 && h2.a, g = h2 && h2.d;
      h2 && (i2 /= g, s /= v, d /= v, c /= g, u = i2 + c, l = s + d);
    }
    return {
      top: i2,
      left: s,
      bottom: u,
      right: l,
      width: d,
      height: c
    };
  }
}
function pa(e, t, n) {
  for (var o = Zt(e, true), r = xe(e)[t]; o; ) {
    var a = xe(o)[n], i2 = void 0;
    if (n === "top" || n === "left" ? i2 = r >= a : i2 = r <= a, !i2)
      return o;
    if (o === Bt())
      break;
    o = Zt(o, false);
  }
  return false;
}
function Pn(e, t, n, o) {
  for (var r = 0, a = 0, i2 = e.children; a < i2.length; ) {
    if (i2[a].style.display !== "none" && i2[a] !== he.ghost && (o || i2[a] !== he.dragged) && Tt(i2[a], n.draggable, e, false)) {
      if (r === t)
        return i2[a];
      r++;
    }
    a++;
  }
  return null;
}
function Kr(e, t) {
  for (var n = e.lastElementChild; n && (n === he.ghost || fe(n, "display") === "none" || t && !Bo(n, t)); )
    n = n.previousElementSibling;
  return n || null;
}
function bt(e, t) {
  var n = 0;
  if (!e || !e.parentNode)
    return -1;
  for (; e = e.previousElementSibling; )
    e.nodeName.toUpperCase() !== "TEMPLATE" && e !== he.clone && (!t || Bo(e, t)) && n++;
  return n;
}
function ga(e) {
  var t = 0, n = 0, o = Bt();
  if (e)
    do {
      var r = kn(e), a = r.a, i2 = r.d;
      t += e.scrollLeft * a, n += e.scrollTop * i2;
    } while (e !== o && (e = e.parentNode));
  return [t, n];
}
function vf(e, t) {
  for (var n in e)
    if (e.hasOwnProperty(n)) {
      for (var o in t)
        if (t.hasOwnProperty(o) && t[o] === e[n][o])
          return Number(n);
    }
  return -1;
}
function Zt(e, t) {
  if (!e || !e.getBoundingClientRect)
    return Bt();
  var n = e, o = false;
  do
    if (n.clientWidth < n.scrollWidth || n.clientHeight < n.scrollHeight) {
      var r = fe(n);
      if (n.clientWidth < n.scrollWidth && (r.overflowX == "auto" || r.overflowX == "scroll") || n.clientHeight < n.scrollHeight && (r.overflowY == "auto" || r.overflowY == "scroll")) {
        if (!n.getBoundingClientRect || n === document.body)
          return Bt();
        if (o || t)
          return n;
        o = true;
      }
    }
  while (n = n.parentNode);
  return Bt();
}
function bf(e, t) {
  if (e && t)
    for (var n in t)
      t.hasOwnProperty(n) && (e[n] = t[n]);
  return e;
}
function or(e, t) {
  return Math.round(e.top) === Math.round(t.top) && Math.round(e.left) === Math.round(t.left) && Math.round(e.height) === Math.round(t.height) && Math.round(e.width) === Math.round(t.width);
}
var Yn;
function bi(e, t) {
  return function() {
    if (!Yn) {
      var n = arguments, o = this;
      n.length === 1 ? e.call(o, n[0]) : e.apply(o, n), Yn = setTimeout(function() {
        Yn = void 0;
      }, t);
    }
  };
}
function yf() {
  clearTimeout(Yn), Yn = void 0;
}
function yi(e, t, n) {
  e.scrollLeft += t, e.scrollTop += n;
}
function wi(e) {
  var t = window.Polymer, n = window.jQuery || window.Zepto;
  return t && t.dom ? t.dom(e).cloneNode(true) : n ? n(e).clone(true)[0] : e.cloneNode(true);
}
var ht = "Sortable" + (/* @__PURE__ */ new Date()).getTime();
function wf() {
  var e = [], t;
  return {
    captureAnimationState: function() {
      if (e = [], !!this.options.animation) {
        var o = [].slice.call(this.el.children);
        o.forEach(function(r) {
          if (!(fe(r, "display") === "none" || r === he.ghost)) {
            e.push({
              target: r,
              rect: xe(r)
            });
            var a = jt({}, e[e.length - 1].rect);
            if (r.thisAnimationDuration) {
              var i2 = kn(r, true);
              i2 && (a.top -= i2.f, a.left -= i2.e);
            }
            r.fromRect = a;
          }
        });
      }
    },
    addAnimationState: function(o) {
      e.push(o);
    },
    removeAnimationState: function(o) {
      e.splice(vf(e, {
        target: o
      }), 1);
    },
    animateAll: function(o) {
      var r = this;
      if (!this.options.animation) {
        clearTimeout(t), typeof o == "function" && o();
        return;
      }
      var a = false, i2 = 0;
      e.forEach(function(s) {
        var u = 0, l = s.target, c = l.fromRect, d = xe(l), f = l.prevFromRect, h2 = l.prevToRect, v = s.rect, g = kn(l, true);
        g && (d.top -= g.f, d.left -= g.e), l.toRect = d, l.thisAnimationDuration && or(f, d) && !or(c, d) && // Make sure animatingRect is on line between toRect & fromRect
        (v.top - d.top) / (v.left - d.left) === (c.top - d.top) / (c.left - d.left) && (u = Cf(v, f, h2, r.options)), or(d, c) || (l.prevFromRect = c, l.prevToRect = d, u || (u = r.options.animation), r.animate(l, v, d, u)), u && (a = true, i2 = Math.max(i2, u), clearTimeout(l.animationResetTimer), l.animationResetTimer = setTimeout(function() {
          l.animationTime = 0, l.prevFromRect = null, l.fromRect = null, l.prevToRect = null, l.thisAnimationDuration = null;
        }, u), l.thisAnimationDuration = u);
      }), clearTimeout(t), a ? t = setTimeout(function() {
        typeof o == "function" && o();
      }, i2) : typeof o == "function" && o(), e = [];
    },
    animate: function(o, r, a, i2) {
      if (i2) {
        fe(o, "transition", ""), fe(o, "transform", "");
        var s = kn(this.el), u = s && s.a, l = s && s.d, c = (r.left - a.left) / (u || 1), d = (r.top - a.top) / (l || 1);
        o.animatingX = !!c, o.animatingY = !!d, fe(o, "transform", "translate3d(" + c + "px," + d + "px,0)"), this.forRepaintDummy = _f(o), fe(o, "transition", "transform " + i2 + "ms" + (this.options.easing ? " " + this.options.easing : "")), fe(o, "transform", "translate3d(0,0,0)"), typeof o.animated == "number" && clearTimeout(o.animated), o.animated = setTimeout(function() {
          fe(o, "transition", ""), fe(o, "transform", ""), o.animated = false, o.animatingX = false, o.animatingY = false;
        }, i2);
      }
    }
  };
}
function _f(e) {
  return e.offsetWidth;
}
function Cf(e, t, n, o) {
  return Math.sqrt(Math.pow(t.top - e.top, 2) + Math.pow(t.left - e.left, 2)) / Math.sqrt(Math.pow(t.top - n.top, 2) + Math.pow(t.left - n.left, 2)) * o.animation;
}
var Cn = [];
var rr = {
  initializeByDefault: true
};
var ho = {
  mount: function(t) {
    for (var n in rr)
      rr.hasOwnProperty(n) && !(n in t) && (t[n] = rr[n]);
    Cn.forEach(function(o) {
      if (o.pluginName === t.pluginName)
        throw "Sortable: Cannot mount plugin ".concat(t.pluginName, " more than once");
    }), Cn.push(t);
  },
  pluginEvent: function(t, n, o) {
    var r = this;
    this.eventCanceled = false, o.cancel = function() {
      r.eventCanceled = true;
    };
    var a = t + "Global";
    Cn.forEach(function(i2) {
      n[i2.pluginName] && (n[i2.pluginName][a] && n[i2.pluginName][a](jt({
        sortable: n
      }, o)), n.options[i2.pluginName] && n[i2.pluginName][t] && n[i2.pluginName][t](jt({
        sortable: n
      }, o)));
    });
  },
  initializePlugins: function(t, n, o, r) {
    Cn.forEach(function(s) {
      var u = s.pluginName;
      if (!(!t.options[u] && !s.initializeByDefault)) {
        var l = new s(t, n, t.options);
        l.sortable = t, l.options = t.options, t[u] = l, zt(o, l.defaults);
      }
    });
    for (var a in t.options)
      if (t.options.hasOwnProperty(a)) {
        var i2 = this.modifyOption(t, a, t.options[a]);
        typeof i2 < "u" && (t.options[a] = i2);
      }
  },
  getEventProperties: function(t, n) {
    var o = {};
    return Cn.forEach(function(r) {
      typeof r.eventProperties == "function" && zt(o, r.eventProperties.call(n[r.pluginName], t));
    }), o;
  },
  modifyOption: function(t, n, o) {
    var r;
    return Cn.forEach(function(a) {
      t[a.pluginName] && a.optionListeners && typeof a.optionListeners[n] == "function" && (r = a.optionListeners[n].call(t[a.pluginName], o));
    }), r;
  }
};
function Sf(e) {
  var t = e.sortable, n = e.rootEl, o = e.name, r = e.targetEl, a = e.cloneEl, i2 = e.toEl, s = e.fromEl, u = e.oldIndex, l = e.newIndex, c = e.oldDraggableIndex, d = e.newDraggableIndex, f = e.originalEvent, h2 = e.putSortable, v = e.extraEventProperties;
  if (t = t || n && n[ht], !!t) {
    var g, w = t.options, y = "on" + o.charAt(0).toUpperCase() + o.substr(1);
    window.CustomEvent && !Wt && !fo ? g = new CustomEvent(o, {
      bubbles: true,
      cancelable: true
    }) : (g = document.createEvent("Event"), g.initEvent(o, true, true)), g.to = i2 || n, g.from = s || n, g.item = r || n, g.clone = a, g.oldIndex = u, g.newIndex = l, g.oldDraggableIndex = c, g.newDraggableIndex = d, g.originalEvent = f, g.pullMode = h2 ? h2.lastPutMode : void 0;
    var R = jt(jt({}, v), ho.getEventProperties(o, t));
    for (var F in R)
      g[F] = R[F];
    n && n.dispatchEvent(g), w[y] && w[y].call(t, g);
  }
}
var Ff = ["evt"];
var rt = function(t, n) {
  var o = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {}, r = o.evt, a = hf(o, Ff);
  ho.pluginEvent.bind(he)(t, n, jt({
    dragEl: U,
    parentEl: Ie,
    ghostEl: be,
    rootEl: Ae,
    nextEl: cn,
    lastDownEl: $o,
    cloneEl: Ve,
    cloneHidden: Qt,
    dragStarted: Hn,
    putSortable: Ke,
    activeSortable: he.active,
    originalEvent: r,
    oldIndex: Dn,
    oldDraggableIndex: Gn,
    newIndex: st,
    newDraggableIndex: Xt,
    hideGhostForTarget: Fi,
    unhideGhostForTarget: Ri,
    cloneNowHidden: function() {
      Qt = true;
    },
    cloneNowShown: function() {
      Qt = false;
    },
    dispatchSortableEvent: function(s) {
      Ze({
        sortable: n,
        name: s,
        originalEvent: r
      });
    }
  }, a));
};
function Ze(e) {
  Sf(jt({
    putSortable: Ke,
    cloneEl: Ve,
    targetEl: U,
    rootEl: Ae,
    oldIndex: Dn,
    oldDraggableIndex: Gn,
    newIndex: st,
    newDraggableIndex: Xt
  }, e));
}
var U;
var Ie;
var be;
var Ae;
var cn;
var $o;
var Ve;
var Qt;
var Dn;
var st;
var Gn;
var Xt;
var bo;
var Ke;
var Fn = false;
var jo = false;
var No = [];
var ln;
var wt;
var ar;
var ir;
var va;
var ba;
var Hn;
var Sn;
var Xn;
var Qn = false;
var yo = false;
var Oo;
var Ye;
var sr = [];
var Ar = false;
var Lo = [];
var Qo = typeof document < "u";
var wo = pi;
var ya = fo || Wt ? "cssFloat" : "float";
var Rf = Qo && !pf && !pi && "draggable" in document.createElement("div");
var _i = function() {
  if (Qo) {
    if (Wt)
      return false;
    var e = document.createElement("x");
    return e.style.cssText = "pointer-events:auto", e.style.pointerEvents === "auto";
  }
}();
var Ci = function(t, n) {
  var o = fe(t), r = parseInt(o.width) - parseInt(o.paddingLeft) - parseInt(o.paddingRight) - parseInt(o.borderLeftWidth) - parseInt(o.borderRightWidth), a = Pn(t, 0, n), i2 = Pn(t, 1, n), s = a && fe(a), u = i2 && fe(i2), l = s && parseInt(s.marginLeft) + parseInt(s.marginRight) + xe(a).width, c = u && parseInt(u.marginLeft) + parseInt(u.marginRight) + xe(i2).width;
  if (o.display === "flex")
    return o.flexDirection === "column" || o.flexDirection === "column-reverse" ? "vertical" : "horizontal";
  if (o.display === "grid")
    return o.gridTemplateColumns.split(" ").length <= 1 ? "vertical" : "horizontal";
  if (a && s.float && s.float !== "none") {
    var d = s.float === "left" ? "left" : "right";
    return i2 && (u.clear === "both" || u.clear === d) ? "vertical" : "horizontal";
  }
  return a && (s.display === "block" || s.display === "flex" || s.display === "table" || s.display === "grid" || l >= r && o[ya] === "none" || i2 && o[ya] === "none" && l + c > r) ? "vertical" : "horizontal";
};
var Df = function(t, n, o) {
  var r = o ? t.left : t.top, a = o ? t.right : t.bottom, i2 = o ? t.width : t.height, s = o ? n.left : n.top, u = o ? n.right : n.bottom, l = o ? n.width : n.height;
  return r === s || a === u || r + i2 / 2 === s + l / 2;
};
var kf = function(t, n) {
  var o;
  return No.some(function(r) {
    var a = r[ht].options.emptyInsertThreshold;
    if (!(!a || Kr(r))) {
      var i2 = xe(r), s = t >= i2.left - a && t <= i2.right + a, u = n >= i2.top - a && n <= i2.bottom + a;
      if (s && u)
        return o = r;
    }
  }), o;
};
var Si = function(t) {
  function n(a, i2) {
    return function(s, u, l, c) {
      var d = s.options.group.name && u.options.group.name && s.options.group.name === u.options.group.name;
      if (a == null && (i2 || d))
        return true;
      if (a == null || a === false)
        return false;
      if (i2 && a === "clone")
        return a;
      if (typeof a == "function")
        return n(a(s, u, l, c), i2)(s, u, l, c);
      var f = (i2 ? s : u).options.group.name;
      return a === true || typeof a == "string" && a === f || a.join && a.indexOf(f) > -1;
    };
  }
  var o = {}, r = t.group;
  (!r || ko(r) != "object") && (r = {
    name: r
  }), o.name = r.name, o.checkPull = n(r.pull, true), o.checkPut = n(r.put), o.revertClone = r.revertClone, t.group = o;
};
var Fi = function() {
  !_i && be && fe(be, "display", "none");
};
var Ri = function() {
  !_i && be && fe(be, "display", "");
};
Qo && document.addEventListener("click", function(e) {
  if (jo)
    return e.preventDefault(), e.stopPropagation && e.stopPropagation(), e.stopImmediatePropagation && e.stopImmediatePropagation(), jo = false, false;
}, true);
var un = function(t) {
  if (U) {
    t = t.touches ? t.touches[0] : t;
    var n = kf(t.clientX, t.clientY);
    if (n) {
      var o = {};
      for (var r in t)
        t.hasOwnProperty(r) && (o[r] = t[r]);
      o.target = o.rootEl = n, o.preventDefault = void 0, o.stopPropagation = void 0, n[ht]._onDragOver(o);
    }
  }
};
var $f = function(t) {
  U && U.parentNode[ht]._isOutsideThisEl(t.target);
};
function he(e, t) {
  if (!(e && e.nodeType && e.nodeType === 1))
    throw "Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(e));
  this.el = e, this.options = t = zt({}, t), e[ht] = this;
  var n = {
    group: null,
    sort: true,
    disabled: false,
    store: null,
    handle: null,
    draggable: /^[uo]l$/i.test(e.nodeName) ? ">li" : ">*",
    swapThreshold: 1,
    // percentage; 0 <= x <= 1
    invertSwap: false,
    // invert always
    invertedSwapThreshold: null,
    // will be set to same as swapThreshold if default
    removeCloneOnHide: true,
    direction: function() {
      return Ci(e, this.options);
    },
    ghostClass: "sortable-ghost",
    chosenClass: "sortable-chosen",
    dragClass: "sortable-drag",
    ignore: "a, img",
    filter: null,
    preventOnFilter: true,
    animation: 0,
    easing: null,
    setData: function(i2, s) {
      i2.setData("Text", s.textContent);
    },
    dropBubble: false,
    dragoverBubble: false,
    dataIdAttr: "data-id",
    delay: 0,
    delayOnTouchOnly: false,
    touchStartThreshold: (Number.parseInt ? Number : window).parseInt(window.devicePixelRatio, 10) || 1,
    forceFallback: false,
    fallbackClass: "sortable-fallback",
    fallbackOnBody: false,
    fallbackTolerance: 0,
    fallbackOffset: {
      x: 0,
      y: 0
    },
    supportPointer: he.supportPointer !== false && "PointerEvent" in window && !Kn,
    emptyInsertThreshold: 5
  };
  ho.initializePlugins(this, e, n);
  for (var o in n)
    !(o in t) && (t[o] = n[o]);
  Si(t);
  for (var r in this)
    r.charAt(0) === "_" && typeof this[r] == "function" && (this[r] = this[r].bind(this));
  this.nativeDraggable = t.forceFallback ? false : Rf, this.nativeDraggable && (this.options.touchStartThreshold = 1), t.supportPointer ? Re(e, "pointerdown", this._onTapStart) : (Re(e, "mousedown", this._onTapStart), Re(e, "touchstart", this._onTapStart)), this.nativeDraggable && (Re(e, "dragover", this), Re(e, "dragenter", this)), No.push(this.el), t.store && t.store.get && this.sort(t.store.get(this) || []), zt(this, wf());
}
he.prototype = /** @lends Sortable.prototype */
{
  constructor: he,
  _isOutsideThisEl: function(t) {
    !this.el.contains(t) && t !== this.el && (Sn = null);
  },
  _getDirection: function(t, n) {
    return typeof this.options.direction == "function" ? this.options.direction.call(this, t, n, U) : this.options.direction;
  },
  _onTapStart: function(t) {
    if (t.cancelable) {
      var n = this, o = this.el, r = this.options, a = r.preventOnFilter, i2 = t.type, s = t.touches && t.touches[0] || t.pointerType && t.pointerType === "touch" && t, u = (s || t).target, l = t.target.shadowRoot && (t.path && t.path[0] || t.composedPath && t.composedPath()[0]) || u, c = r.filter;
      if (Mf(o), !U && !(/mousedown|pointerdown/.test(i2) && t.button !== 0 || r.disabled) && !l.isContentEditable && !(!this.nativeDraggable && Kn && u && u.tagName.toUpperCase() === "SELECT") && (u = Tt(u, r.draggable, o, false), !(u && u.animated) && $o !== u)) {
        if (Dn = bt(u), Gn = bt(u, r.draggable), typeof c == "function") {
          if (c.call(this, t, u, this)) {
            Ze({
              sortable: n,
              rootEl: l,
              name: "filter",
              targetEl: u,
              toEl: o,
              fromEl: o
            }), rt("filter", n, {
              evt: t
            }), a && t.cancelable && t.preventDefault();
            return;
          }
        } else if (c && (c = c.split(",").some(function(d) {
          if (d = Tt(l, d.trim(), o, false), d)
            return Ze({
              sortable: n,
              rootEl: d,
              name: "filter",
              targetEl: u,
              fromEl: o,
              toEl: o
            }), rt("filter", n, {
              evt: t
            }), true;
        }), c)) {
          a && t.cancelable && t.preventDefault();
          return;
        }
        r.handle && !Tt(l, r.handle, o, false) || this._prepareDragStart(t, s, u);
      }
    }
  },
  _prepareDragStart: function(t, n, o) {
    var r = this, a = r.el, i2 = r.options, s = a.ownerDocument, u;
    if (o && !U && o.parentNode === a) {
      var l = xe(o);
      if (Ae = a, U = o, Ie = U.parentNode, cn = U.nextSibling, $o = o, bo = i2.group, he.dragged = U, ln = {
        target: U,
        clientX: (n || t).clientX,
        clientY: (n || t).clientY
      }, va = ln.clientX - l.left, ba = ln.clientY - l.top, this._lastX = (n || t).clientX, this._lastY = (n || t).clientY, U.style["will-change"] = "all", u = function() {
        if (rt("delayEnded", r, {
          evt: t
        }), he.eventCanceled) {
          r._onDrop();
          return;
        }
        r._disableDelayedDragEvents(), !ha && r.nativeDraggable && (U.draggable = true), r._triggerDragStart(t, n), Ze({
          sortable: r,
          name: "choose",
          originalEvent: t
        }), it(U, i2.chosenClass, true);
      }, i2.ignore.split(",").forEach(function(c) {
        vi(U, c.trim(), lr);
      }), Re(s, "dragover", un), Re(s, "mousemove", un), Re(s, "touchmove", un), Re(s, "mouseup", r._onDrop), Re(s, "touchend", r._onDrop), Re(s, "touchcancel", r._onDrop), ha && this.nativeDraggable && (this.options.touchStartThreshold = 4, U.draggable = true), rt("delayStart", this, {
        evt: t
      }), i2.delay && (!i2.delayOnTouchOnly || n) && (!this.nativeDraggable || !(fo || Wt))) {
        if (he.eventCanceled) {
          this._onDrop();
          return;
        }
        Re(s, "mouseup", r._disableDelayedDrag), Re(s, "touchend", r._disableDelayedDrag), Re(s, "touchcancel", r._disableDelayedDrag), Re(s, "mousemove", r._delayedDragTouchMoveHandler), Re(s, "touchmove", r._delayedDragTouchMoveHandler), i2.supportPointer && Re(s, "pointermove", r._delayedDragTouchMoveHandler), r._dragStartTimer = setTimeout(u, i2.delay);
      } else
        u();
    }
  },
  _delayedDragTouchMoveHandler: function(t) {
    var n = t.touches ? t.touches[0] : t;
    Math.max(Math.abs(n.clientX - this._lastX), Math.abs(n.clientY - this._lastY)) >= Math.floor(this.options.touchStartThreshold / (this.nativeDraggable && window.devicePixelRatio || 1)) && this._disableDelayedDrag();
  },
  _disableDelayedDrag: function() {
    U && lr(U), clearTimeout(this._dragStartTimer), this._disableDelayedDragEvents();
  },
  _disableDelayedDragEvents: function() {
    var t = this.el.ownerDocument;
    Fe(t, "mouseup", this._disableDelayedDrag), Fe(t, "touchend", this._disableDelayedDrag), Fe(t, "touchcancel", this._disableDelayedDrag), Fe(t, "mousemove", this._delayedDragTouchMoveHandler), Fe(t, "touchmove", this._delayedDragTouchMoveHandler), Fe(t, "pointermove", this._delayedDragTouchMoveHandler);
  },
  _triggerDragStart: function(t, n) {
    n = n || t.pointerType == "touch" && t, !this.nativeDraggable || n ? this.options.supportPointer ? Re(document, "pointermove", this._onTouchMove) : n ? Re(document, "touchmove", this._onTouchMove) : Re(document, "mousemove", this._onTouchMove) : (Re(U, "dragend", this), Re(Ae, "dragstart", this._onDragStart));
    try {
      document.selection ? Eo(function() {
        document.selection.empty();
      }) : window.getSelection().removeAllRanges();
    } catch {
    }
  },
  _dragStarted: function(t, n) {
    if (Fn = false, Ae && U) {
      rt("dragStarted", this, {
        evt: n
      }), this.nativeDraggable && Re(document, "dragover", $f);
      var o = this.options;
      !t && it(U, o.dragClass, false), it(U, o.ghostClass, true), he.active = this, t && this._appendGhost(), Ze({
        sortable: this,
        name: "start",
        originalEvent: n
      });
    } else
      this._nulling();
  },
  _emulateDragOver: function() {
    if (wt) {
      this._lastX = wt.clientX, this._lastY = wt.clientY, Fi();
      for (var t = document.elementFromPoint(wt.clientX, wt.clientY), n = t; t && t.shadowRoot && (t = t.shadowRoot.elementFromPoint(wt.clientX, wt.clientY), t !== n); )
        n = t;
      if (U.parentNode[ht]._isOutsideThisEl(t), n)
        do {
          if (n[ht]) {
            var o = void 0;
            if (o = n[ht]._onDragOver({
              clientX: wt.clientX,
              clientY: wt.clientY,
              target: t,
              rootEl: n
            }), o && !this.options.dragoverBubble)
              break;
          }
          t = n;
        } while (n = n.parentNode);
      Ri();
    }
  },
  _onTouchMove: function(t) {
    if (ln) {
      var n = this.options, o = n.fallbackTolerance, r = n.fallbackOffset, a = t.touches ? t.touches[0] : t, i2 = be && kn(be, true), s = be && i2 && i2.a, u = be && i2 && i2.d, l = wo && Ye && ga(Ye), c = (a.clientX - ln.clientX + r.x) / (s || 1) + (l ? l[0] - sr[0] : 0) / (s || 1), d = (a.clientY - ln.clientY + r.y) / (u || 1) + (l ? l[1] - sr[1] : 0) / (u || 1);
      if (!he.active && !Fn) {
        if (o && Math.max(Math.abs(a.clientX - this._lastX), Math.abs(a.clientY - this._lastY)) < o)
          return;
        this._onDragStart(t, true);
      }
      if (be) {
        i2 ? (i2.e += c - (ar || 0), i2.f += d - (ir || 0)) : i2 = {
          a: 1,
          b: 0,
          c: 0,
          d: 1,
          e: c,
          f: d
        };
        var f = "matrix(".concat(i2.a, ",").concat(i2.b, ",").concat(i2.c, ",").concat(i2.d, ",").concat(i2.e, ",").concat(i2.f, ")");
        fe(be, "webkitTransform", f), fe(be, "mozTransform", f), fe(be, "msTransform", f), fe(be, "transform", f), ar = c, ir = d, wt = a;
      }
      t.cancelable && t.preventDefault();
    }
  },
  _appendGhost: function() {
    if (!be) {
      var t = this.options.fallbackOnBody ? document.body : Ae, n = xe(U, true, wo, true, t), o = this.options;
      if (wo) {
        for (Ye = t; fe(Ye, "position") === "static" && fe(Ye, "transform") === "none" && Ye !== document; )
          Ye = Ye.parentNode;
        Ye !== document.body && Ye !== document.documentElement ? (Ye === document && (Ye = Bt()), n.top += Ye.scrollTop, n.left += Ye.scrollLeft) : Ye = Bt(), sr = ga(Ye);
      }
      be = U.cloneNode(true), it(be, o.ghostClass, false), it(be, o.fallbackClass, true), it(be, o.dragClass, true), fe(be, "transition", ""), fe(be, "transform", ""), fe(be, "box-sizing", "border-box"), fe(be, "margin", 0), fe(be, "top", n.top), fe(be, "left", n.left), fe(be, "width", n.width), fe(be, "height", n.height), fe(be, "opacity", "0.8"), fe(be, "position", wo ? "absolute" : "fixed"), fe(be, "zIndex", "100000"), fe(be, "pointerEvents", "none"), he.ghost = be, t.appendChild(be), fe(be, "transform-origin", va / parseInt(be.style.width) * 100 + "% " + ba / parseInt(be.style.height) * 100 + "%");
    }
  },
  _onDragStart: function(t, n) {
    var o = this, r = t.dataTransfer, a = o.options;
    if (rt("dragStart", this, {
      evt: t
    }), he.eventCanceled) {
      this._onDrop();
      return;
    }
    rt("setupClone", this), he.eventCanceled || (Ve = wi(U), Ve.draggable = false, Ve.style["will-change"] = "", this._hideClone(), it(Ve, this.options.chosenClass, false), he.clone = Ve), o.cloneId = Eo(function() {
      rt("clone", o), !he.eventCanceled && (o.options.removeCloneOnHide || Ae.insertBefore(Ve, U), o._hideClone(), Ze({
        sortable: o,
        name: "clone"
      }));
    }), !n && it(U, a.dragClass, true), n ? (jo = true, o._loopId = setInterval(o._emulateDragOver, 50)) : (Fe(document, "mouseup", o._onDrop), Fe(document, "touchend", o._onDrop), Fe(document, "touchcancel", o._onDrop), r && (r.effectAllowed = "move", a.setData && a.setData.call(o, r, U)), Re(document, "drop", o), fe(U, "transform", "translateZ(0)")), Fn = true, o._dragStartId = Eo(o._dragStarted.bind(o, n, t)), Re(document, "selectstart", o), Hn = true, Kn && fe(document.body, "user-select", "none");
  },
  // Returns true - if no further action is needed (either inserted or another condition)
  _onDragOver: function(t) {
    var n = this.el, o = t.target, r, a, i2, s = this.options, u = s.group, l = he.active, c = bo === u, d = s.sort, f = Ke || l, h2, v = this, g = false;
    if (Ar)
      return;
    function w(me, Se) {
      rt(me, v, jt({
        evt: t,
        isOwner: c,
        axis: h2 ? "vertical" : "horizontal",
        revert: i2,
        dragRect: r,
        targetRect: a,
        canSort: d,
        fromSortable: f,
        target: o,
        completed: R,
        onMove: function(p, _) {
          return _o(Ae, n, U, r, p, xe(p), t, _);
        },
        changed: F
      }, Se));
    }
    function y() {
      w("dragOverAnimationCapture"), v.captureAnimationState(), v !== f && f.captureAnimationState();
    }
    function R(me) {
      return w("dragOverCompleted", {
        insertion: me
      }), me && (c ? l._hideClone() : l._showClone(v), v !== f && (it(U, Ke ? Ke.options.ghostClass : l.options.ghostClass, false), it(U, s.ghostClass, true)), Ke !== v && v !== he.active ? Ke = v : v === he.active && Ke && (Ke = null), f === v && (v._ignoreWhileAnimating = o), v.animateAll(function() {
        w("dragOverAnimationComplete"), v._ignoreWhileAnimating = null;
      }), v !== f && (f.animateAll(), f._ignoreWhileAnimating = null)), (o === U && !U.animated || o === n && !o.animated) && (Sn = null), !s.dragoverBubble && !t.rootEl && o !== document && (U.parentNode[ht]._isOutsideThisEl(t.target), !me && un(t)), !s.dragoverBubble && t.stopPropagation && t.stopPropagation(), g = true;
    }
    function F() {
      st = bt(U), Xt = bt(U, s.draggable), Ze({
        sortable: v,
        name: "change",
        toEl: n,
        newIndex: st,
        newDraggableIndex: Xt,
        originalEvent: t
      });
    }
    if (t.preventDefault !== void 0 && t.cancelable && t.preventDefault(), o = Tt(o, s.draggable, n, true), w("dragOver"), he.eventCanceled)
      return g;
    if (U.contains(t.target) || o.animated && o.animatingX && o.animatingY || v._ignoreWhileAnimating === o)
      return R(false);
    if (jo = false, l && !s.disabled && (c ? d || (i2 = Ie !== Ae) : Ke === this || (this.lastPutMode = bo.checkPull(this, l, U, t)) && u.checkPut(this, l, U, t))) {
      if (h2 = this._getDirection(t, o) === "vertical", r = xe(U), w("dragOverValid"), he.eventCanceled)
        return g;
      if (i2)
        return Ie = Ae, y(), this._hideClone(), w("revert"), he.eventCanceled || (cn ? Ae.insertBefore(U, cn) : Ae.appendChild(U)), R(true);
      var k = Kr(n, s.draggable);
      if (!k || Af(t, h2, this) && !k.animated) {
        if (k === U)
          return R(false);
        if (k && n === t.target && (o = k), o && (a = xe(o)), _o(Ae, n, U, r, o, a, t, !!o) !== false)
          return y(), n.appendChild(U), Ie = n, F(), R(true);
      } else if (k && Tf(t, h2, this)) {
        var A = Pn(n, 0, s, true);
        if (A === U)
          return R(false);
        if (o = A, a = xe(o), _o(Ae, n, U, r, o, a, t, false) !== false)
          return y(), n.insertBefore(U, A), Ie = n, F(), R(true);
      } else if (o.parentNode === n) {
        a = xe(o);
        var N = 0, $, B2 = U.parentNode !== n, D = !Df(U.animated && U.toRect || r, o.animated && o.toRect || a, h2), x = h2 ? "top" : "left", I = pa(o, "top", "top") || pa(U, "top", "top"), V = I ? I.scrollTop : void 0;
        Sn !== o && ($ = a[x], Qn = false, yo = !D && s.invertSwap || B2), N = If(t, o, a, h2, D ? 1 : s.swapThreshold, s.invertedSwapThreshold == null ? s.swapThreshold : s.invertedSwapThreshold, yo, Sn === o);
        var O;
        if (N !== 0) {
          var M = bt(U);
          do
            M -= N, O = Ie.children[M];
          while (O && (fe(O, "display") === "none" || O === be));
        }
        if (N === 0 || O === o)
          return R(false);
        Sn = o, Xn = N;
        var X = o.nextElementSibling, le = false;
        le = N === 1;
        var ye = _o(Ae, n, U, r, o, a, t, le);
        if (ye !== false)
          return (ye === 1 || ye === -1) && (le = ye === 1), Ar = true, setTimeout(Ef, 30), y(), le && !X ? n.appendChild(U) : o.parentNode.insertBefore(U, le ? X : o), I && yi(I, 0, V - I.scrollTop), Ie = U.parentNode, $ !== void 0 && !yo && (Oo = Math.abs($ - xe(o)[x])), F(), R(true);
      }
      if (n.contains(U))
        return R(false);
    }
    return false;
  },
  _ignoreWhileAnimating: null,
  _offMoveEvents: function() {
    Fe(document, "mousemove", this._onTouchMove), Fe(document, "touchmove", this._onTouchMove), Fe(document, "pointermove", this._onTouchMove), Fe(document, "dragover", un), Fe(document, "mousemove", un), Fe(document, "touchmove", un);
  },
  _offUpEvents: function() {
    var t = this.el.ownerDocument;
    Fe(t, "mouseup", this._onDrop), Fe(t, "touchend", this._onDrop), Fe(t, "pointerup", this._onDrop), Fe(t, "touchcancel", this._onDrop), Fe(document, "selectstart", this);
  },
  _onDrop: function(t) {
    var n = this.el, o = this.options;
    if (st = bt(U), Xt = bt(U, o.draggable), rt("drop", this, {
      evt: t
    }), Ie = U && U.parentNode, st = bt(U), Xt = bt(U, o.draggable), he.eventCanceled) {
      this._nulling();
      return;
    }
    Fn = false, yo = false, Qn = false, clearInterval(this._loopId), clearTimeout(this._dragStartTimer), Ir(this.cloneId), Ir(this._dragStartId), this.nativeDraggable && (Fe(document, "drop", this), Fe(n, "dragstart", this._onDragStart)), this._offMoveEvents(), this._offUpEvents(), Kn && fe(document.body, "user-select", ""), fe(U, "transform", ""), t && (Hn && (t.cancelable && t.preventDefault(), !o.dropBubble && t.stopPropagation()), be && be.parentNode && be.parentNode.removeChild(be), (Ae === Ie || Ke && Ke.lastPutMode !== "clone") && Ve && Ve.parentNode && Ve.parentNode.removeChild(Ve), U && (this.nativeDraggable && Fe(U, "dragend", this), lr(U), U.style["will-change"] = "", Hn && !Fn && it(U, Ke ? Ke.options.ghostClass : this.options.ghostClass, false), it(U, this.options.chosenClass, false), Ze({
      sortable: this,
      name: "unchoose",
      toEl: Ie,
      newIndex: null,
      newDraggableIndex: null,
      originalEvent: t
    }), Ae !== Ie ? (st >= 0 && (Ze({
      rootEl: Ie,
      name: "add",
      toEl: Ie,
      fromEl: Ae,
      originalEvent: t
    }), Ze({
      sortable: this,
      name: "remove",
      toEl: Ie,
      originalEvent: t
    }), Ze({
      rootEl: Ie,
      name: "sort",
      toEl: Ie,
      fromEl: Ae,
      originalEvent: t
    }), Ze({
      sortable: this,
      name: "sort",
      toEl: Ie,
      originalEvent: t
    })), Ke && Ke.save()) : st !== Dn && st >= 0 && (Ze({
      sortable: this,
      name: "update",
      toEl: Ie,
      originalEvent: t
    }), Ze({
      sortable: this,
      name: "sort",
      toEl: Ie,
      originalEvent: t
    })), he.active && ((st == null || st === -1) && (st = Dn, Xt = Gn), Ze({
      sortable: this,
      name: "end",
      toEl: Ie,
      originalEvent: t
    }), this.save()))), this._nulling();
  },
  _nulling: function() {
    rt("nulling", this), Ae = U = Ie = be = cn = Ve = $o = Qt = ln = wt = Hn = st = Xt = Dn = Gn = Sn = Xn = Ke = bo = he.dragged = he.ghost = he.clone = he.active = null, Lo.forEach(function(t) {
      t.checked = true;
    }), Lo.length = ar = ir = 0;
  },
  handleEvent: function(t) {
    switch (t.type) {
      case "drop":
      case "dragend":
        this._onDrop(t);
        break;
      case "dragenter":
      case "dragover":
        U && (this._onDragOver(t), Of(t));
        break;
      case "selectstart":
        t.preventDefault();
        break;
    }
  },
  /**
   * Serializes the item into an array of string.
   * @returns {String[]}
   */
  toArray: function() {
    for (var t = [], n, o = this.el.children, r = 0, a = o.length, i2 = this.options; r < a; r++)
      n = o[r], Tt(n, i2.draggable, this.el, false) && t.push(n.getAttribute(i2.dataIdAttr) || Pf(n));
    return t;
  },
  /**
   * Sorts the elements according to the array.
   * @param  {String[]}  order  order of the items
   */
  sort: function(t, n) {
    var o = {}, r = this.el;
    this.toArray().forEach(function(a, i2) {
      var s = r.children[i2];
      Tt(s, this.options.draggable, r, false) && (o[a] = s);
    }, this), n && this.captureAnimationState(), t.forEach(function(a) {
      o[a] && (r.removeChild(o[a]), r.appendChild(o[a]));
    }), n && this.animateAll();
  },
  /**
   * Save the current sorting
   */
  save: function() {
    var t = this.options.store;
    t && t.set && t.set(this);
  },
  /**
   * For each element in the set, get the first element that matches the selector by testing the element itself and traversing up through its ancestors in the DOM tree.
   * @param   {HTMLElement}  el
   * @param   {String}       [selector]  default: `options.draggable`
   * @returns {HTMLElement|null}
   */
  closest: function(t, n) {
    return Tt(t, n || this.options.draggable, this.el, false);
  },
  /**
   * Set/get option
   * @param   {string} name
   * @param   {*}      [value]
   * @returns {*}
   */
  option: function(t, n) {
    var o = this.options;
    if (n === void 0)
      return o[t];
    var r = ho.modifyOption(this, t, n);
    typeof r < "u" ? o[t] = r : o[t] = n, t === "group" && Si(o);
  },
  /**
   * Destroy
   */
  destroy: function() {
    rt("destroy", this);
    var t = this.el;
    t[ht] = null, Fe(t, "mousedown", this._onTapStart), Fe(t, "touchstart", this._onTapStart), Fe(t, "pointerdown", this._onTapStart), this.nativeDraggable && (Fe(t, "dragover", this), Fe(t, "dragenter", this)), Array.prototype.forEach.call(t.querySelectorAll("[draggable]"), function(n) {
      n.removeAttribute("draggable");
    }), this._onDrop(), this._disableDelayedDragEvents(), No.splice(No.indexOf(this.el), 1), this.el = t = null;
  },
  _hideClone: function() {
    if (!Qt) {
      if (rt("hideClone", this), he.eventCanceled)
        return;
      fe(Ve, "display", "none"), this.options.removeCloneOnHide && Ve.parentNode && Ve.parentNode.removeChild(Ve), Qt = true;
    }
  },
  _showClone: function(t) {
    if (t.lastPutMode !== "clone") {
      this._hideClone();
      return;
    }
    if (Qt) {
      if (rt("showClone", this), he.eventCanceled)
        return;
      U.parentNode == Ae && !this.options.group.revertClone ? Ae.insertBefore(Ve, U) : cn ? Ae.insertBefore(Ve, cn) : Ae.appendChild(Ve), this.options.group.revertClone && this.animate(U, Ve), fe(Ve, "display", ""), Qt = false;
    }
  }
};
function Of(e) {
  e.dataTransfer && (e.dataTransfer.dropEffect = "move"), e.cancelable && e.preventDefault();
}
function _o(e, t, n, o, r, a, i2, s) {
  var u, l = e[ht], c = l.options.onMove, d;
  return window.CustomEvent && !Wt && !fo ? u = new CustomEvent("move", {
    bubbles: true,
    cancelable: true
  }) : (u = document.createEvent("Event"), u.initEvent("move", true, true)), u.to = t, u.from = e, u.dragged = n, u.draggedRect = o, u.related = r || t, u.relatedRect = a || xe(t), u.willInsertAfter = s, u.originalEvent = i2, e.dispatchEvent(u), c && (d = c.call(l, u, i2)), d;
}
function lr(e) {
  e.draggable = false;
}
function Ef() {
  Ar = false;
}
function Tf(e, t, n) {
  var o = xe(Pn(n.el, 0, n.options, true)), r = 10;
  return t ? e.clientX < o.left - r || e.clientY < o.top && e.clientX < o.right : e.clientY < o.top - r || e.clientY < o.bottom && e.clientX < o.left;
}
function Af(e, t, n) {
  var o = xe(Kr(n.el, n.options.draggable)), r = 10;
  return t ? e.clientX > o.right + r || e.clientX <= o.right && e.clientY > o.bottom && e.clientX >= o.left : e.clientX > o.right && e.clientY > o.top || e.clientX <= o.right && e.clientY > o.bottom + r;
}
function If(e, t, n, o, r, a, i2, s) {
  var u = o ? e.clientY : e.clientX, l = o ? n.height : n.width, c = o ? n.top : n.left, d = o ? n.bottom : n.right, f = false;
  if (!i2) {
    if (s && Oo < l * r) {
      if (!Qn && (Xn === 1 ? u > c + l * a / 2 : u < d - l * a / 2) && (Qn = true), Qn)
        f = true;
      else if (Xn === 1 ? u < c + Oo : u > d - Oo)
        return -Xn;
    } else if (u > c + l * (1 - r) / 2 && u < d - l * (1 - r) / 2)
      return Vf(t);
  }
  return f = f || i2, f && (u < c + l * a / 2 || u > d - l * a / 2) ? u > c + l / 2 ? 1 : -1 : 0;
}
function Vf(e) {
  return bt(U) < bt(e) ? 1 : -1;
}
function Pf(e) {
  for (var t = e.tagName + e.className + e.src + e.href + e.textContent, n = t.length, o = 0; n--; )
    o += t.charCodeAt(n);
  return o.toString(36);
}
function Mf(e) {
  Lo.length = 0;
  for (var t = e.getElementsByTagName("input"), n = t.length; n--; ) {
    var o = t[n];
    o.checked && Lo.push(o);
  }
}
function Eo(e) {
  return setTimeout(e, 0);
}
function Ir(e) {
  return clearTimeout(e);
}
Qo && Re(document, "touchmove", function(e) {
  (he.active || Fn) && e.cancelable && e.preventDefault();
});
he.utils = {
  on: Re,
  off: Fe,
  css: fe,
  find: vi,
  is: function(t, n) {
    return !!Tt(t, n, t, false);
  },
  extend: bf,
  throttle: bi,
  closest: Tt,
  toggleClass: it,
  clone: wi,
  index: bt,
  nextTick: Eo,
  cancelNextTick: Ir,
  detectDirection: Ci,
  getChild: Pn
};
he.get = function(e) {
  return e[ht];
};
he.mount = function() {
  for (var e = arguments.length, t = new Array(e), n = 0; n < e; n++)
    t[n] = arguments[n];
  t[0].constructor === Array && (t = t[0]), t.forEach(function(o) {
    if (!o.prototype || !o.prototype.constructor)
      throw "Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(o));
    o.utils && (he.utils = jt(jt({}, he.utils), o.utils)), ho.mount(o);
  });
};
he.create = function(e, t) {
  return new he(e, t);
};
he.version = mf;
var je = [];
var zn;
var Vr;
var Pr = false;
var ur;
var cr;
var qo;
var Wn;
function Bf() {
  function e() {
    this.defaults = {
      scroll: true,
      forceAutoScrollFallback: false,
      scrollSensitivity: 30,
      scrollSpeed: 10,
      bubbleScroll: true
    };
    for (var t in this)
      t.charAt(0) === "_" && typeof this[t] == "function" && (this[t] = this[t].bind(this));
  }
  return e.prototype = {
    dragStarted: function(n) {
      var o = n.originalEvent;
      this.sortable.nativeDraggable ? Re(document, "dragover", this._handleAutoScroll) : this.options.supportPointer ? Re(document, "pointermove", this._handleFallbackAutoScroll) : o.touches ? Re(document, "touchmove", this._handleFallbackAutoScroll) : Re(document, "mousemove", this._handleFallbackAutoScroll);
    },
    dragOverCompleted: function(n) {
      var o = n.originalEvent;
      !this.options.dragOverBubble && !o.rootEl && this._handleAutoScroll(o);
    },
    drop: function() {
      this.sortable.nativeDraggable ? Fe(document, "dragover", this._handleAutoScroll) : (Fe(document, "pointermove", this._handleFallbackAutoScroll), Fe(document, "touchmove", this._handleFallbackAutoScroll), Fe(document, "mousemove", this._handleFallbackAutoScroll)), wa(), To(), yf();
    },
    nulling: function() {
      qo = Vr = zn = Pr = Wn = ur = cr = null, je.length = 0;
    },
    _handleFallbackAutoScroll: function(n) {
      this._handleAutoScroll(n, true);
    },
    _handleAutoScroll: function(n, o) {
      var r = this, a = (n.touches ? n.touches[0] : n).clientX, i2 = (n.touches ? n.touches[0] : n).clientY, s = document.elementFromPoint(a, i2);
      if (qo = n, o || this.options.forceAutoScrollFallback || fo || Wt || Kn) {
        dr(n, this.options, s, o);
        var u = Zt(s, true);
        Pr && (!Wn || a !== ur || i2 !== cr) && (Wn && wa(), Wn = setInterval(function() {
          var l = Zt(document.elementFromPoint(a, i2), true);
          l !== u && (u = l, To()), dr(n, r.options, l, o);
        }, 10), ur = a, cr = i2);
      } else {
        if (!this.options.bubbleScroll || Zt(s, true) === Bt()) {
          To();
          return;
        }
        dr(n, this.options, Zt(s, false), false);
      }
    }
  }, zt(e, {
    pluginName: "scroll",
    initializeByDefault: true
  });
}
function To() {
  je.forEach(function(e) {
    clearInterval(e.pid);
  }), je = [];
}
function wa() {
  clearInterval(Wn);
}
var dr = bi(function(e, t, n, o) {
  if (t.scroll) {
    var r = (e.touches ? e.touches[0] : e).clientX, a = (e.touches ? e.touches[0] : e).clientY, i2 = t.scrollSensitivity, s = t.scrollSpeed, u = Bt(), l = false, c;
    Vr !== n && (Vr = n, To(), zn = t.scroll, c = t.scrollFn, zn === true && (zn = Zt(n, true)));
    var d = 0, f = zn;
    do {
      var h2 = f, v = xe(h2), g = v.top, w = v.bottom, y = v.left, R = v.right, F = v.width, k = v.height, A = void 0, N = void 0, $ = h2.scrollWidth, B2 = h2.scrollHeight, D = fe(h2), x = h2.scrollLeft, I = h2.scrollTop;
      h2 === u ? (A = F < $ && (D.overflowX === "auto" || D.overflowX === "scroll" || D.overflowX === "visible"), N = k < B2 && (D.overflowY === "auto" || D.overflowY === "scroll" || D.overflowY === "visible")) : (A = F < $ && (D.overflowX === "auto" || D.overflowX === "scroll"), N = k < B2 && (D.overflowY === "auto" || D.overflowY === "scroll"));
      var V = A && (Math.abs(R - r) <= i2 && x + F < $) - (Math.abs(y - r) <= i2 && !!x), O = N && (Math.abs(w - a) <= i2 && I + k < B2) - (Math.abs(g - a) <= i2 && !!I);
      if (!je[d])
        for (var M = 0; M <= d; M++)
          je[M] || (je[M] = {});
      (je[d].vx != V || je[d].vy != O || je[d].el !== h2) && (je[d].el = h2, je[d].vx = V, je[d].vy = O, clearInterval(je[d].pid), (V != 0 || O != 0) && (l = true, je[d].pid = setInterval((function() {
        o && this.layer === 0 && he.active._onTouchMove(qo);
        var X = je[this.layer].vy ? je[this.layer].vy * s : 0, le = je[this.layer].vx ? je[this.layer].vx * s : 0;
        typeof c == "function" && c.call(he.dragged.parentNode[ht], le, X, e, qo, je[this.layer].el) !== "continue" || yi(je[this.layer].el, le, X);
      }).bind({
        layer: d
      }), 24))), d++;
    } while (t.bubbleScroll && f !== u && (f = Zt(f, false)));
    Pr = l;
  }
}, 30);
var Di = function(t) {
  var n = t.originalEvent, o = t.putSortable, r = t.dragEl, a = t.activeSortable, i2 = t.dispatchSortableEvent, s = t.hideGhostForTarget, u = t.unhideGhostForTarget;
  if (n) {
    var l = o || a;
    s();
    var c = n.changedTouches && n.changedTouches.length ? n.changedTouches[0] : n, d = document.elementFromPoint(c.clientX, c.clientY);
    u(), l && !l.el.contains(d) && (i2("spill"), this.onSpill({
      dragEl: r,
      putSortable: o
    }));
  }
};
function Yr() {
}
Yr.prototype = {
  startIndex: null,
  dragStart: function(t) {
    var n = t.oldDraggableIndex;
    this.startIndex = n;
  },
  onSpill: function(t) {
    var n = t.dragEl, o = t.putSortable;
    this.sortable.captureAnimationState(), o && o.captureAnimationState();
    var r = Pn(this.sortable.el, this.startIndex, this.options);
    r ? this.sortable.el.insertBefore(n, r) : this.sortable.el.appendChild(n), this.sortable.animateAll(), o && o.animateAll();
  },
  drop: Di
};
zt(Yr, {
  pluginName: "revertOnSpill"
});
function Gr() {
}
Gr.prototype = {
  onSpill: function(t) {
    var n = t.dragEl, o = t.putSortable, r = o || this.sortable;
    r.captureAnimationState(), n.parentNode && n.parentNode.removeChild(n), r.animateAll();
  },
  drop: Di
};
zt(Gr, {
  pluginName: "removeOnSpill"
});
he.mount(new Bf());
he.mount(Gr, Yr);
var jf = Object.defineProperty;
var Nf = Object.defineProperties;
var Lf = Object.getOwnPropertyDescriptors;
var _a = Object.getOwnPropertySymbols;
var qf = Object.prototype.hasOwnProperty;
var xf = Object.prototype.propertyIsEnumerable;
var Ca = (e, t, n) => t in e ? jf(e, t, { enumerable: true, configurable: true, writable: true, value: n }) : e[t] = n;
var en = (e, t) => {
  for (var n in t || (t = {}))
    qf.call(t, n) && Ca(e, n, t[n]);
  if (_a)
    for (var n of _a(t))
      xf.call(t, n) && Ca(e, n, t[n]);
  return e;
};
var xo = (e, t) => Nf(e, Lf(t));
function fr(e) {
  e.parentElement !== null && e.parentElement.removeChild(e);
}
function Sa(e, t, n) {
  const o = n === 0 ? e.children[0] : e.children[n - 1].nextSibling;
  e.insertBefore(t, o);
}
function Hf() {
  return typeof window < "u" ? window.console : global.console;
}
var zf = Hf();
function Wf(e) {
  const t = /* @__PURE__ */ Object.create(null);
  return function(o) {
    return t[o] || (t[o] = e(o));
  };
}
var Uf = /-(\w)/g;
var Kf = Wf((e) => e.replace(Uf, (t, n) => n.toUpperCase()));
var ki = ["Start", "Add", "Remove", "Update", "End"];
var $i = ["Choose", "Unchoose", "Sort", "Filter", "Clone"];
var Oi = ["Move"];
var Yf = [Oi, ki, $i].flatMap((e) => e).map((e) => `on${e}`);
var Mr = {
  manage: Oi,
  manageAndEmit: ki,
  emit: $i
};
function Gf(e) {
  return Yf.indexOf(e) !== -1;
}
var Xf = [
  "a",
  "abbr",
  "address",
  "area",
  "article",
  "aside",
  "audio",
  "b",
  "base",
  "bdi",
  "bdo",
  "blockquote",
  "body",
  "br",
  "button",
  "canvas",
  "caption",
  "cite",
  "code",
  "col",
  "colgroup",
  "data",
  "datalist",
  "dd",
  "del",
  "details",
  "dfn",
  "dialog",
  "div",
  "dl",
  "dt",
  "em",
  "embed",
  "fieldset",
  "figcaption",
  "figure",
  "footer",
  "form",
  "h1",
  "h2",
  "h3",
  "h4",
  "h5",
  "h6",
  "head",
  "header",
  "hgroup",
  "hr",
  "html",
  "i",
  "iframe",
  "img",
  "input",
  "ins",
  "kbd",
  "label",
  "legend",
  "li",
  "link",
  "main",
  "map",
  "mark",
  "math",
  "menu",
  "menuitem",
  "meta",
  "meter",
  "nav",
  "noscript",
  "object",
  "ol",
  "optgroup",
  "option",
  "output",
  "p",
  "param",
  "picture",
  "pre",
  "progress",
  "q",
  "rb",
  "rp",
  "rt",
  "rtc",
  "ruby",
  "s",
  "samp",
  "script",
  "section",
  "select",
  "slot",
  "small",
  "source",
  "span",
  "strong",
  "style",
  "sub",
  "summary",
  "sup",
  "svg",
  "table",
  "tbody",
  "td",
  "template",
  "textarea",
  "tfoot",
  "th",
  "thead",
  "time",
  "title",
  "tr",
  "track",
  "u",
  "ul",
  "var",
  "video",
  "wbr"
];
function Qf(e) {
  return Xf.includes(e);
}
function Jf(e) {
  return ["transition-group", "TransitionGroup"].includes(e);
}
function Ei(e) {
  return ["id", "class", "role", "style"].includes(e) || e.startsWith("data-") || e.startsWith("aria-") || e.startsWith("on");
}
function Ti(e) {
  return e.reduce((t, [n, o]) => (t[n] = o, t), {});
}
function Zf({ $attrs: e, componentData: t = {} }) {
  const n = Ti(Object.entries(e).filter(([o, r]) => Ei(o)));
  return en(en({}, n), t);
}
function eh({ $attrs: e, callBackBuilder: t }) {
  const n = Ti(Ai(e));
  Object.entries(t).forEach(([r, a]) => {
    Mr[r].forEach((i2) => {
      n[`on${i2}`] = a(i2);
    });
  });
  const o = `[data-draggable]${n.draggable || ""}`;
  return xo(en({}, n), {
    draggable: o
  });
}
function Ai(e) {
  return Object.entries(e).filter(([t, n]) => !Ei(t)).map(([t, n]) => [Kf(t), n]).filter(([t, n]) => !Gf(t));
}
var Fa = (e) => {
  const t = e.el || Array.isArray(e.children) && e.children[0].el.parentNode;
  return t || console.error("使用 transition-group , 需要在slot中template内至少2层html标签"), t || {};
};
var th = (e, t) => e.__draggable_context = t;
var Ra = (e) => e.__draggable_context;
var nh = class {
  constructor({
    nodes: { header: t, default: n, footer: o },
    root: r,
    realList: a
  }) {
    this.defaultNodes = n, this.children = [...t, ...n, ...o], this.externalComponent = r.externalComponent, this.rootTransition = r.transition, this.tag = r.tag, this.realList = a;
  }
  get _isRootComponent() {
    return this.externalComponent || this.rootTransition;
  }
  render(t, n) {
    const { tag: o, children: r, _isRootComponent: a } = this;
    return t(o, n, a ? { default: () => r } : r);
  }
  updated() {
    const { defaultNodes: t, realList: n } = this;
    t.forEach((o, r) => {
      th(Fa(o), {
        element: n[r],
        index: r
      });
    });
  }
  getUnderlyingVm(t) {
    return Ra(t);
  }
  getVmIndexFromDomIndex(t, n) {
    const { defaultNodes: o } = this, { length: r } = o, a = n.children, i2 = a.item(t);
    if (i2 === null)
      return r;
    const s = Ra(i2);
    if (s)
      return s.index;
    if (r === 0)
      return 0;
    const u = Fa(o[0]), l = [...a].findIndex((c) => c === u);
    return t < l ? 0 : r;
  }
};
function oh(e, t) {
  const n = e[t];
  return n ? n() : [];
}
function rh({ $slots: e, realList: t, getKey: n }) {
  const o = t || [], [r, a] = ["header", "footer"].map((u) => oh(e, u)), { item: i2 } = e;
  if (!i2)
    throw new Error("draggable element must have an item slot");
  const s = o.flatMap((u, l) => i2({ element: u, index: l }).map((c) => (c.key = n(u), c.props = xo(en({}, c.props || {}), { "data-draggable": true }), c)));
  if (s.length !== o.length)
    throw new Error("Item slot must have only one child");
  return {
    header: r,
    footer: a,
    default: s
  };
}
function ah(e) {
  const t = Jf(e), n = !Qf(e) && !t;
  return {
    transition: t,
    externalComponent: n,
    tag: n ? resolveComponent(e) : t ? TransitionGroup : e
  };
}
function ih({ $slots: e, tag: t, realList: n, getKey: o }) {
  const r = rh({ $slots: e, realList: n, getKey: o }), a = ah(t);
  return new nh({ nodes: r, root: a, realList: n });
}
function Ii(e, t) {
  nextTick(() => this.$emit(e.toLowerCase(), t));
}
function Vi(e) {
  return (t, n) => {
    if (this.realList !== null)
      return this[`onDrag${e}`](t, n);
  };
}
function sh(e) {
  const t = Vi.call(this, e);
  return (n, o) => {
    t.call(this, n, o), Ii.call(this, e, n);
  };
}
var hr = null;
var lh = {
  list: {
    type: Array,
    required: false,
    default: null
  },
  modelValue: {
    type: Array,
    required: false,
    default: null
  },
  itemKey: {
    type: [String, Function],
    required: true
  },
  clone: {
    type: Function,
    default: (e) => e
  },
  tag: {
    type: String,
    default: "div"
  },
  move: {
    type: Function,
    default: null
  },
  componentData: {
    type: Object,
    required: false,
    default: null
  }
};
var uh = [
  "update:modelValue",
  "change",
  ...[...Mr.manageAndEmit, ...Mr.emit].map((e) => e.toLowerCase())
];
var ch = defineComponent({
  name: "draggable",
  inheritAttrs: false,
  props: lh,
  emits: uh,
  data() {
    return {
      error: false
    };
  },
  render() {
    try {
      this.error = false;
      const { $slots: e, $attrs: t, tag: n, componentData: o, realList: r, getKey: a } = this, i2 = ih({
        $slots: e,
        tag: n,
        realList: r,
        getKey: a
      });
      this.componentStructure = i2;
      const s = Zf({ $attrs: t, componentData: o });
      return i2.render(h, s);
    } catch (e) {
      return this.error = true, h("pre", { style: { color: "red" } }, e.stack);
    }
  },
  created() {
    this.list !== null && this.modelValue !== null && zf.error("modelValue and list props are mutually exclusive! Please set one or another.");
  },
  mounted() {
    if (this.error)
      return;
    const { $attrs: e, $el: t, componentStructure: n } = this;
    n.updated();
    const o = eh({
      $attrs: e,
      callBackBuilder: {
        manageAndEmit: (a) => sh.call(this, a),
        emit: (a) => Ii.bind(this, a),
        manage: (a) => Vi.call(this, a)
      }
    }), r = t.nodeType === 1 ? t : t.parentElement;
    this._sortable = new he(r, o), this.targetDomElement = r, r.__draggable_component__ = this;
  },
  updated() {
    this.componentStructure.updated();
  },
  beforeUnmount() {
    this._sortable !== void 0 && this._sortable.destroy();
  },
  computed: {
    realList() {
      const { list: e } = this;
      return e || this.modelValue;
    },
    getKey() {
      const { itemKey: e } = this;
      return typeof e == "function" ? e : (t) => t[e];
    }
  },
  watch: {
    $attrs: {
      handler(e) {
        const { _sortable: t } = this;
        t && Ai(e).forEach(([n, o]) => {
          t.option(n, o);
        });
      },
      deep: true
    }
  },
  methods: {
    getUnderlyingVm(e) {
      return this.componentStructure.getUnderlyingVm(e) || null;
    },
    getUnderlyingPotencialDraggableComponent(e) {
      return e.__draggable_component__;
    },
    emitChanges(e) {
      nextTick(() => this.$emit("change", e));
    },
    alterList(e) {
      if (this.list) {
        e(this.list);
        return;
      }
      const t = [...this.modelValue];
      e(t), this.$emit("update:modelValue", t);
    },
    spliceList() {
      const e = (t) => t.splice(...arguments);
      this.alterList(e);
    },
    updatePosition(e, t) {
      const n = (o) => o.splice(t, 0, o.splice(e, 1)[0]);
      this.alterList(n);
    },
    getRelatedContextFromMoveEvent({ to: e, related: t }) {
      const n = this.getUnderlyingPotencialDraggableComponent(e);
      if (!n)
        return { component: n };
      const o = n.realList, r = { list: o, component: n };
      if (e !== t && o) {
        const a = n.getUnderlyingVm(t) || {};
        return en(en({}, a), r);
      }
      return r;
    },
    getVmIndexFromDomIndex(e) {
      return this.componentStructure.getVmIndexFromDomIndex(e, this.targetDomElement);
    },
    onDragStart(e) {
      this.context = this.getUnderlyingVm(e.item), e.item._underlying_vm_ = this.clone(this.context.element), hr = e.item;
    },
    onDragAdd(e) {
      const t = e.item._underlying_vm_;
      if (t === void 0)
        return;
      fr(e.item);
      const n = this.getVmIndexFromDomIndex(e.newIndex);
      this.spliceList(n, 0, t);
      const o = { element: t, newIndex: n };
      this.emitChanges({ added: o });
    },
    onDragRemove(e) {
      if (Sa(this.$el, e.item, e.oldIndex), e.pullMode === "clone") {
        fr(e.clone);
        return;
      }
      const { index: t, element: n } = this.context;
      this.spliceList(t, 1);
      const o = { element: n, oldIndex: t };
      this.emitChanges({ removed: o });
    },
    onDragUpdate(e) {
      fr(e.item), Sa(e.from, e.item, e.oldIndex);
      const t = this.context.index, n = this.getVmIndexFromDomIndex(e.newIndex);
      this.updatePosition(t, n);
      const o = { element: this.context.element, oldIndex: t, newIndex: n };
      this.emitChanges({ moved: o });
    },
    computeFutureIndex(e, t) {
      if (!e.element)
        return 0;
      const n = [...t.to.children].filter((i2) => i2.style.display !== "none"), o = n.indexOf(t.related), r = e.component.getVmIndexFromDomIndex(o);
      return n.indexOf(hr) !== -1 || !t.willInsertAfter ? r : r + 1;
    },
    onDragMove(e, t) {
      const { move: n, realList: o } = this;
      if (!n || !o)
        return true;
      const r = this.getRelatedContextFromMoveEvent(e), a = this.computeFutureIndex(r, e), i2 = xo(en({}, this.context), {
        futureIndex: a
      }), s = xo(en({}, e), {
        relatedContext: r,
        draggedContext: i2
      });
      return n(s, t);
    },
    onDragEnd() {
      hr = null;
    }
  }
});
var dh = defineComponent({
  name: "FsTableColumnsFixedController",
  props: {
    modelValue: {
      default: false
    }
  },
  emits: ["update:modelValue", "change"],
  setup(e, t) {
    const { ui: n } = B(), o = computed(() => [
      {
        value: "left",
        icon: n.icons.left
        // 'el-icon-arrow-left'
      },
      {
        value: false,
        icon: n.icons.close
        // 'el-icon-close'
      },
      {
        value: "right",
        icon: n.icons.right
        // 'el-icon-arrow-right'
      }
    ]);
    function r(a) {
      t.emit("update:modelValue", a), t.emit("change", a);
    }
    return {
      ui: n,
      options: o,
      submit: r
    };
  }
});
function fh(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-button");
  return openBlock(), createBlock(resolveDynamicComponent(e.ui.buttonGroup.name), { class: "fs-table-columns-fixed-controller" }, {
    default: withCtx(() => [
      (openBlock(true), createElementBlock(Fragment, null, renderList(e.options, (s) => (openBlock(), createBlock(i2, {
        key: s.icon,
        type: e.modelValue === s.value ? "primary" : "default",
        icon: s.icon,
        size: "small",
        onClick: (u) => e.submit(s.value)
      }, null, 8, ["type", "icon", "onClick"]))), 128))
    ]),
    _: 1
  });
}
var Pi = ke(dh, [["render", fh]]);
var hh = ["title", "i"];
var mh = { class: "item-right" };
var ph = {
  "flex-box": "0",
  class: "component--list-item-handle handle"
};
var gh = { style: { "margin-left": "20px", "padding-left": "10px", "border-left": "1px solid #eee" } };
var Mi = defineComponent({
  __name: "fs-columns-filter-nest-list",
  props: {
    columns: {},
    isRoot: { type: Boolean, default: false }
  },
  emits: ["check-changed", "fixed-changed"],
  setup(e, { emit: t }) {
    const { ui: n } = B(), o = t, { originalColumns: r, currentColumns: a, originalColumnsMap: i2, text: s, active: u } = inject(Ur);
    function l(g) {
      return g.label || g.title || g.key || s.value.unnamed;
    }
    function c(g) {
      const w = g.draggedContext.element, y = g.relatedContext.element, R = [];
      for (const $ of a.value)
        $.key === w.key ? R.push(y) : $.key === y.key ? R.push(w) : R.push($);
      let F = 0, k = R.length - 1, A = R.length - 1, N = 0;
      for (let $ = 0; $ < R.length; $++) {
        const B2 = R[$];
        B2.fixed === "left" ? F = $ : B2.fixed === "right" ? k = k > $ ? $ : k : (A = A > $ ? $ : A, N = N < $ ? $ : N);
      }
      if (A < F || N > k)
        return false;
    }
    function d(g) {
      return "update:" + g;
    }
    function f(g) {
      g.show = !g.show;
      function w(R) {
        R.children && R.children.forEach((F) => {
          !F.__show || F.__disabled || (F.show = R.show, w(F));
        });
      }
      w(g);
      function y(R) {
        if (R.__parent) {
          const F = R.__parent;
          F && (F.show = F.children.filter((k) => k.__show && k.show === true).length > 0, y(F));
        }
      }
      y(g);
    }
    function h2() {
      o("check-changed");
    }
    function v(g, w) {
      o("fixed-changed", g, w);
    }
    return (g, w) => {
      const y = resolveComponent("fs-icon"), R = resolveComponent("fs-columns-filter-nest-list", true);
      return openBlock(), createBlock(unref(ch), {
        list: g.columns,
        "item-key": "key",
        move: c
      }, {
        item: withCtx(({ element: F, index: k }) => {
          var A, N;
          return [
            createBaseVNode("div", null, [
              withDirectives(createBaseVNode("div", {
                title: l(F),
                class: "component--list-item",
                flex: "main:justify cross:center",
                i: k
              }, [
                (openBlock(), createBlock(resolveDynamicComponent(unref(n).checkbox.name), mergeProps({
                  [unref(n).checkbox.modelValue || ""]: F.show,
                  disabled: ((A = unref(i2)[F.__key]) == null ? void 0 : A.__disabled) === true,
                  class: "item-label",
                  title: l(F)
                }, {
                  [toHandlerKey(d(unref(n).checkbox.modelValue))]: ($) => f(F)
                }, { onChange: h2 }), {
                  default: withCtx(() => [
                    createTextVNode(toDisplayString(l(F)), 1)
                  ]),
                  _: 2
                }, 1040, ["disabled", "title"])),
                createBaseVNode("div", mh, [
                  g.isRoot ? (openBlock(), createBlock(Pi, {
                    key: 0,
                    modelValue: F.fixed,
                    "onUpdate:modelValue": ($) => F.fixed = $,
                    "flex-box": "0",
                    class: "d2-mr-10",
                    onChange: ($) => v(k, $)
                  }, null, 8, ["modelValue", "onUpdate:modelValue", "onChange"])) : createCommentVNode("", true),
                  createBaseVNode("div", ph, [
                    createVNode(y, {
                      icon: unref(n).icons.sort
                    }, null, 8, ["icon"])
                  ])
                ])
              ], 8, hh), [
                [vShow, ((N = unref(i2)[F.__key]) == null ? void 0 : N.__show) !== false]
              ]),
              createBaseVNode("div", gh, [
                F.children ? (openBlock(), createBlock(R, {
                  key: 0,
                  columns: F.children,
                  onCheckChanged: h2
                }, null, 8, ["columns"])) : createCommentVNode("", true)
              ])
            ])
          ];
        }),
        _: 1
      }, 8, ["list"]);
    };
  }
});
var vh = { class: "component--list" };
var bh = {
  key: "__first__",
  class: "component--list-item",
  flex: "main:justify cross:center"
};
var yh = { span: 12 };
var wh = { class: "title" };
var _h = defineComponent({
  __name: "fs-columns-filter-layout-default",
  props: {
    width: {},
    drawer: {}
  },
  setup(e) {
    const { ui: t } = B(), { originalColumns: n, currentColumns: o, text: r, active: a } = inject(Ur), i2 = e, s = computed(() => merge_default(
      {
        [t.drawer.visible]: a.value,
        ["onUpdate:" + t.drawer.visible]: (w) => {
          a.value = w;
        },
        [t.drawer.width]: i2.width || "400px"
      },
      i2.drawer
    )), u = ref(false);
    function l(w) {
      u.value = w, o.value = o.value.map((y) => (!y.__show || y.__disabled || (y.show = w), y));
    }
    const c = computed(() => ({
      [t.checkbox.modelValue]: u.value,
      ["onUpdate:" + t.checkbox.modelValue]: (w) => {
        l(w);
      }
    })), d = computed(() => o.value.filter((w) => w.__show && w.show === true).length), f = computed(() => o.value.filter((w) => w.__show).length), h2 = computed(() => d.value > 0 && d.value < f.value);
    watch(
      () => {
        o.value;
      },
      () => {
        g();
      },
      { immediate: true }
    );
    function v(w, y) {
      y && (o.value[w].show = true), y === "left" && o.value.unshift(o.value.splice(w, 1)[0]), y === "right" && o.value.push(o.value.splice(w, 1)[0]), g();
    }
    function g() {
      u.value = d.value === f.value;
    }
    return (w, y) => (openBlock(), createBlock(resolveDynamicComponent(unref(t).drawer.name), mergeProps({
      class: "fs-columns-filter-layout-default",
      title: unref(r).title
    }, s.value, { "append-to-body": "" }), {
      default: withCtx(() => [
        (openBlock(), createBlock(resolveDynamicComponent(unref(t).drawer.hasContentWrap || "div"), {
          class: "fs-drawer-wrapper fs-table-columns-filter",
          title: unref(r).title
        }, {
          default: withCtx(() => [
            (openBlock(), createBlock(resolveDynamicComponent(unref(t).card.name), { shadow: "never" }, {
              default: withCtx(() => [
                createBaseVNode("div", vh, [
                  createBaseVNode("div", bh, [
                    createBaseVNode("span", yh, [
                      (openBlock(), createBlock(resolveDynamicComponent(unref(t).checkbox.name), mergeProps({ indeterminate: h2.value }, c.value), {
                        default: withCtx(() => [
                          createTextVNode(toDisplayString(d.value) + " / " + toDisplayString(f.value), 1)
                        ]),
                        _: 1
                      }, 16, ["indeterminate"]))
                    ]),
                    createBaseVNode("span", wh, toDisplayString(unref(r).fixed) + " / " + toDisplayString(unref(r).order), 1)
                  ]),
                  createVNode(Mi, {
                    columns: unref(o),
                    "is-root": true,
                    onCheckChanged: g,
                    onFixedChanged: v
                  }, null, 8, ["columns"])
                ])
              ]),
              _: 1
            })),
            renderSlot(w.$slots, "buttons")
          ]),
          _: 3
        }, 8, ["title"]))
      ]),
      _: 3
    }, 16, ["title"]));
  }
});
var Ch = defineComponent({
  name: "FsSearchButtons",
  inheritAttrs: false,
  props: {
    buttons: {
      type: Object
    }
  },
  setup() {
    const { ui: e } = B();
    return { ui: e };
  }
});
var Sh = { class: "fs-search-col fs-search-btns" };
function Fh(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-button");
  return openBlock(), createElementBlock("div", Sh, [
    (openBlock(), createBlock(resolveDynamicComponent(e.ui.formItem.name), null, {
      default: withCtx(() => [
        (openBlock(true), createElementBlock(Fragment, null, renderList(e.buttons, (s, u) => (openBlock(), createElementBlock(Fragment, { key: u }, [
          s.show ? (openBlock(), createBlock(i2, mergeProps({
            key: 0,
            ref_for: true
          }, s, {
            onClick: (l) => s._click()
          }), null, 16, ["onClick"])) : createCommentVNode("", true)
        ], 64))), 128))
      ]),
      _: 1
    }))
  ]);
}
var Bi = ke(Ch, [["render", Fh]]);
var Rh = defineComponent({
  name: "FsSearch",
  components: {
    FsSearchButtons: Bi
  },
  inheritAttrs: false,
  props: {
    /**
     * 布局容器
     */
    container: {
      type: Object
    },
    /**
     * 初始查询条件
     * 点击重置，会重置成该条件
     */
    initialForm: {
      type: Object
    },
    /**
     * 校验后的查询表单数据
     */
    validatedForm: {
      type: Object
    },
    /**
     * 表单参数
     * 支持el-form | a-form的属性
     */
    options: {
      type: Object
    },
    /**
     * 查询字段配置
     */
    columns: {
      type: Object
    },
    /**
     * 按钮配置,可以根据order排序
     * `{search:{...FsButton},reset:{...FsButton}}`
     */
    buttons: {
      type: Object
    },
    /**
     * 点击重置后是否立即触发查询
     */
    searchAfterReset: {
      type: Boolean,
      default: true
    },
    /**
     * 是否开启自动查询
     */
    autoSearch: {
      type: Boolean,
      default: true
    },
    /**
     * 自动查询，防抖设置
     * 传false则关闭自动查询
     */
    debounce: {
      type: [Boolean, Object],
      default: void 0
    },
    /**
     * 插槽
     */
    slots: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 是否显示查询框
     */
    show: {
      type: Boolean,
      default: true
    },
    /**
     * 是否启用校验
     */
    validate: {
      default: false
    },
    /**
     * 是否任意值变化就触发校验
     */
    validateOnChange: {
      default: true,
      type: Boolean
    },
    /**
     * 列的宽度设置，span=xx
     */
    col: {},
    /**
     * 统一字段的formItem属性
     */
    formItem: {
      type: Object,
      default: void 0
    }
  },
  emits: [
    /**
     * 查询事件
     **/
    "search",
    /**
     * 查询事件，此事件供系统调用
     */
    "_search",
    /**
     * 重置事件，供用户使用
     **/
    "reset",
    /**
     * 重置事件，此事件供系统调用
     */
    "_reset",
    /**
     * 校验失败事件
     */
    "validate-error",
    /**
     * 校验后的表单数据变化
     */
    "update:validatedForm"
  ],
  setup(e, t) {
    const {
      ui: n
    } = B(), {
      merge: o
    } = De(), r = o, {
      doComputed: a,
      AsyncComputeValue: i2,
      ComputeValue: s
    } = Nt();
    forEach_default(e.columns, (p) => {
      p.value != null && (p.value instanceof i2 || p.value instanceof s) && ue.warn("search.value配置不支持ComputeValue/AsyncCompute类型的动态计算");
    });
    function u() {
      const p = {};
      return forEach_default(e.columns, (_, C) => {
        var T;
        if (_.value === void 0)
          return;
        const S = unref(_.value);
        S !== void 0 && _.show !== false && ((T = _.component) == null ? void 0 : T.show) !== false && (p[C] = S);
      }), cloneDeep_default(o({}, e.initialForm, p));
    }
    const l = reactive(u());
    let c = null;
    const d = a(() => e.columns, A, null, (p) => {
      const _ = cloneDeep_default(e.formItem || {});
      forEach_default(p, (T) => {
        o(T, _, T);
      }), e.validate || forEach_default(p, (T) => {
        delete T.rules, delete T.rule;
      }), e.col && forEach_default(p, (T) => {
        T.col = o({}, e.col, T.col);
      }), forEach_default(p, (T) => {
        T._cellRender = () => g(T);
      });
      let C = [];
      forEach_default(p, (T, ne) => {
        T._key = ne, C.push(T);
      }), C = sortBy_default(C, (T) => T.order ?? nn.orderDefault);
      const S = {};
      return C.forEach((T) => {
        let ne = T._key;
        delete T._key, S[ne] = T;
      }), S;
    });
    function f() {
      const p = cloneDeep_default(l);
      t.emit("update:validatedForm", p);
    }
    watch(() => e.validatedForm, (p) => {
      for (const _ in l)
        delete l[_];
      o(l, p || {});
    }, {
      deep: true
    });
    const h2 = (p, _) => get_default(p, _);
    function v(p) {
      if (p != null)
        return p.indexOf(".") >= 0 ? p.split(".") : p;
    }
    function g(p) {
      const _ = p.key;
      async function C(W) {
        Se(W, p);
      }
      function S() {
        ye(p);
      }
      function T(W, oe) {
        oe.code === "Enter" && W.autoSearchTrigger === "enter" && B2();
      }
      let ne = null;
      e.slots["search_" + _] ? ne = createVNode(resolveComponent("fs-slot-render"), {
        slots: e.slots["search_" + _],
        scope: N(_)
      }, null) : p.render ? ne = createVNode(resolveComponent("fs-render"), {
        "render-func": p.render,
        scope: N(_)
      }, null) : p.component && p.component.show !== false && (ne = createVNode(resolveComponent("fs-component-render"), mergeProps({
        ref: (W) => {
          R.value[_] = W;
        },
        "model-value": h2(l, _),
        onKeyup: (W) => {
          T(p, W);
        }
      }, p.component, {
        scope: N(_),
        "onUpdate:modelValue": C,
        onInput: S
      }), null));
      const P = v(_);
      return n.formItem.render({
        props: {
          ...p,
          label: p.title,
          [n.formItem.prop]: P,
          path: _,
          rulePath: _
        },
        slots: {
          default() {
            return ne;
          }
        }
      });
    }
    const w = ref(), {
      t: y
    } = ot(), R = ref({});
    function F(p) {
      return R.value[p];
    }
    function k(p) {
      var _;
      return (_ = F(p)) == null ? void 0 : _.getTargetRef();
    }
    function A() {
      return {
        form: l,
        validatedForm: e.validatedForm,
        getComponentRef: k,
        doSearch: B2,
        doReset: D,
        doValidate: $
      };
    }
    function N(p) {
      return {
        ...A(),
        key: p,
        value: h2(l, p)
      };
    }
    async function $(p = false, _ = "search") {
      try {
        return e.validate && await n.form.validateWrap(w.value), true;
      } catch (C) {
        return p || t.emit("validate-error", {
          ...A(),
          error: C,
          trigger: _
        }), false;
      }
    }
    async function B2() {
      c && c.cancel(), await $() && (f(), await nextTick(), t.emit("_search", A()), t.emit("search", A()));
    }
    async function D() {
      const p = u(), _ = toPairs_default(l);
      for (const C of _) {
        const S = h2(p, C[0]);
        S == null ? unset_default(l, C[0]) : set_default(l, C[0], S);
      }
      await $() && (f(), await nextTick(), e.reset && e.reset(A()), t.emit("_reset", A()), t.emit("reset", A()), e.searchAfterReset && B2());
    }
    const x = computed(() => {
      const p = [], _ = {
        search: {
          show: true,
          type: "primary",
          disabled: false,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          click: (C) => {
            B2();
          },
          order: 1,
          text: y("fs.search.search.text")
          // '查询',
        },
        reset: {
          show: true,
          disabled: false,
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          click: (C) => {
            D();
          },
          text: y("fs.search.reset.text"),
          // '重置',
          order: 2
        }
      };
      o(_, e.buttons);
      for (let C in _) {
        const S = _[C];
        S._click = () => {
          S.click(A());
        }, p.push(S);
      }
      return p.sort((C, S) => C.order - S.order), p;
    });
    function I() {
      var p;
      if (e.autoSearch !== false && e.debounce !== false) {
        let _ = ((p = e.debounce) == null ? void 0 : p.wait) || 500;
        c = debounce_default(B2, _, e.debounce);
      }
    }
    I();
    function V() {
      return l;
    }
    function O() {
      return e.validatedForm;
    }
    function M(p, _ = true) {
      _ || forEach_default(keys_default(l), (C) => {
        delete l[C];
      }), r(l, p), f();
    }
    const X = ref(false), le = () => {
      ue.debug("do auto search,inputEventDisabled:", X.value), X.value !== true && c && c();
    }, ye = (p) => {
      p.autoSearchTrigger === "input" && le();
    }, me = (p) => {
      X.value = p, le();
    };
    async function Se(p, _) {
      const C = _.key;
      set_default(l, C, p);
      const S = e.validateOnChangeSilent;
      if (_.valueChange) {
        const T = _.key, ne = l[T], P = k(T), W = _.valueChange instanceof Function ? _.valueChange : _.valueChange.handle, oe = A(), Z = {
          index: 0,
          row: oe.form,
          form: oe.form,
          ...oe,
          key: T,
          value: ne,
          componentRef: P,
          immediate: false,
          getComponentRef: k,
          mode: "search"
        };
        W(Z);
      }
      e.validateOnChange && await $(S, "change") && f(), (_.autoSearchTrigger == null || _.autoSearchTrigger === true || _.autoSearchTrigger === "change") && le();
    }
    const G = computed(() => e.validate ? e.options.rules : []);
    return f(), {
      get: h2,
      ui: n,
      onValueChanged: Se,
      doValidate: $,
      doSearch: B2,
      doReset: D,
      formData: l,
      componentRenderRefs: R,
      getComponentRenderRef: F,
      getComponentRef: k,
      getForm: V,
      getValidatedForm: O,
      setForm: M,
      searchFormRef: w,
      onInput: ye,
      inputEventDisabled: X,
      changeInputEventDisabled: me,
      computedColumns: d,
      computedButtons: x,
      computedRules: G,
      buildFieldContext: N,
      getContextFn: A
    };
  }
});
var Dh = { class: "fs-search fs-search-v2" };
var kh = { class: "fs-search-buttons" };
function $h(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-button"), s = resolveComponent("fs-slot-render");
  return openBlock(), createElementBlock("div", Dh, [
    (openBlock(), createBlock(resolveDynamicComponent(e.ui.collapseTransition.name), null, {
      default: withCtx(() => [
        (openBlock(), createBlock(resolveDynamicComponent(e.ui.form.name), mergeProps({
          ref: "searchFormRef",
          model: e.formData,
          onsubmit: "event.preventDefault();"
        }, e.options, {
          rules: e.computedRules,
          class: "fs-search-form",
          onCompositionstart: t[0] || (t[0] = (u) => e.changeInputEventDisabled(true)),
          onCompositionend: t[1] || (t[1] = (u) => e.changeInputEventDisabled(false))
        }), {
          default: withCtx(() => {
            var u;
            return [
              e.show !== false ? (openBlock(), createBlock(resolveDynamicComponent(((u = e.container) == null ? void 0 : u.is) || "fs-search-layout-default"), mergeProps({ key: 0 }, e.container, {
                columns: e.computedColumns,
                "get-context-fn": e.getContextFn
              }), createSlots({
                "search-buttons": withCtx(() => [
                  createBaseVNode("div", kh, [
                    (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedButtons, (l, c) => (openBlock(), createElementBlock(Fragment, { key: c }, [
                      l.show !== false ? (openBlock(), createBlock(i2, mergeProps({
                        key: 0,
                        ref_for: true
                      }, l, {
                        onClick: (d) => l._click()
                      }), null, 16, ["onClick"])) : createCommentVNode("", true)
                    ], 64))), 128))
                  ])
                ]),
                _: 2
              }, [
                e.slots["search-left"] ? {
                  name: "search-left",
                  fn: withCtx(() => [
                    createVNode(s, {
                      slots: e.slots["search-left"],
                      scope: e.getContextFn()
                    }, null, 8, ["slots", "scope"])
                  ]),
                  key: "0"
                } : void 0,
                e.slots["search-middle"] ? {
                  name: "search-middle",
                  fn: withCtx(() => [
                    createVNode(s, {
                      slots: e.slots["search-middle"],
                      scope: e.getContextFn()
                    }, null, 8, ["slots", "scope"])
                  ]),
                  key: "1"
                } : void 0,
                e.slots["search-right"] ? {
                  name: "search-right",
                  fn: withCtx(() => [
                    createVNode(s, {
                      slots: e.slots["search-right"],
                      scope: e.getContextFn()
                    }, null, 8, ["slots", "scope"])
                  ]),
                  key: "2"
                } : void 0
              ]), 1040, ["columns", "get-context-fn"])) : createCommentVNode("", true)
            ];
          }),
          _: 1
        }, 16, ["model", "rules"]))
      ]),
      _: 1
    }))
  ]);
}
var Oh = ke(Rh, [["render", $h]]);
var Eh = defineComponent({
  name: "FsSearchV1",
  components: { FsSearchButtons: Bi },
  inheritAttrs: false,
  props: {
    /**
     * 初始查询条件
     * 点击重置，会重置成该条件
     */
    initialForm: {
      type: Object
    },
    /**
     * 表单参数
     * 支持el-form | a-form的属性
     */
    options: {
      type: Object
    },
    /**
     * 查询字段配置
     */
    columns: {
      type: Object
    },
    /**
     * tabs
     * { show , options,key, default}
     */
    tabs: {
      type: Object
    },
    /**
     * 按钮配置,可以根据order排序
     * {search:{...FsButton},reset:{...FsButton}}
     */
    buttons: {
      type: Object
    },
    /**
     * 点击重置后是否立即触发查询
     */
    searchAfterReset: {
      type: Boolean,
      default: true
    },
    /**
     * 是否开启自动查询
     */
    autoSearch: {
      type: Boolean,
      default: true
    },
    /**
     * 自动查询，防抖设置
     * 传false则关闭自动查询
     */
    debounce: {
      type: [Boolean, Object],
      default: void 0
    },
    /**
     * 插槽
     */
    slots: {
      default() {
        return {};
      }
    },
    /**
     * 是否显示查询框
     */
    show: {
      type: Boolean,
      default: true
    },
    /**
     * 是否启用校验
     */
    validate: {
      default: false
    },
    /**
     * 布局, single-line 单行， multi-line 多行（支持展开收起）
     */
    layout: {
      type: String,
      default: "single-line"
    },
    /**
     * 列的宽度设置，span=xx
     */
    col: {},
    /**
     * 是否折叠
     */
    collapse: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    /**
     * 查询事件
     **/
    "search",
    /**
     * 重置事件
     **/
    "reset",
    "collapse",
    "update:collapse"
  ],
  setup(e, t) {
    const { ui: n } = B();
    onMounted(() => {
      if (Se.value && M.value) {
        X.value = M.value.$el.offsetHeight;
        const p = M.value.$el.children;
        p && p.length > 1 && (le.value = p[1].offsetHeight + 2);
      }
    });
    let o = null;
    function r() {
      return cloneDeep(e.initialForm || {});
    }
    const a = reactive(r()), { doComputed: i2, AsyncComputeValue: s } = Nt();
    forEach_default(e.columns, (p) => {
      p.value != null && p.value instanceof s && ue.warn("search.value配置不支持AsyncCompute类型的动态计算");
    });
    const { merge: u } = De(), l = u, c = i2(
      () => e.columns,
      w,
      null,
      (p) => {
        e.validate || forEach_default(p, (S) => {
          delete S.rules;
        }), e.col && forEach_default(p, (S) => {
          S.col = u({}, e.col, S.col);
        });
        let _ = [];
        forEach_default(p, (S, T) => {
          S._key = T, _.push(S);
        }), _ = sortBy_default(_, (S) => S.order ?? nn.orderDefault);
        const C = {};
        return _.forEach((S) => {
          let T = S._key;
          delete S._key, C[T] = S;
        }), C;
      }
    );
    forEach_default(c.value, (p, _) => {
      var S;
      if (p.value === void 0)
        return;
      const C = unref(p.value);
      C !== void 0 && p.show !== false && ((S = p.component) == null ? void 0 : S.show) !== false && (a[_] = C);
    });
    const d = ref(), { t: f } = ot(), h2 = ref({});
    function v(p) {
      return h2.value[p];
    }
    function g(p) {
      var _, C;
      return (C = (_ = v(p)) == null ? void 0 : _.$refs) == null ? void 0 : C.targetRef;
    }
    function w() {
      return { form: a, validatedForm: a, getComponentRef: g, doValidate: null };
    }
    const y = ref(w());
    async function R() {
      if (o && o.cancel(), await n.form.validateWrap(d.value))
        t.emit("search", y.value);
      else
        return n.message.error({
          message: f("fs.search.error.message")
        }), false;
    }
    function F() {
      const p = r(), _ = toPairs_default(a);
      for (const C of _) {
        const S = get_default(p, C[0]);
        S == null ? unset_default(a, C[0]) : set_default(a, C[0], S);
      }
      e.reset && e.reset(y.value), t.emit("reset", w()), e.searchAfterReset && nextTick(() => {
        R();
      });
    }
    const k = computed(() => {
      const p = [], _ = {
        search: {
          show: true,
          type: "primary",
          disabled: false,
          click: (C) => {
            R();
          },
          order: 1,
          text: f("fs.search.search.text")
          // '查询',
        },
        reset: {
          show: true,
          disabled: false,
          click: (C) => {
            F();
          },
          text: f("fs.search.reset.text"),
          // '重置',
          order: 2
        }
      };
      u(_, e.buttons);
      for (let C in _) {
        const S = _[C];
        S._click = () => {
          S.click(w());
        }, p.push(S);
      }
      return p.sort((C, S) => C.order - S.order), p;
    });
    function A() {
      var p;
      if (e.autoSearch !== false && e.debounce !== false) {
        let _ = ((p = e.debounce) == null ? void 0 : p.wait) || 500;
        o = debounce(R, _, e.debounce);
      }
    }
    A();
    function N() {
      return a;
    }
    function $(p, _ = true) {
      _ || forEach_default(keys_default(a), (C) => {
        delete a[C];
      }), l(a, p);
    }
    const B2 = ref(false), D = () => {
      ue.debug("do auto search,inputEventDisabled:", B2.value), B2.value !== true && o && o();
    }, x = (p) => {
      p.autoSearchTrigger === "input" && D();
    }, I = (p) => {
      B2.value = p, D();
    };
    function V(p, _) {
      const C = _.key;
      if (set_default(a, C, p), _.valueChange) {
        const S = _.key, T = a[S], ne = g(S);
        (_.valueChange instanceof Function ? _.valueChange : _.valueChange.handle)({ key: S, value: T, componentRef: ne, ...w(), immidiate: false });
      }
      (_.autoSearchTrigger == null || _.autoSearchTrigger === true || _.autoSearchTrigger === "change") && D();
    }
    const O = computed(() => e.validate ? e.options.rules : []), M = ref(), X = ref(0), le = ref(0), ye = () => {
      t.emit("update:collapse", !e.collapse), t.emit("collapse", !e.collapse);
    }, me = computed(() => e.layout === "multi-line" ? n.col.name : "div"), Se = computed(() => e.layout === "multi-line"), G = computed(() => Se.value ? e.collapse ? le.value ? le.value + "px" : "" : X.value ? X.value + "px" : "" : "auto");
    return {
      get: (p, _) => get_default(p, _),
      ui: n,
      onValueChanged: V,
      doSearch: R,
      doReset: F,
      form: a,
      componentRenderRefs: h2,
      getComponentRenderRef: v,
      getComponentRef: g,
      getForm: N,
      setForm: $,
      searchFormRef: d,
      onInput: x,
      inputEventDisabled: B2,
      changeInputEventDisabled: I,
      computedColumns: c,
      computedButtons: k,
      computedRules: O,
      columnsRowRef: M,
      computedColumnBoxHeight: G,
      computedColName: me,
      computedIsMultiLine: Se,
      toggleCollapse: ye,
      searchEventContextRef: y
    };
  }
});
var Th = { class: "fs-search-box" };
var Ah = { class: "fs-search-main" };
var Ih = {
  key: 0,
  class: "fs-search-col"
};
var Vh = {
  key: 1,
  class: "fs-search-col fs-search-middle"
};
var Ph = {
  key: 3,
  class: "fs-search-col fs-search-right"
};
var Mh = {
  key: 0,
  class: "fs-search-action"
};
function Bh(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-slot-render"), s = resolveComponent("fs-component-render"), u = resolveComponent("fs-search-buttons"), l = resolveComponent("fs-button");
  return openBlock(), createBlock(resolveDynamicComponent(e.ui.collapseTransition.name), null, {
    default: withCtx(() => [
      e.show !== false ? (openBlock(), createElementBlock("div", {
        key: 0,
        class: normalizeClass(["fs-search", { "fs-search-multi-line": e.computedIsMultiLine }])
      }, [
        (openBlock(), createBlock(resolveDynamicComponent(e.ui.form.name), mergeProps({
          ref: "searchFormRef",
          model: e.form
        }, e.options, {
          rules: e.computedRules,
          class: "fs-search-form",
          onCompositionstart: t[0] || (t[0] = (c) => e.changeInputEventDisabled(true)),
          onCompositionend: t[1] || (t[1] = (c) => e.changeInputEventDisabled(false))
        }), {
          default: withCtx(() => [
            createBaseVNode("div", Th, [
              createBaseVNode("div", Ah, [
                createBaseVNode("div", {
                  class: normalizeClass(["fs-search-columns", { "fs-search-collapse": e.collapse }]),
                  style: normalizeStyle({ height: e.computedColumnBoxHeight })
                }, [
                  (openBlock(), createBlock(resolveDynamicComponent(e.ui.row.name), { ref: "columnsRowRef" }, {
                    default: withCtx(() => [
                      e.slots["search-left"] ? (openBlock(), createElementBlock("div", Ih, [
                        (openBlock(), createBlock(resolveDynamicComponent(e.ui.formItem.name), null, {
                          default: withCtx(() => [
                            createVNode(i2, {
                              slots: e.slots["search-left"],
                              scope: e.searchEventContextRef
                            }, null, 8, ["slots", "scope"])
                          ]),
                          _: 1
                        }))
                      ])) : createCommentVNode("", true),
                      (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedColumns, (c, d) => (openBlock(), createElementBlock(Fragment, { key: d }, [
                        c.show === true ? (openBlock(), createBlock(resolveDynamicComponent(e.computedColName), mergeProps({
                          key: 0,
                          class: "fs-search-col",
                          ref_for: true
                        }, c.col), {
                          default: withCtx(() => [
                            (openBlock(), createBlock(resolveDynamicComponent(e.ui.formItem.name), mergeProps({ ref_for: true }, c, {
                              [e.ui.formItem.prop || ""]: d,
                              label: c.title
                            }), {
                              default: withCtx(() => [
                                e.slots["search_" + d] ? (openBlock(), createBlock(i2, {
                                  key: 0,
                                  slots: e.slots["search_" + d],
                                  scope: { ...e.searchEventContextRef, key: d }
                                }, null, 8, ["slots", "scope"])) : (openBlock(), createElementBlock(Fragment, { key: 1 }, [
                                  c.component && c.component.show !== false ? (openBlock(), createBlock(s, mergeProps({
                                    key: 0,
                                    ref_for: true,
                                    ref: (f) => {
                                      f && (e.componentRenderRefs[c.key] = f);
                                    },
                                    "model-value": e.get(e.form, d)
                                  }, c.component, {
                                    scope: e.searchEventContextRef,
                                    "onUpdate:modelValue": (f) => e.onValueChanged(f, c),
                                    onInput: (f) => e.onInput(c)
                                  }), null, 16, ["model-value", "scope", "onUpdate:modelValue", "onInput"])) : createCommentVNode("", true)
                                ], 64))
                              ]),
                              _: 2
                            }, 1040, ["label"]))
                          ]),
                          _: 2
                        }, 1040)) : createCommentVNode("", true)
                      ], 64))), 128)),
                      e.slots["search-middle"] ? (openBlock(), createElementBlock("div", Vh, [
                        (openBlock(), createBlock(resolveDynamicComponent(e.ui.formItem.name), null, {
                          default: withCtx(() => [
                            createVNode(i2, {
                              slots: e.slots["search-middle"],
                              scope: e.searchEventContextRef
                            }, null, 8, ["slots", "scope"])
                          ]),
                          _: 1
                        }))
                      ])) : createCommentVNode("", true),
                      e.computedIsMultiLine ? createCommentVNode("", true) : (openBlock(), createBlock(u, {
                        key: 2,
                        buttons: e.computedButtons
                      }, null, 8, ["buttons"])),
                      e.slots["search-right"] ? (openBlock(), createElementBlock("div", Ph, [
                        (openBlock(), createBlock(resolveDynamicComponent(e.ui.formItem.name), null, {
                          default: withCtx(() => [
                            createVNode(i2, {
                              slots: e.slots["search-right"],
                              scope: e.searchEventContextRef
                            }, null, 8, ["slots", "scope"])
                          ]),
                          _: 1
                        }))
                      ])) : createCommentVNode("", true)
                    ]),
                    _: 1
                  }, 512))
                ], 6),
                e.computedIsMultiLine ? (openBlock(), createBlock(u, {
                  key: 0,
                  buttons: e.computedButtons
                }, null, 8, ["buttons"])) : createCommentVNode("", true)
              ]),
              e.computedIsMultiLine ? (openBlock(), createElementBlock("div", Mh, [
                (openBlock(), createBlock(resolveDynamicComponent(e.ui.formItem.name), null, {
                  default: withCtx(() => [
                    createVNode(l, {
                      icon: e.collapse ? e.ui.icons.caretUp : e.ui.icons.caretDown,
                      onClick: e.toggleCollapse
                    }, null, 8, ["icon", "onClick"])
                  ]),
                  _: 1
                }))
              ])) : createCommentVNode("", true)
            ])
          ]),
          _: 1
        }, 16, ["model", "rules"]))
      ], 2)) : createCommentVNode("", true)
    ]),
    _: 1
  });
}
var jh = ke(Eh, [["render", Bh]]);
var Nh = defineComponent({
  name: "FsSearchLayoutDefault",
  props: {
    /**
     * 是否收缩
     */
    collapse: {
      type: Boolean,
      default: true
    },
    /**
     * 展开按钮
     */
    collapseButton: {
      type: Object
    },
    action: {
      type: Object
    },
    /**
     * 布局模式
     */
    layout: {
      type: String,
      default: "single-line"
    },
    /**
     * 查询字段列表，可以精细化自定义查询字段布局
     */
    columns: {
      type: Object
    },
    /**
     * 默认的col配置
     */
    col: {
      type: Object
    },
    /**
     * 获取查询上下文
     */
    getContextFn: {
      type: Function
    }
  },
  emits: ["update:collapse", "collapse"],
  setup(e, t) {
    const { ui: n } = B(), { t: o } = ot(), r = ref(), a = computed(() => e.layout === "multi-line");
    onMounted(() => {
      a.value && r.value && (s.value = r.value.$el.offsetHeight);
    });
    const i2 = computed(() => !a.value || !e.collapse ? "auto" : s.value ? s.value + "px" : ""), s = ref(0), u = () => {
      t.emit("update:collapse", !e.collapse), t.emit("collapse", !e.collapse);
    };
    function l(c) {
      return merge_default({}, e.col, c);
    }
    return {
      ui: n,
      columnsRowRef: r,
      computedColumnBoxHeight: i2,
      computedIsMultiLine: a,
      toggleCollapse: u,
      mergeCol: l,
      t: o
    };
  }
});
var Lh = { class: "fs-search-box" };
var qh = { class: "fs-search-col fs-search-slot" };
var xh = { class: "fs-search-col fs-search-slot" };
var Hh = { class: "fs-search-col fs-search-slot" };
var zh = {
  key: 0,
  class: "fs-search-buttons-group fs-search-multi-line-buttons"
};
function Wh(e, t, n, o, r, a) {
  var u;
  const i2 = resolveComponent("fs-render"), s = resolveComponent("fs-button");
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["fs-search-layout-default", { "fs-search-multi-line": e.computedIsMultiLine }])
  }, [
    createBaseVNode("div", Lh, [
      createBaseVNode("div", {
        class: normalizeClass(["fs-search-main", { "fs-search-collapse": e.collapse }]),
        style: normalizeStyle({ maxHeight: e.computedColumnBoxHeight })
      }, [
        (openBlock(), createBlock(resolveDynamicComponent(e.ui.row.name), {
          ref: "columnsRowRef",
          class: "fs-search-columns"
        }, {
          default: withCtx(() => {
            var l;
            return [
              createBaseVNode("span", qh, [
                renderSlot(e.$slots, "search-left")
              ]),
              (openBlock(true), createElementBlock(Fragment, null, renderList(e.columns, (c, d) => (openBlock(), createElementBlock(Fragment, { key: d }, [
                c.show ? (openBlock(), createBlock(resolveDynamicComponent(e.ui.col.name), mergeProps({
                  key: 0,
                  class: "fs-search-col",
                  ref_for: true
                }, e.mergeCol(c.col)), {
                  default: withCtx(() => [
                    createVNode(i2, {
                      "render-func": c._cellRender,
                      scope: e.getContextFn()
                    }, null, 8, ["render-func", "scope"])
                  ]),
                  _: 2
                }, 1040)) : createCommentVNode("", true)
              ], 64))), 128)),
              createBaseVNode("span", xh, [
                renderSlot(e.$slots, "search-middle")
              ]),
              e.computedIsMultiLine ? createCommentVNode("", true) : (openBlock(), createBlock(resolveDynamicComponent(e.ui.col.name), mergeProps({
                key: 0,
                class: "fs-search-col fs-search-buttons-group"
              }, e.mergeCol((l = e.action) == null ? void 0 : l.col)), {
                default: withCtx(() => {
                  var c;
                  return [
                    (openBlock(), createBlock(resolveDynamicComponent(e.ui.formItem.name), normalizeProps({
                      [e.ui.formItem.label || ""]: (c = e.action) == null ? void 0 : c.label
                    }), {
                      default: withCtx(() => [
                        renderSlot(e.$slots, "search-buttons", normalizeProps(guardReactiveProps(e.getContextFn())))
                      ]),
                      _: 3
                    }, 16))
                  ];
                }),
                _: 3
              }, 16)),
              createBaseVNode("span", Hh, [
                renderSlot(e.$slots, "search-right", normalizeProps(guardReactiveProps(e.getContextFn())))
              ])
            ];
          }),
          _: 3
        }, 512))
      ], 6),
      e.computedIsMultiLine ? (openBlock(), createElementBlock("div", zh, [
        renderSlot(e.$slots, "search-buttons"),
        ((u = e.collapseButton) == null ? void 0 : u.show) !== false ? (openBlock(), createBlock(s, mergeProps({
          key: 0,
          icon: e.collapse ? e.ui.icons.caretUp : e.ui.icons.caretDown,
          text: e.collapse ? e.t("fs.search.container.collapseButton.text.expand") : e.t("fs.search.container.collapseButton.text.collapse")
        }, e.collapseButton, { onClick: e.toggleCollapse }), null, 16, ["icon", "text", "onClick"])) : createCommentVNode("", true)
      ])) : createCommentVNode("", true)
    ])
  ], 2);
}
var Uh = ke(Nh, [["render", Wh]]);
var Kh = { class: "fs-tabs-filter" };
var Yh = defineComponent({
  name: "FsTabsFilter",
  inheritAttrs: false
});
var Gh = defineComponent({
  ...Yh,
  props: {
    name: {},
    show: { type: Boolean, default: false },
    defaultOption: { default: void 0 },
    options: { default: () => [] },
    modelValue: {},
    value: { default: "value" },
    label: { default: "label" }
  },
  emits: ["update:modelValue", "change"],
  setup(e, { emit: t }) {
    const n = useAttrs(), { merge: o } = De(), r = e, a = computed(() => o({
      show: true,
      value: null,
      label: "全部"
    }, r.defaultOption || {})), i2 = "_default_key_", s = computed(() => {
      const h2 = r.modelValue == null || r.modelValue === a.value.value ? i2 : r.modelValue;
      return {
        type: "card",
        ...n,
        [f.tabs.modelValue]: h2,
        ["onUpdate:" + f.tabs.modelValue]: l
      };
    }), u = t;
    function l(h2) {
      i2 === h2 && (h2 = a.value.value);
      const v = r.modelValue;
      u("update:modelValue", h2), v !== h2 && u("change", h2);
    }
    function c(h2) {
      return h2[r.value];
    }
    function d(h2) {
      return h2[r.label];
    }
    const { ui: f } = B();
    return (h2, v) => (openBlock(), createElementBlock("div", Kh, [
      (openBlock(), createBlock(resolveDynamicComponent(unref(f).tabs.name), normalizeProps(guardReactiveProps(s.value)), {
        default: withCtx(() => [
          a.value.show ? (openBlock(), createBlock(resolveDynamicComponent(unref(f).tabPane.name), normalizeProps({
            key: 0,
            [unref(f).tabPane.key || ""]: i2,
            [unref(f).tabPane.tab || ""]: a.value.label
          }), null, 16)) : createCommentVNode("", true),
          (openBlock(true), createElementBlock(Fragment, null, renderList(h2.options, (g, w) => (openBlock(), createBlock(resolveDynamicComponent(unref(f).tabPane.name), normalizeProps({
            key: w,
            [unref(f).tabPane.key || ""]: c(g),
            [unref(f).tabPane.tab || ""]: d(g)
          }), null, 16))), 128))
        ]),
        _: 1
      }, 16))
    ]));
  }
});
var Xh = { class: "fs-icon-selector" };
var Qh = {
  key: 0,
  class: "fs-icon-selector-dialog"
};
var Jh = { class: "fs-icon-selector-dialog-content mb-4" };
var Zh = { class: "icon-tabs-box mt-10 mb-10" };
var em = { class: "icon-container" };
var tm = { class: "icon-list" };
var nm = ["title", "onClick", "onDblclick"];
var om = { class: "load-more" };
var rm = { key: 1 };
var am = { class: "footer" };
var im = defineComponent({
  name: "FsIconSelector"
});
var sm = defineComponent({
  ...im,
  props: {
    modelValue: {
      type: String,
      default: ""
    },
    dialog: {
      type: Object,
      default: () => ({})
    },
    tabs: {
      type: Object,
      default: () => ({})
    },
    // 限制每页显示数量
    limit: {
      type: Number,
      default: 136
    },
    iconSets: {
      type: Array,
      default: () => ["carbon", "ion", "ant-design", "fa-solid", "fa-brands", "fa-regular", "mdi"]
    },
    apiProvider: {
      type: String,
      default: "https://api.iconify.design"
    }
  },
  emits: ["update:modelValue"],
  setup(e, { emit: t }) {
    const n = t, o = 136, r = e, { ui: a } = B(), i2 = ref(false), s = computed(() => ({
      width: 1024,
      style: {
        width: "1024px"
      },
      ...r.dialog,
      [a.dialog.visible]: i2.value,
      [`onUpdate:${a.dialog.visible}`]: (O) => {
        i2.value = O;
      }
    })), u = a.formItem.injectFormItemContext(), l = computed(() => ({
      ...f,
      [a.input.modelValue]: r.modelValue,
      [`onUpdate:${a.input.modelValue}`]: (O) => {
        c(O);
      }
    })), c = (O) => {
      n("update:modelValue", O), u == null || u.onChange();
    }, d = () => {
      i2.value = true, r.iconSets.length > 0 && k.value.records.length === 0 && R(r.iconSets[0]);
    }, f = useAttrs(), h2 = {
      async getCollections() {
        return await (await fetch(`${r.apiProvider}/collections`)).json();
      },
      async getIcons(O) {
        return await (await fetch(`${r.apiProvider}/collection?prefix=${O}`)).json();
      },
      async search(O, M) {
        const X = M.start ?? 0, le = M.limit ?? 50;
        return await (await fetch(`${r.apiProvider}/search?query=${O}&start=${X}&limit=${le}`)).json();
      }
    }, v = ref({}), g = async (O) => {
      let M = v.value[O];
      if (!M) {
        const X = await h2.getIcons(O), le = X.uncategorized ?? [], ye = X.categories ?? [];
        M = le;
        for (const me in ye)
          M = M.concat(ye[me]);
        M = M.map((me) => `${O}:${me}`), v.value[O] = M;
      }
      return M;
    }, w = ref(""), y = ref("all"), R = (O) => {
      y.value = O, w.value = "", F(), A();
    }, F = () => {
      k.value.start = 0, k.value.records = [], k.value.total = 0, k.value.limit = r.limit ?? o;
    }, k = ref({
      loading: false,
      start: 0,
      limit: r.limit ?? o,
      total: null,
      query: "",
      records: []
    }), A = async () => {
      if (!k.value.loading) {
        k.value.query !== w.value && F(), k.value.loading = true;
        try {
          const O = await N();
          k.value.records = k.value.records.concat(O.icons), k.value.total = O.total, k.value.limit = O.limit, k.value.query = w.value;
        } finally {
          k.value.loading = false;
        }
      }
    };
    async function N() {
      if (y.value === "all") {
        if (!w.value) {
          a.notification.warn("请输入搜索关键字");
          return;
        }
        return await h2.search(w.value, k.value);
      } else
        return await $(y.value);
    }
    async function $(O) {
      const M = await g(O);
      let X = M;
      w.value && (X = M.filter((me) => me.includes(w.value)));
      let le = k.value.start + k.value.limit;
      return le > X.length && (le = X.length), {
        icons: X.slice(k.value.start, le),
        total: X.length,
        limit: k.value.limit,
        start: k.value.start
      };
    }
    const B2 = ref(r.modelValue), D = (O, M = false) => {
      B2.value = O, M && x();
    }, x = () => {
      i2.value = false, c(B2.value);
    }, I = async () => {
      k.value.start += k.value.limit, await A();
    }, V = computed(() => ({
      ...r.tabs,
      [a.tabs.modelValue]: y.value,
      [`onUpdate:${a.tabs.modelValue}`]: R
    }));
    return (O, M) => {
      const X = resolveComponent("fs-icon"), le = resolveComponent("fs-button"), ye = resolveComponent("fs-loading");
      return openBlock(), createElementBlock("div", Xh, [
        (openBlock(), createBlock(resolveDynamicComponent(unref(a).input.name), mergeProps(l.value, { onClick: d }), {
          prefix: withCtx(() => [
            e.modelValue ? (openBlock(), createBlock(X, {
              key: 0,
              class: "fs-icon-selector-input-prefix",
              icon: e.modelValue
            }, null, 8, ["icon"])) : createCommentVNode("", true)
          ]),
          _: 1
        }, 16)),
        i2.value ? (openBlock(), createElementBlock("div", Qh, [
          (openBlock(), createBlock(resolveDynamicComponent(unref(a).dialog.name), mergeProps({ preset: "dialog" }, s.value, { footer: null }), {
            [unref(a).dialog.titleSlotName]: withCtx(() => [
              createVNode(X, {
                icon: "icon-select",
                class: "mr-2"
              }),
              M[1] || (M[1] = createTextVNode(" 选择图标 "))
            ]),
            default: withCtx(() => [
              createBaseVNode("div", Jh, [
                createBaseVNode("div", Zh, [
                  (openBlock(), createBlock(resolveDynamicComponent(unref(a).tabs.name), mergeProps(V.value, { type: "card" }), {
                    default: withCtx(() => [
                      (openBlock(), createBlock(resolveDynamicComponent(unref(a).tabPane.name), normalizeProps({
                        key: "all",
                        [unref(a).tabPane.key || ""]: "all",
                        [unref(a).tabPane.tab || ""]: "全部"
                      }), null, 16)),
                      (openBlock(true), createElementBlock(Fragment, null, renderList(e.iconSets, (me) => (openBlock(), createBlock(resolveDynamicComponent(unref(a).tabPane.name), normalizeProps({
                        key: me,
                        [unref(a).tabPane.key || ""]: me,
                        [unref(a).tabPane.tab || ""]: me
                      }), null, 16))), 128))
                    ]),
                    _: 1
                  }, 16)),
                  (openBlock(), createBlock(resolveDynamicComponent(unref(a).input.name), normalizeProps({
                    [unref(a).input.modelValue]: w.value,
                    ["onUpdate:" + unref(a).input.modelValue]: M[0] || (M[0] = (me) => w.value = me),
                    class: "ml-2",
                    placeholder: "搜索图标, 双击选择",
                    onKeydown: withKeys(A, ["enter"])
                  }), {
                    suffix: withCtx(() => [
                      createVNode(le, {
                        type: "primary",
                        size: "small",
                        icon: unref(a).icons.search,
                        onClick: A
                      }, null, 8, ["icon"])
                    ]),
                    _: 1
                  }, 16)),
                  createBaseVNode("div", em, [
                    createBaseVNode("div", tm, [
                      (openBlock(true), createElementBlock(Fragment, null, renderList(k.value.records, (me) => (openBlock(), createElementBlock("div", {
                        key: me,
                        class: normalizeClass(["icon-item", { active: me === B2.value }]),
                        title: me,
                        onClick: (Se) => D(me, false),
                        onDblclick: (Se) => D(me, true)
                      }, [
                        createVNode(X, {
                          icon: me,
                          class: "text-2xl"
                        }, null, 8, ["icon"])
                      ], 42, nm))), 128))
                    ]),
                    createBaseVNode("div", om, [
                      k.value.loading ? (openBlock(), createBlock(ye, {
                        key: 0,
                        loading: k.value.loading,
                        text: "加载中"
                      }, null, 8, ["loading"])) : k.value.total == null || k.value.total == 0 ? (openBlock(), createElementBlock("div", rm, M[2] || (M[2] = [
                        createBaseVNode("div", null, "暂无数据", -1)
                      ]))) : k.value.total > k.value.start + k.value.limit ? (openBlock(), createElementBlock("div", {
                        key: 2,
                        onClick: I
                      }, M[3] || (M[3] = [
                        createBaseVNode("div", null, "加载更多", -1)
                      ]))) : createCommentVNode("", true)
                    ])
                  ]),
                  createBaseVNode("div", am, [
                    createVNode(le, {
                      type: "primary",
                      onClick: x
                    }, {
                      default: withCtx(() => M[4] || (M[4] = [
                        createTextVNode("确定")
                      ])),
                      _: 1
                    })
                  ])
                ])
              ])
            ]),
            _: 2
          }, 1040))
        ])) : createCommentVNode("", true)
      ]);
    };
  }
});
var lm = defineComponent({
  name: "FsDictRadio",
  props: {
    /**
     * 数据字典配置
     */
    dict: {},
    /**
     * 可选项，比dict.data优先级高
     */
    options: { type: Array },
    /**
     * radio组件名称
     * antdv使用button样式的时候有用
     * 即将废弃,请使用optionName
     */
    radioName: {},
    /**
     * 选项的组件名称
     */
    optionName: {
      type: String
    },
    /**
     * 选项的属性
     */
    optionProps: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 转换DictData
     */
    transformDictData: {
      type: Function,
      default: void 0
    }
  },
  emits: [
    /**
     * 字典数据变化事件
     */
    "dict-change",
    /**
     * 选中值变化事件，可以获取到当前选中的option对象
     */
    "selected-change",
    /**
     * 值变化事件
     */
    "change"
  ],
  setup(e, t) {
    const { ui: n } = B();
    e.radioName && console.warn("参数radioName即将废弃，请改成optionName");
    const o = computed(() => e.optionName ?? e.radioName ?? n.radio.name);
    let r = on(e, t, n.radioGroup.modelValue);
    const a = r.createComputedOptions();
    return {
      ui: n,
      computedRadioName: o,
      ...r,
      computedOptions: a,
      onSelectedChange: (s) => {
        t.emit("change", s), s && s.target && (s = s.target.value);
        const u = r.getDict();
        if (u && u.dataMap && u.dataMap[s]) {
          const l = u.dataMap[s];
          t.emit("selected-change", l);
        } else
          t.emit("selected-change", null);
      }
    };
  }
});
function um(e, t, n, o, r, a) {
  return openBlock(), createBlock(resolveDynamicComponent(e.ui.radioGroup.name), { onChange: e.onSelectedChange }, {
    default: withCtx(() => [
      (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedOptions, (i2) => (openBlock(), createBlock(resolveDynamicComponent(e.computedRadioName), mergeProps({
        ref_for: true,
        ref: "radioRef",
        key: e.getValue(i2),
        [e.ui.radio.value || ""]: e.getValue(i2)
      }, e.optionProps), {
        default: withCtx(() => [
          createTextVNode(toDisplayString(e.getLabel(i2)), 1)
        ]),
        _: 2
      }, 1040))), 128))
    ]),
    _: 1
  }, 40, ["onChange"]);
}
var cm = ke(lm, [["render", um]]);
var dm = defineComponent({
  name: "FsDictSelect",
  props: {
    /**
     * 字典
     */
    dict: {},
    /**
     * 可选项，比dict.data优先级高
     */
    options: {
      type: Array
    },
    /**
     * placeholder
     */
    placeholder: {
      type: String
    },
    /**
     * select组件的插槽
     */
    slots: {},
    /**
     * 自定义label的render方法
     */
    renderLabel: {
      type: Function
    },
    /**
     * 转换DictData
     */
    transformDictData: {
      type: Function,
      default: void 0
    }
  },
  emits: [
    /**
     * 字典项变化
     */
    "dict-change",
    /**
     * 选中值变化事件，可以获取到当前选中的option对象
     */
    "selected-change",
    /**
     * 值变化事件
     */
    "change"
  ],
  setup(e, t) {
    const {
      t: n
    } = ot(), o = computed(() => e.placeholder || n("fs.component.select.placeholder")), {
      ui: r
    } = B(), a = on(e, t, r.select.modelValue), i2 = a.createComputedOptions();
    return {
      computedPlaceholder: o,
      ...a,
      computedOptions: i2,
      onSelectedChange: (u) => {
        t.emit("change", u);
        const l = a.getDict();
        if (u && Array.isArray(u) && u.length > 0) {
          const c = [];
          for (const d of u)
            l && l.dataMap && l.dataMap[d] && c.push(l.dataMap[d]);
          t.emit("selected-change", c);
          return;
        }
        if (l && l.dataMap && l.dataMap[u]) {
          const c = l.dataMap[u];
          t.emit("selected-change", c);
        } else
          t.emit("selected-change", null);
      }
    };
  },
  render() {
    const {
      ui: e
    } = B(), t = resolveDynamicComponent(e.select.name), n = e.select.modelValue;
    if (e.option.name == null) {
      const i2 = this.computedOptions || [], s = {
        [`onUpdate:${n}`]: (u) => {
          this.$emit(`onUpdate:${n}`, u), this.onSelectedChange(u);
        }
      };
      return createVNode(t, mergeProps({
        ref: "selectRef",
        placeholder: this.computedPlaceholder,
        options: i2,
        renderLabel: this.renderLabel
      }, s), null);
    }
    const o = [], r = resolveDynamicComponent(e.option.name), a = this.computedOptions || [];
    for (const i2 of a) {
      const s = createVNode(r, mergeProps(i2, {
        value: this.getValue(i2),
        label: this.getLabel(i2)
      }), {
        default: () => [this.renderLabel ? this.renderLabel(i2) : this.getLabel(i2)]
      });
      o.push(s);
    }
    return createVNode(t, {
      ref: "selectRef",
      placeholder: this.computedPlaceholder,
      onChange: this.onSelectedChange
    }, {
      default: () => [o],
      ...this.slots
    });
  }
});
function fm(e) {
  if (e == null)
    return 0;
  typeof e != "string" && (e = JSON.stringify(e));
  let t = 0, n, o, r;
  if (e.length === 0)
    return t;
  for (n = 0, r = e.length; n < r; n++)
    o = e.charCodeAt(n), t = (t << 5) - t + o, t |= 0;
  return t;
}
function hm(e) {
  let t = [];
  return typeof e.modelValue == "string" && e.multiple && e.separator != null && e.separator !== "" ? t = e.modelValue.split(e.separator) : e.modelValue instanceof Array ? t = e.modelValue : t = [e.modelValue], t;
}
var mm = defineComponent({
  name: "FsValuesFormat",
  props: {
    /**
     * 值
     */
    modelValue: {},
    /**
     * 字典配置
     */
    dict: {},
    /**
     * 是否多选
     */
    multiple: { default: true },
    /**
     * 分隔符<br/>
     * 多选时，如果value为string，则以该分隔符分割成多个展示<br/>
     * 传入空字符串，表示不分割<br/>
     */
    separator: { default: "," },
    /**
     * 颜色
     * element=【auto, primary, success, warning, danger ,info】
     * antdv=【auto, primary, success, blue,red,...】
     * 配置auto，则自动根据value值hashcode分配颜色值
     */
    color: {},
    /**
     * 效果（仅element）
     **/
    effect: {},
    /**
     * 自动染色颜色值列表
     */
    autoColors: {
      type: Array
    },
    /**
     * 自动主题列表（仅element）
     * 【 light, plain 】
     */
    autoEffects: {
      type: Array
    },
    /**
     * 显示类型：【text, tag】
     */
    type: {
      default: "tag"
    },
    /**
     * 值的类型，【 value | object】
     */
    valueType: {
      type: String
    },
    /**
     * 当value值不在字典中时默认显示的文本
     */
    defaultLabel: {},
    /**
     * label自定义render
     */
    labelFormatter: {
      type: Function
    },
    /**
     * 自定义选项render
     */
    itemRender: {
      type: Function
    },
    closable: {
      type: Boolean,
      default: false
    }
  },
  emits: ["click", "dict-change", "close", "update:modelValue"],
  setup(e, t) {
    const { ui: n } = B(), o = n.tag.colors, r = ["plain", "light"], a = on(e, t), { getColor: i2, getValue: s, removePropValue: u } = a;
    a.watchValue();
    function l(h2, v) {
      !v.effect && h2.effect && (v.effect = h2.effect);
      const g = n.tag.type, w = i2(v);
      if (w != null) {
        if (typeof w != "string")
          return;
        u(v, "color"), v[g] = w;
        return;
      }
      if (h2.color === "auto") {
        const y = fm(s(v)), R = h2.autoColors ? h2.autoColors : o;
        v[g] = R[y % R.length];
        const F = h2.autoEffects ? h2.autoEffects : r;
        v.effect = F[Math.floor(y / R.length) % F.length];
      } else
        v[g] = h2.color;
    }
    const c = computed(() => {
      var F, k;
      if (e.valueType === "object")
        return e.modelValue && !Array.isArray(e.modelValue) ? [e.modelValue] : e.modelValue;
      const h2 = a.getDict();
      if (e.modelValue == null || e.modelValue === "")
        return [];
      const v = hm(e);
      let g = [];
      const w = ((F = e.dict) == null ? void 0 : F.value) || "value", y = ((k = e.dict) == null ? void 0 : k.label) || "label";
      h2 ? (g = h2.getNodesFromDataMap(v), forEach_default(g, (A) => {
        A[y] == null && (A[y] = e.defaultLabel || A[w]);
      })) : (g = [], forEach_default(v, (A) => {
        A instanceof Object ? g.push(A) : g.push({
          [w]: A,
          [y]: A
        });
      }));
      const R = [];
      return forEach_default(g, (A) => {
        R.push(omit_default(A, "children"));
      }), forEach_default(R, (A) => {
        l(e, A);
      }), R;
    });
    function d(h2) {
      t.emit("click", { item: h2 });
    }
    function f(h2, v) {
      t.emit("close", { item: v, index: h2 });
      const g = [];
      for (let w = 0; w < c.value.length; w++)
        w !== h2 && g.push(s(c.value[w]));
      t.emit("update:modelValue", g);
    }
    return {
      ui: n,
      ...a,
      doClick: d,
      computedValueItems: c,
      doClose: f
    };
  }
});
var pm = { class: "fs-values-format" };
var gm = ["onClick"];
function vm(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-render"), s = resolveComponent("fs-icon");
  return openBlock(), createElementBlock("span", pm, [
    e.itemRender ? (openBlock(true), createElementBlock(Fragment, { key: 0 }, renderList(e.computedValueItems, (u) => (openBlock(), createBlock(i2, {
      key: e.getValue(u),
      "render-func": e.itemRender,
      scope: u
    }, null, 8, ["render-func", "scope"]))), 128)) : e.type === "text" ? (openBlock(true), createElementBlock(Fragment, { key: 1 }, renderList(e.computedValueItems, (u) => (openBlock(), createElementBlock("span", {
      key: e.getValue(u),
      onClick: (l) => e.doClick(u)
    }, toDisplayString(e.getLabel(u)), 9, gm))), 128)) : (openBlock(true), createElementBlock(Fragment, { key: 2 }, renderList(e.computedValueItems, (u, l) => (openBlock(), createBlock(resolveDynamicComponent(e.ui.tag.name), mergeProps({
      key: e.getValue(u),
      class: "fs-tag",
      size: "small",
      closable: e.closable,
      ref_for: true
    }, u, {
      icon: null,
      name: null,
      onClose: (c) => e.doClose(l, u),
      onClick: (c) => e.doClick(u)
    }), {
      default: withCtx(() => [
        u.icon ? (openBlock(), createBlock(s, {
          key: 0,
          icon: u.icon,
          spin: u.iconSpin,
          class: "fs-tag-icon"
        }, null, 8, ["icon", "spin"])) : createCommentVNode("", true),
        createTextVNode(" " + toDisplayString(e.getLabel(u)), 1)
      ]),
      _: 2
    }, 1040, ["closable", "onClose", "onClick"]))), 128))
  ]);
}
var bm = ke(mm, [["render", vm]]);
var ym = defineComponent({
  name: "FsDictCascaderFormat",
  props: {
    /**
     *   值<br/>
     *   单选时 '1,2,3' 或 [1,2,3]<br/>
     *   多选[[1,2,3],[4,5,6]]<br/>
     */
    // @ts-ignore
    modelValue: {
      type: [String, Array],
      default: void 0,
      require: true
    },
    /**
     *  value的分隔符<br/>
     *  多选时，如果value为string，则以该分隔符分割成多个展示<br/>
     *  传入空字符串，表示不分割<br/>
     */
    // @ts-ignore
    separator: { type: String, default: ",", require: false },
    /**
     * 是否多选
     */
    // @ts-ignore
    multiple: { type: Boolean, default: false },
    /**
     * 数据字典
     */
    dict: {
      type: Object,
      default: void 0,
      require: false
    }
  },
  emits: ["dict-change"],
  setup(e, t) {
    var r;
    const n = on(e, t);
    (r = e.dict) != null && r.getNodesByValues && n.watchValue();
    const o = computed(() => e.multiple);
    return {
      ...n,
      computedMultiple: o
    };
  },
  data() {
    return {};
  },
  computed: {
    labels() {
      return this.modelValue == null ? [] : this.buildValueItem(this.modelValue);
    },
    multipleLabels() {
      if (this.modelValue == null)
        return [];
      const e = [];
      for (const t of this.modelValue)
        e.push(this.buildValueItem(t));
      return e;
    }
  },
  methods: {
    getValueArr(e) {
      if (e == null)
        if (this.multiple) {
          e = [];
          for (const n of this.modelValue)
            for (const o of n)
              e.push(o);
        } else
          e = this.modelValue;
      if (e == null)
        return [];
      let t = null;
      return typeof e == "string" && !this.multiple && this.separator != null && this.separator !== "" ? t = e.split(this.separator) : e instanceof Array ? t = e : t = [e], t;
    },
    buildValueItem(e) {
      const t = this.getValueArr(e), n = this.getDict();
      if (n)
        return n.getNodesFromDataMap(t);
    }
  }
});
var wm = { key: 0 };
var _m = { key: 0 };
function Cm(e, t, n, o, r, a) {
  return openBlock(), createElementBlock("span", null, [
    e.computedMultiple ? (openBlock(true), createElementBlock(Fragment, { key: 0 }, renderList(e.multipleLabels, (i2, s) => (openBlock(), createElementBlock("div", { key: s }, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(i2, (u, l) => (openBlock(), createElementBlock("span", { key: l }, [
        l !== 0 ? (openBlock(), createElementBlock("span", wm, " / ")) : createCommentVNode("", true),
        createBaseVNode("span", null, toDisplayString(e.getLabel(u)), 1)
      ]))), 128))
    ]))), 128)) : (openBlock(true), createElementBlock(Fragment, { key: 1 }, renderList(e.labels, (i2, s) => (openBlock(), createElementBlock("span", { key: s }, [
      s !== 0 ? (openBlock(), createElementBlock("span", _m, " / ")) : createCommentVNode("", true),
      createBaseVNode("span", null, toDisplayString(e.getLabel(i2)), 1)
    ]))), 128))
  ]);
}
var Sm = ke(ym, [["render", Cm]]);
var Fm = defineComponent({
  name: "FsDictCascader",
  props: {
    /**
     * 字典配置
     */
    dict: {},
    /**
     * 选项，比dict.data优先级高
     */
    options: { type: Array },
    /**
     * 转换DictData
     */
    transformDictData: {
      type: Function,
      default: void 0
    }
  },
  emits: [
    /**
     * 字典数据变化事件
     */
    "dict-change",
    /**
     * 选中值变化事件，可以获取到当前选中的option对象
     */
    "selected-change",
    /**
     * 值变化事件
     */
    "change"
  ],
  setup(e, t) {
    const n = on(e, t), { ui: o } = B(), r = ref();
    e.dict && (r.value = o.cascader.fieldNames({
      // @ts-ignore
      value: e.dict.value,
      // @ts-ignore
      label: e.dict.label,
      // @ts-ignore
      children: e.dict.children
    }));
    const a = n.createComputedOptions();
    function i2(s) {
      if (t.emit("change", s), s) {
        let u = [];
        const l = n.getDict();
        if (l && l.dataMap) {
          for (let c of s) {
            const d = l.dataMap[c];
            d && u.push(d);
          }
          t.emit("selected-change", u);
        }
      } else
        t.emit("selected-change", null);
    }
    return {
      ui: o,
      ...n,
      fieldNamesBinder: r,
      computedOptions: a,
      onSelectChange: i2
    };
  }
});
function Rm(e, t, n, o, r, a) {
  return openBlock(), createBlock(resolveDynamicComponent(e.ui.cascader.name), mergeProps({
    ref: "cascaderRef",
    options: e.computedOptions
  }, e.fieldNamesBinder, { onChange: e.onSelectChange }), null, 16, ["options", "onChange"]);
}
var Dm = ke(Fm, [["render", Rm]]);
var km = defineComponent({
  name: "FsDictCheckbox",
  props: {
    /**
     * 字典
     */
    dict: {},
    /**
     * 选项，比dict.data优先级高
     */
    options: { type: Array, default: void 0, require: false },
    /**
     * 选项的组件名称
     */
    optionName: {
      type: String
    },
    /**
     * 选项的属性
     */
    optionProps: {
      type: Object,
      default() {
        return {};
      }
    },
    /**
     * 转换DictData
     */
    transformDictData: {
      type: Function,
      default: void 0
    }
  },
  emits: [
    /**
     * 字典数据变化事件
     */
    "dict-change",
    /**
     * 选中值变化事件，可以获取到当前选中的option对象
     */
    "selected-change",
    /**
     * 值变化事件
     */
    "change"
  ],
  setup(e, t) {
    const { ui: n } = B();
    let o = on(e, t, n.checkboxGroup.modelValue);
    const r = o.createComputedOptions();
    return {
      ui: n,
      ...o,
      computedOptions: r,
      onSelectedChange: (i2) => {
        if (t.emit("change", i2), i2) {
          let s = [];
          const u = o.getDict();
          if (u && u.dataMap) {
            for (let l of i2) {
              const c = u.dataMap[l];
              c && s.push(c);
            }
            t.emit("selected-change", s);
          }
        } else
          t.emit("selected-change", null);
      }
    };
  }
});
function $m(e, t, n, o, r, a) {
  return openBlock(), createBlock(resolveDynamicComponent(e.ui.checkboxGroup.name), { onChange: e.onSelectedChange }, {
    default: withCtx(() => [
      (openBlock(true), createElementBlock(Fragment, null, renderList(e.computedOptions, (i2) => (openBlock(), createBlock(resolveDynamicComponent(e.optionName || e.ui.checkbox.name), mergeProps({
        ref_for: true,
        ref: "checkboxRef",
        key: e.getValue(i2),
        [e.ui.checkbox.value || ""]: e.getValue(i2)
      }, e.optionProps), {
        default: withCtx(() => [
          createTextVNode(toDisplayString(e.getLabel(i2)), 1)
        ]),
        _: 2
      }, 1040))), 128))
    ]),
    _: 1
  }, 40, ["onChange"]);
}
var Om = ke(km, [["render", $m]]);
var Em = defineComponent({
  name: "FsDictSwitch",
  props: {
    /**
     * 字典第一个为开启
     * 第二个为关闭
     */
    dict: {
      type: Object
    },
    options: {},
    /**
     * 转换DictData
     */
    transformDictData: {
      type: Function,
      default: void 0
    }
  },
  emits: ["dict-change"],
  setup(e, t) {
    const { ui: n } = B();
    let o = on(e, t, n.switch.modelValue);
    const r = o.createComputedOptions();
    return {
      ui: n,
      ...o,
      computedOptions: r
    };
  },
  computed: {
    _active() {
      return this.computedOptions.length > 0 ? this.computedOptions[0] : {};
    },
    _inActive() {
      return this.computedOptions.length > 1 ? this.computedOptions[1] : {};
    },
    binding() {
      var t, n, o, r, a, i2;
      const e = i.get();
      return {
        // @ts-ignore
        [e.switch.activeText]: this._active[((t = this.dict) == null ? void 0 : t.label) || "label"],
        // @ts-ignore
        [e.switch.inactiveText]: this._inActive[((n = this.dict) == null ? void 0 : n.label) || "label"],
        // @ts-ignore
        [e.switch.activeColor]: this._active[((o = this.dict) == null ? void 0 : o.color) || "color"],
        // @ts-ignore
        [e.switch.inactiveColor]: this._inActive[((r = this.dict) == null ? void 0 : r.color) || "color"],
        // @ts-ignore
        [e.switch.activeValue]: this._active[((a = this.dict) == null ? void 0 : a.value) || "value"],
        // @ts-ignore
        [e.switch.inactiveValue]: this._inActive[((i2 = this.dict) == null ? void 0 : i2.value) || "value"]
      };
    }
  }
});
function Tm(e, t, n, o, r, a) {
  return openBlock(), createBlock(resolveDynamicComponent(e.ui.switch.name), mergeProps({ ref: "switchRef" }, e.binding), null, 16);
}
var Am = ke(Em, [["render", Tm]]);
var Im = defineComponent({
  name: "FsDateFormat",
  props: {
    /**
     * 日期时间值，支持long,string,date等，由dayjs转化
     */
    modelValue: { required: false },
    /**
     *  输入格式化，不传则由dayjs自动转化
     */
    valueFormat: { type: String, default: void 0, required: false },
    /**
     *  输出格式化
     */
    format: { type: String, default: "YYYY-MM-DD HH:mm:ss", required: false }
  },
  data() {
    return {
      item: {}
    };
  },
  computed: {
    doFormat() {
      if (this.modelValue == null || this.modelValue === "")
        return "";
      let e = null;
      return this.valueFormat != null ? e = (0, import_dayjs.default)(this.modelValue, this.valueFormat) : e = (0, import_dayjs.default)(this.modelValue), e.format(this.format);
    }
  },
  created() {
  },
  methods: {}
});
function Vm(e, t, n, o, r, a) {
  return openBlock(), createElementBlock("span", null, toDisplayString(e.doFormat), 1);
}
var Pm = ke(Im, [["render", Vm]]);
var Mm = defineComponent({
  name: "FsDictTree",
  components: {},
  props: {
    /**
     * 数据字典
     */
    dict: {},
    /**
     * 可选项，比dict.data优先级高
     */
    options: { type: Array },
    /**
     * placeholder
     */
    placeholder: { type: String },
    /**
     * 转换DictData
     */
    transformDictData: {
      type: Function,
      default: void 0
    }
  },
  emits: [
    /**
     * 字典数据变化事件
     */
    "dict-change",
    /**
     * 选中值变化事件，可以获取到当前选中的option对象
     */
    "selected-change",
    /**
     * 值变化事件
     */
    "change"
  ],
  // render () {
  //   return this.renderFunc({ data: this.data, dataMap: this.dataMap, scope: this.scope, attrs: this.$attrs })
  // },
  setup(e, t) {
    const { t: n } = ot(), { ui: o } = B(), r = computed(() => e.placeholder || n("fs.component.select.placeholder"));
    let a = on(e, t);
    const i2 = t.slots, s = a.createComputedOptions(), u = computed(() => {
      const d = a.getDict();
      return o.treeSelect.buildOptionKeysNameBinding({
        label: d.label,
        value: d.value,
        children: d.children
      });
    }), l = (d) => {
      if (t.emit("change", d), d) {
        const f = a.getDict();
        if (f && f.dataMap)
          if (d instanceof Array) {
            let h2 = [];
            for (let v of d) {
              const g = f.dataMap[v];
              g && h2.push(g);
            }
            t.emit("selected-change", h2);
          } else
            t.emit("selected-change", f.dataMap[d]);
      } else
        t.emit("selected-change", null);
    }, c = ref();
    return {
      ui: o,
      computedBinding: u,
      computedPlaceholder: r,
      ...a,
      computedOptions: s,
      onSelectedChange: l,
      slots: i2,
      treeRef: c
    };
  }
});
function Bm(e, t, n, o, r, a) {
  const i2 = resolveComponent("fs-slot-render");
  return openBlock(), createBlock(resolveDynamicComponent(e.ui.treeSelect.name), mergeProps({
    ref: "treeRef",
    [e.ui.treeSelect.options || ""]: e.computedOptions,
    placeholder: e.computedPlaceholder
  }, e.computedBinding, { onChange: e.onSelectedChange }), createSlots({ _: 2 }, [
    renderList(e.slots, (s, u) => ({
      name: u,
      fn: withCtx((l) => [
        createVNode(i2, {
          slots: s,
          scope: l
        }, null, 8, ["slots", "scope"])
      ])
    }))
  ]), 1040, ["placeholder", "onChange"]);
}
var jm = ke(Mm, [["render", Bm]]);
var Nm = {
  class: "fs-table-select"
};
var Lm = {
  key: 0,
  class: "fs-table-select-current"
};
var qm = defineComponent({
  __name: "fs-table-select",
  props: {
    modelValue: {},
    createCrudOptions: {},
    crudOptionsOverride: {
      default: void 0
    },
    beforeOpen: {},
    dict: {},
    select: {
      default: void 0
    },
    showSelect: {
      type: Boolean,
      default: true
    },
    dialog: {
      default: void 0
    },
    showCurrent: {
      type: Boolean,
      default: true
    },
    valuesFormat: {
      default: void 0
    },
    height: {
      default: void 0
    },
    multiple: {
      type: Boolean
    },
    crossPage: {
      type: Boolean,
      default: true
    },
    rowKey: {
      default: void 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    valueType: {
      default: "value"
    },
    viewMode: {
      type: Boolean,
      default: false
    },
    emitOnViewModel: {
      type: Boolean,
      default: true
    },
    destroyOnClose: {
      type: Boolean,
      default: true
    }
  },
  emits: [
    "change",
    "update:modelValue",
    /* 选中行变化事件 */
    "selected-change",
    /*对话框即将关闭*/
    "dialog-close",
    /*对话框已关闭*/
    "dialog-closed"
  ],
  setup(e, {
    expose: t,
    emit: n
  }) {
    const o = e, r = useSlots(), a = n, {
      ui: i2
    } = B(), {
      t: s
    } = ot(), u = ref(), l = ref(), c = ref(false), {
      crudRef: d,
      crudBinding: f,
      crudExpose: h2
    } = hu();
    function v(I) {
      I == null || Array.isArray(I) && I.length == 0 ? F.value = [] : (o.multiple ? F.value = I || [] : F.value = [I], o.valueType === "object" && (F.value = F.value.map((V) => o.dict.getValue(V))));
    }
    const g = async (I) => {
      var O, M;
      if (o.disabled || o.readonly || (O = o.select) != null && O.disabled || (M = o.select) != null && M.readonly)
        return;
      if (o.dict == null)
        throw new Error("必须配置dict，且必须配置dict.getNodesByValues");
      const V = await fu({
        crudBinding: f,
        crudRef: d,
        createCrudOptions: o.createCrudOptions,
        crudOptionsOverride: N(),
        context: I.context,
        crudExpose: h2
      });
      return v(o.modelValue), o.beforeOpen && await o.beforeOpen({
        crudOptions: I.crudOptions,
        ...D()
      }), I && V.appendCrudOptions(I.crudOptions), c.value = true, await h2.doRefresh(), V;
    }, w = computed(() => ({
      ...o.valuesFormat
    })), y = computed(() => {
      const I = `onUpdate:${i2.select.modelValue}`;
      let V = o.modelValue;
      return o.valueType === "object" && o.modelValue && (o.multiple ? V = o.modelValue.map((O) => o.dict.getValue(O)) : V = o.dict.getValue(o.modelValue)), {
        [i2.select.modelValue]: V,
        [I]: (O) => {
          a("update:modelValue", O);
        },
        [i2.select.clearable]: true,
        ...i2.select.buildMultiBinding(o.multiple),
        show: false,
        ...o.select
      };
    }), R = computed(() => {
      const I = i2.dialog.buildProps({
        title: o.viewMode ? s("fs.extends.tableSelect.view") : s("fs.extends.tableSelect.select"),
        width: "80%"
      });
      return $(I, o.dialog);
    });
    watch(() => o.modelValue, async (I) => {
      I !== F.value && (v(I), await nextTick(), await o.dict.appendByValues(F.value));
    });
    const F = ref([]);
    function k() {
      return o.rowKey || f.value.table.rowKey || "id";
    }
    const A = ref(false);
    function N() {
      var M, X;
      let I = i2.table;
      ((X = (M = f.value) == null ? void 0 : M.table) == null ? void 0 : X.tableVersion) === "v2" && (I = i2.tableV2);
      let V = I.buildSelectionCrudOptions({
        crossPage: o.crossPage,
        selectOnClickRow: true,
        getRowKey: k,
        getPageData() {
          return f.value.data;
        },
        useCompute: Nt,
        multiple: o.multiple,
        selectedRowKeys: F,
        onSelectedKeysChanged: async (le) => {
          F.value = [...le], await nextTick(), await o.dict.appendByValues(F.value);
        }
      });
      return $({
        table: {
          async onRefreshed() {
            if (I.setSelectedRows) {
              A.value = true, await nextTick(), await nextTick();
              const le = h2.getBaseTableRef();
              I.setSelectedRows({
                getRowKey: k,
                multiple: o.multiple,
                tableRef: le,
                selectedRowKeys: F
              }), A.value = false;
            }
          }
        }
      }, V, o.crudOptionsOverride);
    }
    const {
      merge: $
    } = De();
    async function B2() {
      var M;
      if (o.dict.loading)
        return;
      let I = null, V = null;
      ((M = F.value) == null ? void 0 : M.length) > 0 && (I = [...F.value], V = I.map((X) => o.dict.getDictMap()[X]), o.valueType === "object" && (I = V), o.multiple !== true && I.length > 0 && (I = I[0])), (!o.viewMode || o.emitOnViewModel) && (a("update:modelValue", I), a("change", I), a("selected-change", V)), c.value = false;
      let O = {
        value: I,
        rows: V,
        selectedRowKeys: F.value
      };
      a("dialog-close", O), await nextTick(), a("dialog-closed", O);
    }
    const D = () => ({
      opened: c,
      open: g,
      selectedRowKeys: F,
      dictSelectRef: u,
      valuesFormatRef: l,
      crudRef: d,
      crudBinding: f,
      crudExpose: h2
    }), x = ref(D());
    return t(x.value), (I, V) => {
      const O = resolveComponent("fs-dict-select"), M = resolveComponent("fs-values-format"), X = resolveComponent("fs-crud");
      return openBlock(), createElementBlock("div", Nm, [!(r != null && r.default) && !I.viewMode ? (openBlock(), createBlock(O, mergeProps({
        key: 0,
        ref_key: "dictSelectRef",
        ref: u
      }, y.value, {
        open: false,
        disabled: I.disabled,
        readonly: I.readonly,
        dict: I.dict,
        onClick: g
      }), null, 16, ["disabled", "readonly", "dict"])) : createCommentVNode("", true), renderSlot(I.$slots, "default", normalizeProps(guardReactiveProps(x.value))), (openBlock(), createBlock(resolveDynamicComponent(unref(i2).formItem.skipValidationWrapper), null, {
        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(unref(i2).dialog.name), mergeProps({
          [unref(i2).dialog.visible]: c.value,
          ["onUpdate:" + unref(i2).dialog.visible]: V[2] || (V[2] = (le) => c.value = le)
        }, R.value), {
          [unref(i2).dialog.footerSlotName]: withCtx(() => [I.viewMode ? createCommentVNode("", true) : (openBlock(), createBlock(resolveDynamicComponent(unref(i2).button.name), {
            key: 0,
            onClick: V[1] || (V[1] = (le) => c.value = false)
          }, {
            default: withCtx(() => V[4] || (V[4] = [createTextVNode("取消")])),
            _: 1
          })), (openBlock(), createBlock(resolveDynamicComponent(unref(i2).button.name), {
            type: "primary",
            onClick: B2
          }, {
            default: withCtx(() => V[5] || (V[5] = [createTextVNode("确认")])),
            _: 1
          }))]),
          default: withCtx(() => [c.value || I.destroyOnClose === false ? (openBlock(), createElementBlock("div", {
            key: 0,
            style: normalizeStyle({
              width: "100%",
              height: I.height || "60vh"
            })
          }, [createVNode(X, mergeProps({
            ref_key: "crudRef",
            ref: d
          }, unref(f)), {
            "header-top": withCtx(() => [I.showCurrent !== false && !I.viewMode ? (openBlock(), createElementBlock("div", Lm, [V[3] || (V[3] = createTextVNode(" 当前选中： ")), createVNode(M, mergeProps({
              ref_key: "valuesFormatRef",
              ref: l,
              modelValue: F.value,
              "onUpdate:modelValue": V[0] || (V[0] = (le) => F.value = le),
              dict: I.dict,
              closable: true
            }, w.value), null, 16, ["modelValue", "dict"])])) : createCommentVNode("", true)]),
            _: 1
          }, 16)], 4)) : createCommentVNode("", true)]),
          _: 2
        }, 1040))]),
        _: 1
      }))]);
    };
  }
});
var Da = Object.freeze(Object.defineProperty({
  __proto__: null,
  FsActionbar: rf,
  FsBox: pc,
  FsButton: Yu,
  FsCell: Wd,
  FsColumnsFilterLayoutDefault: _h,
  FsComponentRender: bc,
  FsContainer: Eu,
  FsCrud: nd,
  FsDateFormat: Pm,
  FsDictCascader: Dm,
  FsDictCascaderFormat: Sm,
  FsDictCheckbox: Om,
  FsDictRadio: cm,
  FsDictSelect: dm,
  FsDictSwitch: Am,
  FsDictTree: jm,
  FsEditable: ef,
  FsEditableCell: Ud,
  FsForm: Cc,
  FsFormHelper: jc,
  FsFormItem: Ac,
  FsFormProvider: qc,
  FsFormWrapper: to,
  FsIcon: Gu,
  FsIconSelector: sm,
  FsIconSvg: oc,
  FsIconify: Ju,
  FsLabel: sc,
  FsLayoutCard: Uu,
  FsLayoutDefault: ju,
  FsLoading: dc,
  FsPage: Vl,
  FsRender: di,
  FsRowHandle: sd,
  FsSearch: Oh,
  FsSearchLayoutDefault: Uh,
  FsSearchV1: jh,
  FsSlotRender: yc,
  FsTable: zd,
  FsTableColumnsFixedController: Pi,
  FsTableSelect: qm,
  FsTabsFilter: Gh,
  FsToolbar: cf,
  FsValuesFormat: bm,
  fsColumnsFilterNestList: Mi
}, Symbol.toStringTag, { value: "Module" }));
var { setDictRequest: xm } = bu();
var ip = {
  install(e, t = {}) {
    t.ui && i.set(t.ui);
    const { merge: n } = De();
    t.commonOptions && (Zn.commonOptions = t.commonOptions), t.dictRequest && xm(t.dictRequest), t.i18n && Co.setVueI18n(t.i18n);
    const o = t.customComponents || {};
    for (const r in Da) {
      const a = o[r] || Da[r];
      e.component(r, a);
    }
    to._context = e._context, Rn.install(), e.config.globalProperties.$fsui = i.get(), n(Su.logger, t.logger), Hm(t.logger);
  }
};
function Hm(e) {
  var t;
  ((t = e == null ? void 0 : e.off) == null ? void 0 : t.tableColumns) !== false && console.warn(`[fast-crud] crudBinding.value.table.columns / toolbar.columnsFilter.originalColumns 由array改成map. 请改成通过key读取，你可以全局代码搜索【value.table.columns / columnsFilter.originalColumns】来检查是否有使用它们。
      [通过 app.use(FastCrud,{logger:{off:{tableColumns:false}}}) 可关闭此警告] `);
}

export {
  _s,
  vt,
  ot,
  Ko,
  Yo,
  Vl,
  De,
  Go,
  Hl,
  xr,
  zl,
  Nt,
  Xo,
  Wr,
  br,
  Po,
  Za,
  ou,
  ti,
  iu,
  su,
  uu,
  ep,
  ni,
  oi,
  cu,
  du,
  tp,
  fu,
  hu,
  on,
  ci,
  vu,
  bu,
  rp,
  Cu,
  Su,
  Eu,
  ju,
  Uu,
  Yu,
  Gu,
  Ju,
  oc,
  sc,
  dc,
  pc,
  bc,
  yc,
  di,
  Cc,
  Ac,
  jc,
  to,
  qc,
  nd,
  sd,
  zd,
  Wd,
  Ud,
  ef,
  rf,
  ap,
  Ur,
  cf,
  Pi,
  Mi,
  _h,
  Oh,
  jh,
  Uh,
  Gh,
  sm,
  cm,
  dm,
  bm,
  Sm,
  Dm,
  Om,
  Am,
  Pm,
  jm,
  qm,
  ip
};
/*! Bundled license information:

@fast-crud/fast-crud/dist/index-7517cf48.mjs:
  (**!
   * Sortable 1.14.0
   * <AUTHOR>   <<EMAIL>>
   * <AUTHOR>    <<EMAIL>>
   * @license MIT
   *)
*/
//# sourceMappingURL=chunk-RUCDUSHK.js.map
