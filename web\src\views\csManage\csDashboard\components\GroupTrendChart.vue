<template>
  <div class="group-trend-chart">
    <!-- AI会话趋势图 -->
    <div class="chart-container mb-6">
      <div v-if="loading" class="flex items-center justify-center h-[500px]">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
      
      <div v-else-if="!hasAiData" class="flex items-center justify-center h-[500px] text-gray-500">
        <div class="text-center">
          <i class="fas fa-chart-bar text-4xl mb-2"></i>
          <p>AI会话暂无数据</p>
        </div>
      </div>
      
      <div v-else ref="aiChartRef" class="w-full h-[500px]"></div>
    </div>
    
    <!-- 人工会话趋势图 -->
    <div class="chart-container">
      <div v-if="loading" class="flex items-center justify-center h-[500px]">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
      
      <div v-else-if="!hasManualData" class="flex items-center justify-center h-[500px] text-gray-500">
        <div class="text-center">
          <i class="fas fa-chart-bar text-4xl mb-2"></i>
          <p>人工会话暂无数据</p>
        </div>
      </div>
      
      <div v-else ref="manualChartRef" class="w-full h-[500px]"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed } from 'vue'
import * as echarts from 'echarts'

interface GroupTrendData {
  ai_chart?: {
    dates: string[]
    series: Array<{
      name: string
      data: number[]
      type: string
      stack: string
    }>
    title: string
  }
  manual_chart?: {
    dates: string[]
    series: Array<{
      name: string
      data: number[]
      type: string
      stack: string
    }>
    title: string
  }
}

interface Props {
  data?: GroupTrendData
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const aiChartRef = ref<HTMLDivElement>()
const manualChartRef = ref<HTMLDivElement>()
let aiChart: any = null
let manualChart: any = null

// 计算是否有AI数据
const hasAiData = computed(() => {
  return props.data?.ai_chart?.dates?.length > 0
})

// 计算是否有人工数据
const hasManualData = computed(() => {
  return props.data?.manual_chart?.dates?.length > 0
})

// 客服组颜色配置
const groupColors = [
  '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
  '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1',
  '#14B8A6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'
]

const initCharts = async () => {
  await nextTick()
  
  if (aiChartRef.value && !aiChart) {
    aiChart = echarts.init(aiChartRef.value)
  }
  
  if (manualChartRef.value && !manualChart) {
    manualChart = echarts.init(manualChartRef.value)
  }
  
  updateCharts()
}

const updateCharts = () => {
  if (props.loading) {
    aiChart?.showLoading()
    manualChart?.showLoading()
    return
  }
  
  aiChart?.hideLoading()
  manualChart?.hideLoading()
  
  // 更新AI会话图表
  if (aiChart && props.data?.ai_chart) {
    const aiOption = createChartOption(props.data.ai_chart, 'AI会话量', 'AI会话量趋势')
    aiChart.setOption(aiOption, true)
  }

  // 更新人工会话图表
  if (manualChart && props.data?.manual_chart) {
    const manualOption = createChartOption(props.data.manual_chart, '人工会话量', '人工会话量趋势')
    manualChart.setOption(manualOption, true)
  }
}

const createChartOption = (chartData: any, yAxisName: string, chartTitle: string) => {
  if (!chartData.dates || !chartData.series) {
    return getEmptyOption(yAxisName, chartTitle)
  }

  return {
    title: {
      text: chartTitle,
      left: 'center',
      top: '2%',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        let result = `${params[0].axisValue}<br/>`
        let total = 0

        params.forEach((param: any) => {
          total += param.value
          result += `<span style="color:${param.color}">●</span> ${param.seriesName}: ${param.value}<br/>`
        })

        result += `<strong>总计: ${total}</strong>`
        return result
      }
    },
    legend: {
      data: chartData.series.map((s: any) => s.name),
      top: '12%',
      type: 'scroll'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: chartData.dates,
      axisLabel: {
        rotate: 45,
        interval: 'auto',
        fontSize: 12,
        margin: 15
      }
    },
    yAxis: {
      type: 'value',
      name: yAxisName,
      nameTextStyle: {
        color: '#666'
      }
    },
    series: chartData.series.map((s: any, index: number) => ({
      ...s,
      itemStyle: {
        color: groupColors[index % groupColors.length]
      },
      emphasis: {
        focus: 'series'
      }
    }))
  }
}

const getEmptyOption = (yAxisName: string, chartTitle: string) => {
  return {
    title: {
      text: chartTitle,
      left: 'center',
      top: '2%',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333'
      }
    },
    graphic: {
      type: 'text',
      left: 'center',
      top: 'center',
      style: {
        text: '暂无数据',
        fontSize: 14,
        fill: '#999'
      }
    },
    xAxis: {
      type: 'category',
      data: []
    },
    yAxis: {
      type: 'value',
      name: yAxisName
    }
  }
}

const handleResize = () => {
  aiChart?.resize()
  manualChart?.resize()
}

onMounted(() => {
  initCharts()
  window.addEventListener('resize', handleResize)
})

watch(() => props.data, updateCharts, { deep: true })
watch(() => props.loading, updateCharts)

// 清理
const cleanup = () => {
  window.removeEventListener('resize', handleResize)
  aiChart?.dispose()
  manualChart?.dispose()
  aiChart = null
  manualChart = null
}

// 组件卸载时清理
import { onBeforeUnmount } from 'vue'
onBeforeUnmount(cleanup)
</script>

<style scoped>
.group-trend-chart {
  min-height: 800px;
}

.chart-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}
</style>
