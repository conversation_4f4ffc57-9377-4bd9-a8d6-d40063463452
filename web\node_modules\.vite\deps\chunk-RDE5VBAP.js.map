{"version": 3, "sources": ["../../@fast-crud/fast-extends/src/uploader/components/fs-images-format.vue", "../../@fast-crud/fast-extends/src/uploader/components/fs-images-format.vue", "../../@fast-crud/src/uploader/type/validators.ts", "../../@fast-crud/src/uploader/type/types.tsx", "../../@fast-crud/src/uploader/type/config.ts", "../../@fast-crud/src/uploader/type/index.ts", "../../@fast-crud/src/uploader/components/libs/index.ts", "../../@fast-crud/src/uploader/components/utils/index.ts", "../../@fast-crud/src/uploader/index.ts", "../../@fast-crud/src/editor/type/types.ts", "../../@fast-crud/src/editor/type/config.ts", "../../@fast-crud/src/editor/type/index.ts", "../../@fast-crud/src/editor/components/fs-editor-code/workers.ts", "../../@fast-crud/src/editor/components/fs-editor-code/validators.ts", "../../@fast-crud/src/editor/index.ts", "../../@fast-crud/src/json/type/types.ts", "../../@fast-crud/src/json/type/index.ts", "../../@fast-crud/src/json/index.ts", "../../@fast-crud/src/copyable/type/types.ts", "../../@fast-crud/src/copyable/type/index.ts", "../../node_modules/.pnpm/@soerenmartius+vue3-clipboard@0.1.2/node_modules/@soerenmartius/vue3-clipboard/dist/vue3-clipboard.esm.js", "../../@fast-crud/src/copyable/index.ts", "../../@fast-crud/src/time/type/types.ts", "../../@fast-crud/src/time/type/index.ts", "../../@fast-crud/src/time/index.ts", "../../@fast-crud/src/input/components/fs-phone-input/utils.ts", "../../@fast-crud/fast-extends/src/input/components/fs-phone-input/fs-phone-input.vue", "../../@fast-crud/src/input/components/fs-phone-input/index.ts", "../../@fast-crud/src/input/type/types.tsx", "../../@fast-crud/src/input/type/index.ts", "../../@fast-crud/src/input/index.ts"], "sourcesContent": ["<template>\n  <div class=\"fs-image-format\">\n    <component :is=\"ui.imageGroup.name\" v-bind=\"wrapper\">\n      <component :is=\"ui.image.name\" v-for=\"item in imageListRef\" :key=\"item.src\" class=\"fs-image-item\" v-bind=\"item\">\n        <template #placeholder>\n          <div class=\"fs-image-slot\">\n            <fs-loading :loading=\"true\" v-bind=\"errorBinding\" />\n          </div>\n        </template>\n        <template #error>\n          <div class=\"fs-image-slot\">\n            <img :src=\"error\" v-bind=\"errorBinding\" />\n          </div>\n        </template>\n      </component>\n    </component>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, Ref, ref, watch } from \"vue\";\nimport { useUi } from \"@fast-crud/fast-crud\";\nimport { merge } from \"lodash-es\";\n// 图片行展示组件\nexport default defineComponent({\n  name: \"FsImagesFormat\",\n  inheritAttrs: false,\n  props: {\n    //包裹image的组件配置，antdv是preview-group，element是div\n    wrapper: {\n      type: Object,\n      default: null\n    },\n    // 图片的url\n    // 'value' 或 ['value','value']\n    modelValue: {\n      type: [String, Array, Object],\n      require: true\n    },\n    /**\n     * 构建好的图片链接，如果此处传值，则不走buildUrl方法\n     * 'url' 或 ['url1','url2'] 或 {url,previewUrl} 或 [{url,previewUrl}]\n     */\n    urls: {\n      type: [String, Object, Array]\n    },\n    /**\n     * 加载错误时显示的图片\n     */\n    error: {\n      default:\n        'data:image/svg+xml,%3Csvg xmlns=\"http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"%3E%3Cpath fill=\"%23888\" d=\"M5 21q-.825 0-1.413-.588T3 19v-6.6l3 3l4-4l4 4l4-4l3 3V19q0 .825-.588 1.413T19 21H5ZM5 3h14q.825 0 1.413.588T21 5v6.575l-3-3l-4 4l-4-4l-4 4l-3-3V5q0-.825.588-1.413T5 3Z\"%2F%3E%3C%2Fsvg%3E'\n    },\n    /**\n     * 从value构建图片下载url的方法\n     * 支持异步\n     */\n    buildUrl: {\n      type: Function,\n      default: function (value: any) {\n        return value;\n      }\n    },\n    buildUrls: {\n      type: Function,\n      default: null\n    },\n    /**\n     * 从value或url构建预览大图的方法\n     * 支持异步\n     */\n    buildPreviewUrl: {\n      type: Function,\n      default: function ({ url, value, index }: any) {\n        return url;\n      }\n    },\n\n    buildPreviewUrls: {\n      type: Function,\n      default: null\n    }\n  },\n  setup(props: any, ctx) {\n    const { ui } = useUi();\n    type ImageItem = {\n      value?: any;\n      url?: string;\n      index?: number;\n      previewUrl?: string;\n    };\n    const imageListRef: Ref<any[]> = ref([]);\n\n    const errorBinding = computed(() => {\n      return merge({ style: {} }, { style: ctx.attrs.style });\n    });\n\n    const computedValues = computed(() => {\n      const urls: any = [];\n      if (props.modelValue == null || props.modelValue === \"\") {\n        return urls;\n      }\n      if (typeof props.modelValue === \"string\") {\n        urls.push(props.modelValue);\n      } else if (Array.isArray(props.modelValue)) {\n        for (const item of props.modelValue) {\n          if (item == null) {\n            continue;\n          }\n          if (item.url != null) {\n            urls.push(item.url);\n          } else {\n            urls.push(item);\n          }\n        }\n      } else {\n        //object\n        if (props.modelValue.url != null) {\n          urls.push(props.modelValue.url);\n        } else {\n          urls.push(props.modelValue);\n        }\n      }\n      return urls;\n    });\n\n    function buildImageList(images: ImageItem[]) {\n      const urls: string[] = [];\n      const previewUrls: string[] = [];\n      for (let i = 0; i < images.length; i++) {\n        const image = images[i];\n        urls.push(image.url as string);\n        previewUrls.push(image.previewUrl as string);\n      }\n      const imageList: any[] = [];\n      for (let i = 0; i < images.length; i++) {\n        const image = images[i];\n        const url = image.url;\n        const previewUrl = image.url;\n        const preview = ui.image.buildPreviewBind({\n          url,\n          urls,\n          previewUrl,\n          previewUrls,\n          index: i\n        });\n        imageList.push({\n          fit: \"contain\",\n          src: url,\n          [ui.image.fallback]: props.error,\n          ...ctx.attrs,\n          ...preview\n        });\n      }\n      return imageList;\n    }\n\n    async function buildImageListUrls(list: ImageItem[]) {\n      if (props.buildUrls) {\n        const values = list.map((item) => item.value);\n        const urls = await props.buildUrls(values);\n        for (let i = 0; i < list.length; i++) {\n          list[i].url = urls[i];\n        }\n        let previewUrls = urls;\n        if (props.buildPreviewUrls) {\n          previewUrls = await props.buildPreviewUrls(list);\n        }\n        for (let i = 0; i < list.length; i++) {\n          list[i].previewUrl = previewUrls[i];\n        }\n      } else if (props.buildUrl) {\n        for (let item of list) {\n          item.url = await props.buildUrl(item.value);\n          item.previewUrl = item.url;\n          if (props.buildPreviewUrl) {\n            item.previewUrl = await props.buildPreviewUrl(item);\n          }\n        }\n      } else {\n        for (let i = 0; i < list.length; i++) {\n          list[i].url = list[i].value;\n          list[i].previewUrl = list[i].value;\n        }\n      }\n    }\n\n    async function buildImageListByValue(values: any) {\n      const images: ImageItem[] = [];\n      for (let i = 0; i < values.length; i++) {\n        let value = values[i];\n        images.push({\n          value,\n          index: i\n        });\n      }\n\n      await buildImageListUrls(images);\n\n      return buildImageList(images);\n    }\n\n    async function buildImageListByUrls(urls: any) {\n      const list: ImageItem[] = [];\n      if (typeof urls === \"string\") {\n        list.push({\n          value: urls,\n          url: urls,\n          index: 0,\n          previewUrl: urls\n        });\n      } else if (urls instanceof Array) {\n        if (urls.length > 0) {\n          if (typeof urls[0] === \"string\") {\n            for (let i = 0; i < urls.length; i++) {\n              const url = urls[i];\n              list.push({\n                value: url,\n                url: url,\n                previewUrl: url,\n                index: i\n              });\n            }\n          } else {\n            for (let i = 0; i < urls.length; i++) {\n              const url = urls[i];\n              list.push({\n                value: url.url,\n                ...url,\n                index: i\n              });\n            }\n          }\n        }\n      } else {\n        // is object\n        list.push({\n          value: urls.url,\n          ...urls,\n          index: 0\n        });\n      }\n\n      return buildImageList(list);\n    }\n    watch(\n      () => {\n        return computedValues.value;\n      },\n      async (values) => {\n        if (!props.urls) {\n          imageListRef.value = await buildImageListByValue(values);\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    watch(\n      () => {\n        return props.urls;\n      },\n      async (value) => {\n        if (value) {\n          imageListRef.value = await buildImageListByUrls(value);\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    return { imageListRef, ui, errorBinding };\n  }\n});\n</script>\n<style lang=\"less\">\n.fs-image-format {\n  display: flex;\n  margin: 1px;\n  flex-wrap: wrap;\n  justify-content: center;\n  align-items: center;\n\n  .fs-box {\n    display: flex;\n    align-items: center;\n  }\n\n  .fs-image-item {\n    border: 1px solid #eee;\n    margin: 0 1px;\n    object-fit: fill;\n    background-color: #eee;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .fs-image-slot {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    //  height: 100%;\n    width: 100%;\n    height: 100%;\n  }\n\n  .el-image-viewer__close {\n    color: #fff;\n  }\n\n  .el-image__error,\n  .el-image__inner,\n  .el-image__placeholder {\n    height: auto;\n  }\n\n  .el-image__wrapper {\n    position: relative;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n}\n</style>\n", "<template>\n  <div class=\"fs-image-format\">\n    <component :is=\"ui.imageGroup.name\" v-bind=\"wrapper\">\n      <component :is=\"ui.image.name\" v-for=\"item in imageListRef\" :key=\"item.src\" class=\"fs-image-item\" v-bind=\"item\">\n        <template #placeholder>\n          <div class=\"fs-image-slot\">\n            <fs-loading :loading=\"true\" v-bind=\"errorBinding\" />\n          </div>\n        </template>\n        <template #error>\n          <div class=\"fs-image-slot\">\n            <img :src=\"error\" v-bind=\"errorBinding\" />\n          </div>\n        </template>\n      </component>\n    </component>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, Ref, ref, watch } from \"vue\";\nimport { useUi } from \"@fast-crud/fast-crud\";\nimport { merge } from \"lodash-es\";\n// 图片行展示组件\nexport default defineComponent({\n  name: \"FsImagesFormat\",\n  inheritAttrs: false,\n  props: {\n    //包裹image的组件配置，antdv是preview-group，element是div\n    wrapper: {\n      type: Object,\n      default: null\n    },\n    // 图片的url\n    // 'value' 或 ['value','value']\n    modelValue: {\n      type: [String, Array, Object],\n      require: true\n    },\n    /**\n     * 构建好的图片链接，如果此处传值，则不走buildUrl方法\n     * 'url' 或 ['url1','url2'] 或 {url,previewUrl} 或 [{url,previewUrl}]\n     */\n    urls: {\n      type: [String, Object, Array]\n    },\n    /**\n     * 加载错误时显示的图片\n     */\n    error: {\n      default:\n        'data:image/svg+xml,%3Csvg xmlns=\"http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"%3E%3Cpath fill=\"%23888\" d=\"M5 21q-.825 0-1.413-.588T3 19v-6.6l3 3l4-4l4 4l4-4l3 3V19q0 .825-.588 1.413T19 21H5ZM5 3h14q.825 0 1.413.588T21 5v6.575l-3-3l-4 4l-4-4l-4 4l-3-3V5q0-.825.588-1.413T5 3Z\"%2F%3E%3C%2Fsvg%3E'\n    },\n    /**\n     * 从value构建图片下载url的方法\n     * 支持异步\n     */\n    buildUrl: {\n      type: Function,\n      default: function (value: any) {\n        return value;\n      }\n    },\n    buildUrls: {\n      type: Function,\n      default: null\n    },\n    /**\n     * 从value或url构建预览大图的方法\n     * 支持异步\n     */\n    buildPreviewUrl: {\n      type: Function,\n      default: function ({ url, value, index }: any) {\n        return url;\n      }\n    },\n\n    buildPreviewUrls: {\n      type: Function,\n      default: null\n    }\n  },\n  setup(props: any, ctx) {\n    const { ui } = useUi();\n    type ImageItem = {\n      value?: any;\n      url?: string;\n      index?: number;\n      previewUrl?: string;\n    };\n    const imageListRef: Ref<any[]> = ref([]);\n\n    const errorBinding = computed(() => {\n      return merge({ style: {} }, { style: ctx.attrs.style });\n    });\n\n    const computedValues = computed(() => {\n      const urls: any = [];\n      if (props.modelValue == null || props.modelValue === \"\") {\n        return urls;\n      }\n      if (typeof props.modelValue === \"string\") {\n        urls.push(props.modelValue);\n      } else if (Array.isArray(props.modelValue)) {\n        for (const item of props.modelValue) {\n          if (item == null) {\n            continue;\n          }\n          if (item.url != null) {\n            urls.push(item.url);\n          } else {\n            urls.push(item);\n          }\n        }\n      } else {\n        //object\n        if (props.modelValue.url != null) {\n          urls.push(props.modelValue.url);\n        } else {\n          urls.push(props.modelValue);\n        }\n      }\n      return urls;\n    });\n\n    function buildImageList(images: ImageItem[]) {\n      const urls: string[] = [];\n      const previewUrls: string[] = [];\n      for (let i = 0; i < images.length; i++) {\n        const image = images[i];\n        urls.push(image.url as string);\n        previewUrls.push(image.previewUrl as string);\n      }\n      const imageList: any[] = [];\n      for (let i = 0; i < images.length; i++) {\n        const image = images[i];\n        const url = image.url;\n        const previewUrl = image.url;\n        const preview = ui.image.buildPreviewBind({\n          url,\n          urls,\n          previewUrl,\n          previewUrls,\n          index: i\n        });\n        imageList.push({\n          fit: \"contain\",\n          src: url,\n          [ui.image.fallback]: props.error,\n          ...ctx.attrs,\n          ...preview\n        });\n      }\n      return imageList;\n    }\n\n    async function buildImageListUrls(list: ImageItem[]) {\n      if (props.buildUrls) {\n        const values = list.map((item) => item.value);\n        const urls = await props.buildUrls(values);\n        for (let i = 0; i < list.length; i++) {\n          list[i].url = urls[i];\n        }\n        let previewUrls = urls;\n        if (props.buildPreviewUrls) {\n          previewUrls = await props.buildPreviewUrls(list);\n        }\n        for (let i = 0; i < list.length; i++) {\n          list[i].previewUrl = previewUrls[i];\n        }\n      } else if (props.buildUrl) {\n        for (let item of list) {\n          item.url = await props.buildUrl(item.value);\n          item.previewUrl = item.url;\n          if (props.buildPreviewUrl) {\n            item.previewUrl = await props.buildPreviewUrl(item);\n          }\n        }\n      } else {\n        for (let i = 0; i < list.length; i++) {\n          list[i].url = list[i].value;\n          list[i].previewUrl = list[i].value;\n        }\n      }\n    }\n\n    async function buildImageListByValue(values: any) {\n      const images: ImageItem[] = [];\n      for (let i = 0; i < values.length; i++) {\n        let value = values[i];\n        images.push({\n          value,\n          index: i\n        });\n      }\n\n      await buildImageListUrls(images);\n\n      return buildImageList(images);\n    }\n\n    async function buildImageListByUrls(urls: any) {\n      const list: ImageItem[] = [];\n      if (typeof urls === \"string\") {\n        list.push({\n          value: urls,\n          url: urls,\n          index: 0,\n          previewUrl: urls\n        });\n      } else if (urls instanceof Array) {\n        if (urls.length > 0) {\n          if (typeof urls[0] === \"string\") {\n            for (let i = 0; i < urls.length; i++) {\n              const url = urls[i];\n              list.push({\n                value: url,\n                url: url,\n                previewUrl: url,\n                index: i\n              });\n            }\n          } else {\n            for (let i = 0; i < urls.length; i++) {\n              const url = urls[i];\n              list.push({\n                value: url.url,\n                ...url,\n                index: i\n              });\n            }\n          }\n        }\n      } else {\n        // is object\n        list.push({\n          value: urls.url,\n          ...urls,\n          index: 0\n        });\n      }\n\n      return buildImageList(list);\n    }\n    watch(\n      () => {\n        return computedValues.value;\n      },\n      async (values) => {\n        if (!props.urls) {\n          imageListRef.value = await buildImageListByValue(values);\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    watch(\n      () => {\n        return props.urls;\n      },\n      async (value) => {\n        if (value) {\n          imageListRef.value = await buildImageListByUrls(value);\n        }\n      },\n      {\n        immediate: true\n      }\n    );\n\n    return { imageListRef, ui, errorBinding };\n  }\n});\n</script>\n<style lang=\"less\">\n.fs-image-format {\n  display: flex;\n  margin: 1px;\n  flex-wrap: wrap;\n  justify-content: center;\n  align-items: center;\n\n  .fs-box {\n    display: flex;\n    align-items: center;\n  }\n\n  .fs-image-item {\n    border: 1px solid #eee;\n    margin: 0 1px;\n    object-fit: fill;\n    background-color: #eee;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .fs-image-slot {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    //  height: 100%;\n    width: 100%;\n    height: 100%;\n  }\n\n  .el-image-viewer__close {\n    color: #fff;\n  }\n\n  .el-image__error,\n  .el-image__inner,\n  .el-image__placeholder {\n    height: auto;\n  }\n\n  .el-image__wrapper {\n    position: relative;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n}\n</style>\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "/*!\n  * @soerenmartius/vue3-clipboard v0.1.2\n  * (c) 2021 Soeren Martius\n  * @license MIT\n  */\nvar commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};\n\nfunction getDefaultExportFromCjs (x) {\n\treturn x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;\n}\n\nfunction createCommonjsModule(fn, basedir, module) {\n\treturn module = {\n\t\tpath: basedir,\n\t\texports: {},\n\t\trequire: function (path, base) {\n\t\t\treturn commonjsRequire(path, (base === undefined || base === null) ? module.path : base);\n\t\t}\n\t}, fn(module, module.exports), module.exports;\n}\n\nfunction commonjsRequire () {\n\tthrow new Error('Dynamic requires are not currently supported by @rollup/plugin-commonjs');\n}\n\nvar clipboard = createCommonjsModule(function (module, exports) {\n/*!\n * clipboard.js v2.0.6\n * https://clipboardjs.com/\n * \n * Licensed MIT © Zeno Rocha\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tmodule.exports = factory();\n})(commonjsGlobal, function() {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 6);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\nfunction select(element) {\n    var selectedText;\n\n    if (element.nodeName === 'SELECT') {\n        element.focus();\n\n        selectedText = element.value;\n    }\n    else if (element.nodeName === 'INPUT' || element.nodeName === 'TEXTAREA') {\n        var isReadOnly = element.hasAttribute('readonly');\n\n        if (!isReadOnly) {\n            element.setAttribute('readonly', '');\n        }\n\n        element.select();\n        element.setSelectionRange(0, element.value.length);\n\n        if (!isReadOnly) {\n            element.removeAttribute('readonly');\n        }\n\n        selectedText = element.value;\n    }\n    else {\n        if (element.hasAttribute('contenteditable')) {\n            element.focus();\n        }\n\n        var selection = window.getSelection();\n        var range = document.createRange();\n\n        range.selectNodeContents(element);\n        selection.removeAllRanges();\n        selection.addRange(range);\n\n        selectedText = selection.toString();\n    }\n\n    return selectedText;\n}\n\nmodule.exports = select;\n\n\n/***/ }),\n/* 1 */\n/***/ (function(module, exports) {\n\nfunction E () {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\n\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n\n    return this;\n  },\n\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener () {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    }\n    listener._ = callback;\n    return this.on(name, listener, ctx);\n  },\n\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n\n    return this;\n  },\n\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback)\n          liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    (liveEvents.length)\n      ? e[name] = liveEvents\n      : delete e[name];\n\n    return this;\n  }\n};\n\nmodule.exports = E;\nmodule.exports.TinyEmitter = E;\n\n\n/***/ }),\n/* 2 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar is = __webpack_require__(3);\nvar delegate = __webpack_require__(4);\n\n/**\n * Validates all params and calls the right\n * listener function based on its target type.\n *\n * @param {String|HTMLElement|HTMLCollection|NodeList} target\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listen(target, type, callback) {\n    if (!target && !type && !callback) {\n        throw new Error('Missing required arguments');\n    }\n\n    if (!is.string(type)) {\n        throw new TypeError('Second argument must be a String');\n    }\n\n    if (!is.fn(callback)) {\n        throw new TypeError('Third argument must be a Function');\n    }\n\n    if (is.node(target)) {\n        return listenNode(target, type, callback);\n    }\n    else if (is.nodeList(target)) {\n        return listenNodeList(target, type, callback);\n    }\n    else if (is.string(target)) {\n        return listenSelector(target, type, callback);\n    }\n    else {\n        throw new TypeError('First argument must be a String, HTMLElement, HTMLCollection, or NodeList');\n    }\n}\n\n/**\n * Adds an event listener to a HTML element\n * and returns a remove listener function.\n *\n * @param {HTMLElement} node\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNode(node, type, callback) {\n    node.addEventListener(type, callback);\n\n    return {\n        destroy: function() {\n            node.removeEventListener(type, callback);\n        }\n    }\n}\n\n/**\n * Add an event listener to a list of HTML elements\n * and returns a remove listener function.\n *\n * @param {NodeList|HTMLCollection} nodeList\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNodeList(nodeList, type, callback) {\n    Array.prototype.forEach.call(nodeList, function(node) {\n        node.addEventListener(type, callback);\n    });\n\n    return {\n        destroy: function() {\n            Array.prototype.forEach.call(nodeList, function(node) {\n                node.removeEventListener(type, callback);\n            });\n        }\n    }\n}\n\n/**\n * Add an event listener to a selector\n * and returns a remove listener function.\n *\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenSelector(selector, type, callback) {\n    return delegate(document.body, selector, type, callback);\n}\n\nmodule.exports = listen;\n\n\n/***/ }),\n/* 3 */\n/***/ (function(module, exports) {\n\n/**\n * Check if argument is a HTML element.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.node = function(value) {\n    return value !== undefined\n        && value instanceof HTMLElement\n        && value.nodeType === 1;\n};\n\n/**\n * Check if argument is a list of HTML elements.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.nodeList = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return value !== undefined\n        && (type === '[object NodeList]' || type === '[object HTMLCollection]')\n        && ('length' in value)\n        && (value.length === 0 || exports.node(value[0]));\n};\n\n/**\n * Check if argument is a string.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.string = function(value) {\n    return typeof value === 'string'\n        || value instanceof String;\n};\n\n/**\n * Check if argument is a function.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.fn = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return type === '[object Function]';\n};\n\n\n/***/ }),\n/* 4 */\n/***/ (function(module, exports, __webpack_require__) {\n\nvar closest = __webpack_require__(5);\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction _delegate(element, selector, type, callback, useCapture) {\n    var listenerFn = listener.apply(this, arguments);\n\n    element.addEventListener(type, listenerFn, useCapture);\n\n    return {\n        destroy: function() {\n            element.removeEventListener(type, listenerFn, useCapture);\n        }\n    }\n}\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element|String|Array} [elements]\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction delegate(elements, selector, type, callback, useCapture) {\n    // Handle the regular Element usage\n    if (typeof elements.addEventListener === 'function') {\n        return _delegate.apply(null, arguments);\n    }\n\n    // Handle Element-less usage, it defaults to global delegation\n    if (typeof type === 'function') {\n        // Use `document` as the first parameter, then apply arguments\n        // This is a short way to .unshift `arguments` without running into deoptimizations\n        return _delegate.bind(null, document).apply(null, arguments);\n    }\n\n    // Handle Selector-based usage\n    if (typeof elements === 'string') {\n        elements = document.querySelectorAll(elements);\n    }\n\n    // Handle Array-like based usage\n    return Array.prototype.map.call(elements, function (element) {\n        return _delegate(element, selector, type, callback, useCapture);\n    });\n}\n\n/**\n * Finds closest match and invokes callback.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Function}\n */\nfunction listener(element, selector, type, callback) {\n    return function(e) {\n        e.delegateTarget = closest(e.target, selector);\n\n        if (e.delegateTarget) {\n            callback.call(element, e);\n        }\n    }\n}\n\nmodule.exports = delegate;\n\n\n/***/ }),\n/* 5 */\n/***/ (function(module, exports) {\n\nvar DOCUMENT_NODE_TYPE = 9;\n\n/**\n * A polyfill for Element.matches()\n */\nif (typeof Element !== 'undefined' && !Element.prototype.matches) {\n    var proto = Element.prototype;\n\n    proto.matches = proto.matchesSelector ||\n                    proto.mozMatchesSelector ||\n                    proto.msMatchesSelector ||\n                    proto.oMatchesSelector ||\n                    proto.webkitMatchesSelector;\n}\n\n/**\n * Finds the closest parent that matches a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @return {Function}\n */\nfunction closest (element, selector) {\n    while (element && element.nodeType !== DOCUMENT_NODE_TYPE) {\n        if (typeof element.matches === 'function' &&\n            element.matches(selector)) {\n          return element;\n        }\n        element = element.parentNode;\n    }\n}\n\nmodule.exports = closest;\n\n\n/***/ }),\n/* 6 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n__webpack_require__.r(__webpack_exports__);\n\n// EXTERNAL MODULE: ./node_modules/select/src/select.js\nvar src_select = __webpack_require__(0);\nvar select_default = /*#__PURE__*/__webpack_require__.n(src_select);\n\n// CONCATENATED MODULE: ./src/clipboard-action.js\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n\n\n/**\n * Inner class which performs selection from either `text` or `target`\n * properties and then executes copy or cut operations.\n */\n\nvar clipboard_action_ClipboardAction = function () {\n    /**\n     * @param {Object} options\n     */\n    function ClipboardAction(options) {\n        _classCallCheck(this, ClipboardAction);\n\n        this.resolveOptions(options);\n        this.initSelection();\n    }\n\n    /**\n     * Defines base properties passed from constructor.\n     * @param {Object} options\n     */\n\n\n    _createClass(ClipboardAction, [{\n        key: 'resolveOptions',\n        value: function resolveOptions() {\n            var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n            this.action = options.action;\n            this.container = options.container;\n            this.emitter = options.emitter;\n            this.target = options.target;\n            this.text = options.text;\n            this.trigger = options.trigger;\n\n            this.selectedText = '';\n        }\n\n        /**\n         * Decides which selection strategy is going to be applied based\n         * on the existence of `text` and `target` properties.\n         */\n\n    }, {\n        key: 'initSelection',\n        value: function initSelection() {\n            if (this.text) {\n                this.selectFake();\n            } else if (this.target) {\n                this.selectTarget();\n            }\n        }\n\n        /**\n         * Creates a fake textarea element, sets its value from `text` property,\n         * and makes a selection on it.\n         */\n\n    }, {\n        key: 'selectFake',\n        value: function selectFake() {\n            var _this = this;\n\n            var isRTL = document.documentElement.getAttribute('dir') == 'rtl';\n\n            this.removeFake();\n\n            this.fakeHandlerCallback = function () {\n                return _this.removeFake();\n            };\n            this.fakeHandler = this.container.addEventListener('click', this.fakeHandlerCallback) || true;\n\n            this.fakeElem = document.createElement('textarea');\n            // Prevent zooming on iOS\n            this.fakeElem.style.fontSize = '12pt';\n            // Reset box model\n            this.fakeElem.style.border = '0';\n            this.fakeElem.style.padding = '0';\n            this.fakeElem.style.margin = '0';\n            // Move element out of screen horizontally\n            this.fakeElem.style.position = 'absolute';\n            this.fakeElem.style[isRTL ? 'right' : 'left'] = '-9999px';\n            // Move element to the same position vertically\n            var yPosition = window.pageYOffset || document.documentElement.scrollTop;\n            this.fakeElem.style.top = yPosition + 'px';\n\n            this.fakeElem.setAttribute('readonly', '');\n            this.fakeElem.value = this.text;\n\n            this.container.appendChild(this.fakeElem);\n\n            this.selectedText = select_default()(this.fakeElem);\n            this.copyText();\n        }\n\n        /**\n         * Only removes the fake element after another click event, that way\n         * a user can hit `Ctrl+C` to copy because selection still exists.\n         */\n\n    }, {\n        key: 'removeFake',\n        value: function removeFake() {\n            if (this.fakeHandler) {\n                this.container.removeEventListener('click', this.fakeHandlerCallback);\n                this.fakeHandler = null;\n                this.fakeHandlerCallback = null;\n            }\n\n            if (this.fakeElem) {\n                this.container.removeChild(this.fakeElem);\n                this.fakeElem = null;\n            }\n        }\n\n        /**\n         * Selects the content from element passed on `target` property.\n         */\n\n    }, {\n        key: 'selectTarget',\n        value: function selectTarget() {\n            this.selectedText = select_default()(this.target);\n            this.copyText();\n        }\n\n        /**\n         * Executes the copy operation based on the current selection.\n         */\n\n    }, {\n        key: 'copyText',\n        value: function copyText() {\n            var succeeded = void 0;\n\n            try {\n                succeeded = document.execCommand(this.action);\n            } catch (err) {\n                succeeded = false;\n            }\n\n            this.handleResult(succeeded);\n        }\n\n        /**\n         * Fires an event based on the copy operation result.\n         * @param {Boolean} succeeded\n         */\n\n    }, {\n        key: 'handleResult',\n        value: function handleResult(succeeded) {\n            this.emitter.emit(succeeded ? 'success' : 'error', {\n                action: this.action,\n                text: this.selectedText,\n                trigger: this.trigger,\n                clearSelection: this.clearSelection.bind(this)\n            });\n        }\n\n        /**\n         * Moves focus away from `target` and back to the trigger, removes current selection.\n         */\n\n    }, {\n        key: 'clearSelection',\n        value: function clearSelection() {\n            if (this.trigger) {\n                this.trigger.focus();\n            }\n            document.activeElement.blur();\n            window.getSelection().removeAllRanges();\n        }\n\n        /**\n         * Sets the `action` to be performed which can be either 'copy' or 'cut'.\n         * @param {String} action\n         */\n\n    }, {\n        key: 'destroy',\n\n\n        /**\n         * Destroy lifecycle.\n         */\n        value: function destroy() {\n            this.removeFake();\n        }\n    }, {\n        key: 'action',\n        set: function set() {\n            var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'copy';\n\n            this._action = action;\n\n            if (this._action !== 'copy' && this._action !== 'cut') {\n                throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');\n            }\n        }\n\n        /**\n         * Gets the `action` property.\n         * @return {String}\n         */\n        ,\n        get: function get() {\n            return this._action;\n        }\n\n        /**\n         * Sets the `target` property using an element\n         * that will be have its content copied.\n         * @param {Element} target\n         */\n\n    }, {\n        key: 'target',\n        set: function set(target) {\n            if (target !== undefined) {\n                if (target && (typeof target === 'undefined' ? 'undefined' : _typeof(target)) === 'object' && target.nodeType === 1) {\n                    if (this.action === 'copy' && target.hasAttribute('disabled')) {\n                        throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\n                    }\n\n                    if (this.action === 'cut' && (target.hasAttribute('readonly') || target.hasAttribute('disabled'))) {\n                        throw new Error('Invalid \"target\" attribute. You can\\'t cut text from elements with \"readonly\" or \"disabled\" attributes');\n                    }\n\n                    this._target = target;\n                } else {\n                    throw new Error('Invalid \"target\" value, use a valid Element');\n                }\n            }\n        }\n\n        /**\n         * Gets the `target` property.\n         * @return {String|HTMLElement}\n         */\n        ,\n        get: function get() {\n            return this._target;\n        }\n    }]);\n\n    return ClipboardAction;\n}();\n\n/* harmony default export */ var clipboard_action = (clipboard_action_ClipboardAction);\n// EXTERNAL MODULE: ./node_modules/tiny-emitter/index.js\nvar tiny_emitter = __webpack_require__(1);\nvar tiny_emitter_default = /*#__PURE__*/__webpack_require__.n(tiny_emitter);\n\n// EXTERNAL MODULE: ./node_modules/good-listener/src/listen.js\nvar listen = __webpack_require__(2);\nvar listen_default = /*#__PURE__*/__webpack_require__.n(listen);\n\n// CONCATENATED MODULE: ./src/clipboard.js\nvar clipboard_typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar clipboard_createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction clipboard_classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\n\n\n\n\n/**\n * Base class which takes one or more elements, adds event listeners to them,\n * and instantiates a new `ClipboardAction` on each click.\n */\n\nvar clipboard_Clipboard = function (_Emitter) {\n    _inherits(Clipboard, _Emitter);\n\n    /**\n     * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n     * @param {Object} options\n     */\n    function Clipboard(trigger, options) {\n        clipboard_classCallCheck(this, Clipboard);\n\n        var _this = _possibleConstructorReturn(this, (Clipboard.__proto__ || Object.getPrototypeOf(Clipboard)).call(this));\n\n        _this.resolveOptions(options);\n        _this.listenClick(trigger);\n        return _this;\n    }\n\n    /**\n     * Defines if attributes would be resolved using internal setter functions\n     * or custom functions that were passed in the constructor.\n     * @param {Object} options\n     */\n\n\n    clipboard_createClass(Clipboard, [{\n        key: 'resolveOptions',\n        value: function resolveOptions() {\n            var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n            this.action = typeof options.action === 'function' ? options.action : this.defaultAction;\n            this.target = typeof options.target === 'function' ? options.target : this.defaultTarget;\n            this.text = typeof options.text === 'function' ? options.text : this.defaultText;\n            this.container = clipboard_typeof(options.container) === 'object' ? options.container : document.body;\n        }\n\n        /**\n         * Adds a click event listener to the passed trigger.\n         * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n         */\n\n    }, {\n        key: 'listenClick',\n        value: function listenClick(trigger) {\n            var _this2 = this;\n\n            this.listener = listen_default()(trigger, 'click', function (e) {\n                return _this2.onClick(e);\n            });\n        }\n\n        /**\n         * Defines a new `ClipboardAction` on each click event.\n         * @param {Event} e\n         */\n\n    }, {\n        key: 'onClick',\n        value: function onClick(e) {\n            var trigger = e.delegateTarget || e.currentTarget;\n\n            if (this.clipboardAction) {\n                this.clipboardAction = null;\n            }\n\n            this.clipboardAction = new clipboard_action({\n                action: this.action(trigger),\n                target: this.target(trigger),\n                text: this.text(trigger),\n                container: this.container,\n                trigger: trigger,\n                emitter: this\n            });\n        }\n\n        /**\n         * Default `action` lookup function.\n         * @param {Element} trigger\n         */\n\n    }, {\n        key: 'defaultAction',\n        value: function defaultAction(trigger) {\n            return getAttributeValue('action', trigger);\n        }\n\n        /**\n         * Default `target` lookup function.\n         * @param {Element} trigger\n         */\n\n    }, {\n        key: 'defaultTarget',\n        value: function defaultTarget(trigger) {\n            var selector = getAttributeValue('target', trigger);\n\n            if (selector) {\n                return document.querySelector(selector);\n            }\n        }\n\n        /**\n         * Returns the support of the given action, or all actions if no action is\n         * given.\n         * @param {String} [action]\n         */\n\n    }, {\n        key: 'defaultText',\n\n\n        /**\n         * Default `text` lookup function.\n         * @param {Element} trigger\n         */\n        value: function defaultText(trigger) {\n            return getAttributeValue('text', trigger);\n        }\n\n        /**\n         * Destroy lifecycle.\n         */\n\n    }, {\n        key: 'destroy',\n        value: function destroy() {\n            this.listener.destroy();\n\n            if (this.clipboardAction) {\n                this.clipboardAction.destroy();\n                this.clipboardAction = null;\n            }\n        }\n    }], [{\n        key: 'isSupported',\n        value: function isSupported() {\n            var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ['copy', 'cut'];\n\n            var actions = typeof action === 'string' ? [action] : action;\n            var support = !!document.queryCommandSupported;\n\n            actions.forEach(function (action) {\n                support = support && !!document.queryCommandSupported(action);\n            });\n\n            return support;\n        }\n    }]);\n\n    return Clipboard;\n}(tiny_emitter_default.a);\n\n/**\n * Helper function to retrieve attribute value.\n * @param {String} suffix\n * @param {Element} element\n */\n\n\nfunction getAttributeValue(suffix, element) {\n    var attribute = 'data-clipboard-' + suffix;\n\n    if (!element.hasAttribute(attribute)) {\n        return;\n    }\n\n    return element.getAttribute(attribute);\n}\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (clipboard_Clipboard);\n\n/***/ })\n/******/ ])[\"default\"];\n});\n});\n\nvar ClipboardJS = /*@__PURE__*/getDefaultExportFromCjs(clipboard);\n\nconst defaultConfig = {\r\n    autoSetContainer: false,\r\n    appendToBody: true,\r\n};\r\nconst VueClipboard = {\r\n    config: (options) => {\r\n        const { autoSetContainer, appendToBody } = options;\r\n        defaultConfig.autoSetContainer = autoSetContainer ? autoSetContainer : false;\r\n        defaultConfig.appendToBody = appendToBody ? appendToBody : true;\r\n    },\r\n    install: (app) => {\r\n        app.config.globalProperties.$vclipboard = toClipboard;\r\n        app.directive('clipboard', {\r\n            beforeMount(el, binding) {\r\n                if (binding.arg === 'success') {\r\n                    el._vClipboard_success = binding.value;\r\n                }\r\n                else if (binding.arg === 'error') {\r\n                    el._vClipboard_error = binding.value;\r\n                }\r\n                else {\r\n                    const clipboard = new ClipboardJS(el, {\r\n                        text: () => binding.value,\r\n                        action: () => {\r\n                            return binding.arg === 'cut' ? 'cut' : 'copy';\r\n                        },\r\n                        container: defaultConfig.autoSetContainer ? el : undefined,\r\n                    });\r\n                    clipboard.on('success', (e) => {\r\n                        const callback = el._vClipboard_success;\r\n                        callback && callback(e);\r\n                    });\r\n                    clipboard.on('error', (e) => {\r\n                        const callback = el._vClipboard_error;\r\n                        callback && callback(e);\r\n                    });\r\n                    el._vClipboard = clipboard;\r\n                }\r\n            },\r\n            updated(el, binding) {\r\n                if (binding.arg === 'success') {\r\n                    el._vClipboard_success = binding.value;\r\n                }\r\n                else if (binding.arg === 'error') {\r\n                    el._vClipboard_error = binding.value;\r\n                }\r\n                else {\r\n                    el._vClipboard.text = () => {\r\n                        return binding.value;\r\n                    };\r\n                    el._vClipboard.action = () => {\r\n                        return binding.arg === 'cut' ? 'cut' : 'copy';\r\n                    };\r\n                }\r\n            },\r\n            unmounted(el, binding) {\r\n                if (binding.arg === 'success') {\r\n                    delete el._vClipboard_success;\r\n                }\r\n                else if (binding.arg === 'error') {\r\n                    delete el._vClipboard_error;\r\n                }\r\n                else {\r\n                    el._vClipboard.destroy();\r\n                    delete el._vClipboard;\r\n                }\r\n            },\r\n        });\r\n    },\r\n    toClipboard: (text, action) => toClipboard(text, action),\r\n};\r\nconst toClipboard = (text, action = 'copy') => {\r\n    return new Promise((resolve, reject) => {\r\n        const fakeElement = document.createElement('button');\r\n        const clipboard = new ClipboardJS(fakeElement, {\r\n            text: () => text,\r\n            action: () => action,\r\n        });\r\n        clipboard.on('success', (e) => {\r\n            clipboard.destroy();\r\n            resolve(e);\r\n        });\r\n        clipboard.on('error', (e) => {\r\n            clipboard.destroy();\r\n            reject(e);\r\n        });\r\n        if (defaultConfig.appendToBody) {\r\n            document.body.appendChild(fakeElement);\r\n        }\r\n        fakeElement.click();\r\n        if (defaultConfig.appendToBody) {\r\n            document.body.removeChild(fakeElement);\r\n        }\r\n    });\r\n};\n\nexport { VueClipboard, toClipboard };\n", null, null, null, null, null, "<template>\n  <div class=\"fs-phone-input\">\n    <fs-dict-select\n      :disabled=\"disabled\"\n      :readonly=\"readonly\"\n      :filterable=\"filterable\"\n      :clearable=\"clearable\"\n      :options=\"countryOptions\"\n      :dict=\"countryDict\"\n      :show-search=\"true\"\n      :allow-clear=\"true\"\n      v-bind=\"computedSelect\"\n    />\n    <component\n      :is=\"ui.input.name\"\n      type=\"text\"\n      :clearable=\"clearable\"\n      :disabled=\"disabled\"\n      :readonly=\"readonly\"\n      :allow-clear=\"true\"\n      v-bind=\"computedInput\"\n    >\n    </component>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { computed, Ref, ref, watch } from \"vue\";\nimport { dict, useUi } from \"@fast-crud/fast-crud\";\nimport { merge, cloneDeep } from \"lodash-es\";\nimport { getCountries } from \"./utils\";\nconst { ui } = useUi();\nimport { getCountryByValue as getCountryByValueFromUtil } from \"./utils\";\ntype PhoneInputValue = {\n  callingCode?: string;\n  countryCode?: string;\n  phoneNumber?: string;\n};\n\ntype PhoneInputProps = {\n  /**\n   * 选择框参数\n   */\n  select?: {\n    width?: string;\n    placeholder?: string;\n    [key: string]: any;\n  };\n\n  /**\n   * 号码框参数\n   */\n  input?: {\n    placeholder?: string;\n    [key: string]: any;\n  };\n  /**\n   * 输入输出值\n   */\n  modelValue?: PhoneInputValue; // 结构\n  /**\n   * 仅限哪些国家\n   */\n  onlyCountries?: string[];\n  /**\n   * 忽略哪些国家\n   */\n  ignoredCountries?: string[];\n  /**\n   * 优先哪些国家\n   */\n  priorityCountries?: string[];\n\n  clearable?: boolean;\n  filterable?: boolean;\n  /**\n   * 默认国家\n   */\n  defaultCountry?: string;\n  disabled?: boolean;\n  readonly?: boolean;\n};\nconst formValidator = ui.formItem.injectFormItemContext();\nconst props = withDefaults(defineProps<PhoneInputProps>(), {\n  defaultCountry: \"CN\"\n});\n\nconst emits = defineEmits([\"change\", \"input\", \"update:modelValue\"]);\n// eslint-disable-next-line vue/no-setup-props-destructure\nconst selectValue: Ref = ref<PhoneInputValue>(\n  props.modelValue || {\n    callingCode: undefined, // 电话区号\n    countryCode: undefined, // 国家代码\n    phoneNumber: undefined // 电话号码\n  }\n);\n\nconst countryDict = dict({\n  value: \"iso2\",\n  label: \"label\"\n});\n\nconst countriesRef = ref([]);\n\nasync function loadCountries() {\n  countriesRef.value = await getCountries();\n}\nloadCountries();\n\nconst countryOptions = computed(() => {\n  const countries = countriesRef.value;\n  let options = [];\n  if (props.onlyCountries != null && props.onlyCountries.length > 0) {\n    for (let country of countries) {\n      if (props.onlyCountries.find((item) => item.toLowerCase() === country.iso2.toLowerCase())) {\n        options.push(country);\n      }\n    }\n  } else {\n    const priorityCountries = props.priorityCountries || [];\n    const ignoredCountries = props.ignoredCountries || [];\n    const priorities = [];\n    const leaved = [];\n    for (let country of countries) {\n      if (priorityCountries.find((item) => item.toLowerCase() === country.iso2.toLowerCase())) {\n        priorities.push(country);\n      }\n      if (!ignoredCountries.find((item) => item.toLowerCase() === country.iso2.toLowerCase())) {\n        leaved.push(country);\n      }\n    }\n    options = priorities.concat(leaved);\n  }\n\n  options = options.map((item) => {\n    return {\n      ...item,\n      label: item.name + \"(\" + item.dialCode + \")\"\n    };\n  });\n  return options;\n});\n\nconst computedSelect = computed(() => {\n  const def = {\n    placeholder: \"请选择\",\n    [ui.select.filterable]: true,\n    [ui.select.clearable]: true,\n    [ui.select.modelValue]: selectValue.value.countryCode,\n    [\"onUpdate:\" + ui.select.modelValue]: handleSelectInput\n  };\n  return merge(def, props.select);\n});\n\nconst computedInput = computed(() => {\n  const def: any = {\n    placeholder: \"请输入\",\n    [ui.select.clearable]: true,\n    [ui.input.modelValue]: selectValue.value.phoneNumber,\n    [`onUpdate:${ui.input.modelValue}`]: handleNumberInput\n  };\n  return merge(def, props.input);\n});\n\nfunction isChanged(value: any) {\n  if (value === selectValue.value) {\n    return false;\n  }\n  return true;\n  // if (value && selectValue.value) {\n  //   return (\n  //     value.callingCode !== selectValue.value.callingCode ||\n  //     value.countryCode !== selectValue.value.countryCode ||\n  //     value.phoneNumber !== selectValue.value.phoneNumber\n  //   );\n  // } else {\n  //   return value !== selectValue.value;\n  // }\n}\n\nasync function setValue(value: any) {\n  selectValue.value = { callingCode: undefined, countryCode: undefined, phoneNumber: undefined };\n  const ret = await getCountryByValue(value);\n  if (ret != null) {\n    selectValue.value.callingCode = ret.callingCode;\n    selectValue.value.countryCode = ret.countryCode;\n  }\n  if (value && value.phoneNumber) {\n    selectValue.value.phoneNumber = value.phoneNumber;\n  } else {\n    selectValue.value.phoneNumber = undefined;\n  }\n}\nasync function getCountryByValue(value: any): Promise<any> {\n  let ret: any = null;\n  if (value != null) {\n    if (value.countryCode != null) {\n      ret = countryOptions.value.find((item) => item.iso2 === value.countryCode);\n    } else if (value.callingCode != null) {\n      ret = countryOptions.value.find((item) => item.dialCode === value.callingCode);\n    }\n  }\n  if (ret != null) {\n    ret = {\n      callingCode: ret.dialCode,\n      countryCode: ret.iso2\n    };\n  }\n  if (ret == null) {\n    ret = await getCountryByValueFromUtil({ countryCode: props.defaultCountry });\n  }\n  return ret;\n}\n\nasync function handleSelectInput(countryCode: any) {\n  await changeCountry(countryCode);\n  let emitValue: any = getEmitValue();\n  emits(\"update:modelValue\", emitValue);\n  emits(\"input\", emitValue);\n  emits(\"change\", emitValue);\n  await formValidator.onChange();\n  await formValidator.onBlur();\n}\n\nasync function handleNumberInput(number: any) {\n  selectValue.value.phoneNumber = number;\n  if (selectValue.value.callingCode == null && selectValue.value.countryCode == null) {\n    selectValue.value.countryCode = props.defaultCountry;\n    const country = await getCountryByValue(selectValue.value);\n    if (country) {\n      selectValue.value.callingCode = country.callingCode;\n    }\n  }\n  let emitValue = getEmitValue();\n  emits(\"update:modelValue\", emitValue);\n  emits(\"input\", emitValue);\n  emits(\"change\", emitValue);\n  await formValidator.onChange();\n  await formValidator.onBlur();\n}\n\nfunction getEmitValue(): PhoneInputValue {\n  return {\n    countryCode: selectValue.value.countryCode,\n    callingCode: selectValue.value.callingCode,\n    phoneNumber: selectValue.value.phoneNumber\n  };\n}\n\nasync function changeCountry(countryCode: any) {\n  if (!countryCode) {\n    selectValue.value.callingCode = undefined;\n  }\n  selectValue.value.countryCode = countryCode;\n  let ret = await getCountryByValue(selectValue.value);\n  if (ret) {\n    selectValue.value.callingCode = ret.callingCode;\n  }\n}\n\nwatch(\n  () => {\n    return props.modelValue;\n  },\n  async (value, oldValue) => {\n    await setValue(value);\n    emits(\"change\", selectValue.value);\n  },\n  {\n    immediate: true\n  }\n);\n</script>\n<style lang=\"less\">\n.fs-phone-input {\n  display: flex;\n  align-items: center;\n  .ant-select {\n    width: 120px;\n    max-width: 50%;\n    .ant-select-selector {\n      border-top-right-radius: 0;\n      border-bottom-right-radius: 0;\n    }\n  }\n\n  .ant-input,\n  .ant-input-affix-wrapper {\n    border-bottom-left-radius: 0;\n    border-top-left-radius: 0;\n    border-left: 0;\n  }\n\n  .el-select {\n    width: 100px;\n    max-width: 50%;\n\n    .el-input {\n      border-right: 0;\n    }\n    .el-input__wrapper {\n      border-right: 0;\n      border-top-right-radius: 0;\n      border-bottom-right-radius: 0;\n    }\n  }\n  .el-input {\n    border-left: 0;\n    .el-input__wrapper {\n      border-bottom-left-radius: 0;\n      border-top-left-radius: 0;\n      border-left: 0;\n    }\n  }\n\n  .n-select {\n    width: 100px;\n    max-width: 50%;\n    .n-base-selection {\n      border-top-right-radius: 0;\n      border-bottom-right-radius: 0;\n    }\n    .n-base-selection__border {\n      border-right: 0;\n    }\n  }\n\n  .n-input {\n    border-bottom-left-radius: 0;\n    border-top-left-radius: 0;\n  }\n}\n</style>\n", null, null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAAA,KAAeC,gBAAgB;EAC7B,MAAM;EACN,cAAc;EACd,OAAO;;IAEL,SAAS;MACP,MAAM;MACN,SAAS;IACX;;;IAGA,YAAY;MACV,MAAM,CAAC,QAAQ,OAAO,MAAM;MAC5B,SAAS;IACX;;;;;IAKA,MAAM;MACJ,MAAM,CAAC,QAAQ,QAAQ,KAAK;IAC9B;;;;IAIA,OAAO;MACL,SACE;IACJ;;;;;IAKA,UAAU;MACR,MAAM;MACN,SAAS,SAAUC,GAAY;AACtB,eAAAA;MACT;IACF;IACA,WAAW;MACT,MAAM;MACN,SAAS;IACX;;;;;IAKA,iBAAiB;MACf,MAAM;MACN,SAAS,SAAU,EAAE,KAAAC,GAAK,OAAAD,GAAO,OAAAE,EAAAA,GAAc;AACtC,eAAAD;MACT;IACF;IAEA,kBAAkB;MAChB,MAAM;MACN,SAAS;IACX;EACF;EACA,MAAME,GAAYC,GAAK;AACf,UAAA,EAAE,IAAAC,EAAAA,IAAOC,EAAAA,GAOTC,KAA2BC,IAAI,CAAA,CAAE,GAEjCC,IAAeC,SAAS,MACrBC,cAAM,EAAE,OAAO,CAAA,EAAM,GAAA,EAAE,OAAOP,EAAI,MAAM,MAAA,CAAO,CACvD,GAEKQ,IAAiBF,SAAS,MAAM;AACpC,YAAMG,IAAY,CAAA;AAClB,UAAIV,EAAM,cAAc,QAAQA,EAAM,eAAe;AAC5C,eAAAU;AAEL,UAAA,OAAOV,EAAM,cAAe;AACzBU,UAAA,KAAKV,EAAM,UAAU;eACjB,MAAM,QAAQA,EAAM,UAAU;AAC5B,mBAAAW,KAAQX,EAAM;AACnBW,eAAQ,SAGRA,EAAK,OAAO,OACTD,EAAA,KAAKC,EAAK,GAAG,IAElBD,EAAK,KAAKC,CAAI;;AAKdX,UAAM,WAAW,OAAO,OACrBU,EAAA,KAAKV,EAAM,WAAW,GAAG,IAEzBU,EAAA,KAAKV,EAAM,UAAU;AAGvB,aAAAU;IAAA,CACR;AAED,aAASE,EAAeC,GAAqB;AAC3C,YAAMH,IAAiB,CAAA,GACjBI,IAAwB,CAAA;AAC9B,eAASC,IAAI,GAAGA,IAAIF,EAAO,QAAQE,KAAK;AAChC,cAAAC,IAAQH,EAAOE,CAAC;AACjBL,UAAA,KAAKM,EAAM,GAAa,GACjBF,EAAA,KAAKE,EAAM,UAAoB;MAC7C;AACA,YAAMC,IAAmB,CAAA;AACzB,eAASF,IAAI,GAAGA,IAAIF,EAAO,QAAQE,KAAK;AAChC,cAAAC,IAAQH,EAAOE,CAAC,GAChBjB,IAAMkB,EAAM,KACZE,IAAaF,EAAM,KACnBG,IAAUjB,EAAG,MAAM,iBAAiB;UACxC,KAAAJ;UACA,MAAAY;UACA,YAAAQ;UACA,aAAAJ;UACA,OAAOC;QAAA,CACR;AACDE,UAAU,KAAK;UACb,KAAK;UACL,KAAKnB;UACL,CAACI,EAAG,MAAM,QAAQ,GAAGF,EAAM;UAC3B,GAAGC,EAAI;UACP,GAAGkB;QAAA,CACJ;MACH;AACO,aAAAF;IACT;AAEA,mBAAeG,EAAmBC,GAAmB;AACnD,UAAIrB,EAAM,WAAW;AACnB,cAAMsB,IAASD,EAAK,IAAI,CAACV,MAASA,EAAK,KAAK,GACtCD,IAAO,MAAMV,EAAM,UAAUsB,CAAM;AACzC,iBAASP,IAAI,GAAGA,IAAIM,EAAK,QAAQN;AAC/BM,YAAKN,CAAC,EAAE,MAAML,EAAKK,CAAC;AAEtB,YAAID,IAAcJ;AACdV,UAAM,qBACMc,IAAA,MAAMd,EAAM,iBAAiBqB,CAAI;AAEjD,iBAASN,IAAI,GAAGA,IAAIM,EAAK,QAAQN;AAC/BM,YAAKN,CAAC,EAAE,aAAaD,EAAYC,CAAC;MACpC,WACSf,EAAM;AACf,iBAASW,KAAQU;AACfV,YAAK,MAAM,MAAMX,EAAM,SAASW,EAAK,KAAK,GAC1CA,EAAK,aAAaA,EAAK,KACnBX,EAAM,oBACRW,EAAK,aAAa,MAAMX,EAAM,gBAAgBW,CAAI;;AAItD,iBAASI,IAAI,GAAGA,IAAIM,EAAK,QAAQN;AAC/BM,YAAKN,CAAC,EAAE,MAAMM,EAAKN,CAAC,EAAE,OACtBM,EAAKN,CAAC,EAAE,aAAaM,EAAKN,CAAC,EAAE;IAGnC;AAEA,mBAAeQ,EAAsBD,GAAa;AAChD,YAAMT,IAAsB,CAAA;AAC5B,eAASE,IAAI,GAAGA,IAAIO,EAAO,QAAQP,KAAK;AAClC,YAAAlB,IAAQyB,EAAOP,CAAC;AACpBF,UAAO,KAAK;UACV,OAAAhB;UACA,OAAOkB;QAAA,CACR;MACH;AAEA,aAAA,MAAMK,EAAmBP,CAAM,GAExBD,EAAeC,CAAM;IAC9B;AAEA,mBAAeW,EAAqBd,GAAW;AAC7C,YAAMW,IAAoB,CAAA;AACtB,UAAA,OAAOX,KAAS;AAClBW,UAAK,KAAK;UACR,OAAOX;UACP,KAAKA;UACL,OAAO;UACP,YAAYA;QAAA,CACb;eACQA,aAAgB,OAAA;AACrB,YAAAA,EAAK,SAAS;AAChB,cAAI,OAAOA,EAAK,CAAC,KAAM;AACrB,qBAASK,IAAI,GAAGA,IAAIL,EAAK,QAAQK,KAAK;AAC9B,oBAAAjB,IAAMY,EAAKK,CAAC;AAClBM,gBAAK,KAAK;gBACR,OAAOvB;gBACP,KAAAA;gBACA,YAAYA;gBACZ,OAAOiB;cAAA,CACR;YACH;;AAEA,qBAASA,IAAI,GAAGA,IAAIL,EAAK,QAAQK,KAAK;AAC9B,oBAAAjB,IAAMY,EAAKK,CAAC;AAClBM,gBAAK,KAAK;gBACR,OAAOvB,EAAI;gBACX,GAAGA;gBACH,OAAOiB;cAAA,CACR;YACH;MAAA;AAKJM,UAAK,KAAK;UACR,OAAOX,EAAK;UACZ,GAAGA;UACH,OAAO;QAAA,CACR;AAGH,aAAOE,EAAeS,CAAI;IAC5B;AACA,WAAAI;MACE,MACShB,EAAe;MAExB,OAAOa,MAAW;AACXtB,UAAM,SACII,GAAA,QAAQ,MAAMmB,EAAsBD,CAAM;MAE3D;MACA;QACE,WAAW;MACb;IAAA,GAGFG;MACE,MACSzB,EAAM;MAEf,OAAOH,MAAU;AACXA,cACWO,GAAA,QAAQ,MAAMoB,EAAqB3B,CAAK;MAEzD;MACA;QACE,WAAW;MACb;IAAA,GAGK,EAAE,cAAAO,IAAc,IAAAF,GAAI,cAAAI,EAAAA;EAC7B;AACF,CAAC;;;;;;;ICzQcoB,KAAA,EAAA,OAAM,kBAAA;IAVrBC,KAAA,EAAA,OAAA,gBAAA;IAAA,KAAA,EAAA,OAAA,gBAAA;IAAA,KAAA,CAAA,KAAA;;YACEC,iBAeM,YAAA;AAhBR,SAAAC,UAAA,GAGqCC,mBAA4B,OAAAJ,IAAA;KAAAG,UAAA,GAAAE,YAA3DC,wBAWYC,EAAA,GAdlB,WAGoD7B,IAAAA,GAAAA,eAAR8B,mBAAID,EAAA,OAAA,CAAA,GAAA;MAAA,SAAAE,QAAA,MAAA;SAAwBN,UAAA,IAAK,GAAGC,mBAAAM,UAAA,MAAAC,WAAAJ,EAAA,cAAA,CAAAtB,OAAOkB,UAAA,GAAgBE,YAAAC,wBAAAC,EAAA,GAAA,MAAA,IAAA,GAAAK,WAAA;UAHvG,KAAA3B,EAAA;UAGoH,OAAA;UACjG,SAAA;QACT,GAAAA,CAAA,GAAA;UAAA,aACEwB,QAAoD,MAAA;YAA1BI,gBAAA,OAAAZ,IAAA;cANtCa,YAAAC,GAAAH,WAAA;gBAMgDhC,SAAAA;gBAAAA,SAAAA;cAAAA,GAAAA,EAAAA,YAAAA,GAAAA,MAAAA,EAAAA;YAG7B,CAAA;UAAA,CAAA;UAEP,OAAA6B,QAAA,MAAA;YAAgBI,gBAAA,OAAAG,IAAA;cAX5BH,gBAAA,OAAAD,WAAA;gBAWsChC,KAAAA,EAAAA;gBAAAA,SAAAA;cAAAA,GAAAA,EAAAA,YAAAA,GAAAA,MAAAA,IAAAA,EAAAA;YAXtC,CAAA;UAAA,CAAA;UAAA,GAAA;iBAAA,GAAA,GAAA;MAAA,CAAA;MAAA,GAAA;;;;;;;;;ICCaqC,KAAkC,CAACC,MACvC,OAAOC,GAAWhD,MAAc;AACrC,QAAMQ,KAAM,MAAMuC,EAAoBC,EAAK,WAAW,IAAI;AACtD,MAAAxC,MAAOA,GAAI,aAAA;AACP,UAAA,IAAI,MAAM,YAAY;AAEvB,SAAA;AAAA;IAIEyC,IAA4B,MAChCC,GAAQ,CAAC,EAAE,iBAAAC,EAAAA,MACTL,GAAgCK,CAAe,CACvD;IAQUC,KAAsB,CAACC,GAA0BC,OACxDD,KAAa,SACfA,IAAY,CAAA,IAEdA,EAAU,KAAK;;EAEb,WAAWJ,EAA2B;EACtC,SAASK,KAA2B;EACpC,SAAS;;AAAA,CACV,GACMD;AC9BK,SAAAE,KAAA;AACZ,QAAM;IAAEC,GAAAA;EAAG,IAAGC,GAAO,GACfpD,IAAKqD,EAAUC,IAAAA;AACrB,SAAO;IACL,kBAAkB;MAChBC,MAAM;QACJC,WAAW;UACTC,MAAM;UACNC,UAAU1D,EAAG2D,OAAOC;UACpBC,QAAQ;QACT;QACD,CAAC7D,EAAG8D,SAASC,KAAK,GAAG,CACnB;UACEC,WAAWpB,EAA2B;UACtCqB,SAASd,EAAE,sCAAsC;UACjDe,SAAS;QAAA,CACV;MAEJ;MACDC,QAAQ;QACNX,WAAW;UACTC,MAAM;UACNW,OAAO;UACPC,mBAAmB;;UAEnBC,aAAaA,MAAAhC,YAAA,OAAA;YAAA,OAAkB;UAAeA,GAAAA,CAAAA,YAAAZ,iBAAA,SAAA,GAAA;YAAA,MAAkB1B,EAAGuE,MAAMC;UAAG,GAAA,IAAA,CAAA,CAAA;QAC7E;MACF;MACDC,UAAU;QACRjB,WAAW;UAAEkB,QAAQ;UAAKC,OAAO;QAAK;MACvC;IACF;IACD,mBAAmB;MACjBpB,MAAM;QACJ,CAACvD,EAAG8D,SAASC,KAAK,GAAG,CACnB;UACEC,WAAWpB,EAA2B;UACtCqB,SAASd,EAAE,sCAAsC;UACjDe,SAAS;QACV,CAAA;QAEHV,WAAW;UACTC,MAAM;UACNmB,OAAO;UACPlB,UAAU1D,EAAG2D,OAAOC;UACpBC,QAAQ;QACT;MACF;MACDM,QAAQ;QACNU,OAAO;QACPrB,WAAW;UAAEC,MAAM;UAAoBW,OAAO;UAAcC,mBAAmB;QAAM;MACtF;MACDI,UAAU;QACRjB,WAAW;UAAEkB,QAAQ;UAAKC,OAAO;QAAK;MACvC;MACDG,aAAa;QAAEC,KAAAA;QAAKC,KAAAA;MAA0B,GAAA;AAC5C,cAAMrF,IAAQoF,EAAIC,EAAG;AACjBrF,aAAS,QAAQA,aAAiBsF,UAChCtF,EAAMuF,UAAU,IAClBH,EAAIC,EAAG,IAAIrF,EAAM,CAAC,EAAEC,MAEpBmF,EAAIC,EAAG,IAAI;MAGjB;IACD;IACD,iBAAiB;MACfzB,MAAM;QACJC,WAAW;UACTC,MAAM;UACNC,UAAU;QACX;QACD,CAAC1D,EAAG8D,SAASC,KAAK,GAAG,CACnB;UACEC,WAAWpB,EAA2B;UACtCqB,SAASd,EAAE,sCAAsC;UACjDe,SAAS;QAAA,CACV;MAEJ;MACDC,QAAQ;QACNX,WAAW;UAAEC,MAAM;QAAmB;MACvC;IACF;IACD,oBAAoB;MAClBF,MAAM;QACJC,WAAW;UACTC,MAAM;UACNI,QAAQ;UACRsB,SAAS;YAAEC,aAAa;YAAGC,cAAc;YAAGC,UAAU;UAAG;QAC1D;QACD,CAACtF,EAAG8D,SAASC,KAAK,GAAG,CACnB;UACEC,WAAWpB,EAA2B;UACtCqB,SAASd,EAAE,sCAAsC;UACjDe,SAAS;QAAA,CACV;MAEJ;MACDC,QAAQ;QACNU,OAAO;QACPrB,WAAW;UAAEC,MAAM;UAAoBW,OAAO;UAAcC,mBAAmB;QAAM;MACtF;MACDI,UAAU;QACRjB,WAAW;UAAEkB,QAAQ;UAAKC,OAAO;QAAK;MACvC;IACF;EAAA;AAEL;AC3GO,IAAMY,KAAmC;EAC9C,aAAa;EACb,KAAK;;IAEH,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,UAAU;IACV,WAAW;IACX,MAAM,iBAAiBC,GAAO;AAEtB,YAAA,IAAI,MAAM,4DAA4D;IAC9E;EACD;EACD,QAAQ;IACN,QAAQ;IACR,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,iBAAiB;IACjB,iBAAiBA,GAAiC;AAEhD,aAAO,IAAI,QAAQ,CAACC,GAASC,MAAU;AAC9BA,UAAA,IAAI,MAAM,wDAAwD,CAAC;MAAA,CAC3E;IACH;IACA,UAAU;IACV,SAAS;;;IAGR;EACF;EACD,OAAO;IACL,QAAQ;IACR,MAAM,SAASF,GAAO;AACd,YAAA,IAAI,MAAM,yEAAyE;IAC3F;IACA,QAAQ;EACT;EACD,IAAI;IACF,QAAQ;IACR,SAAS;MACP,QAAQ;MACR,gBAAgB;MAChB,UAAU;MACV,aAAa;QACX,aAAa;QACb,iBAAiB;;MAClB;IACF;EACF;EACD,MAAM;IACJ,cAAcG,GAAG;AAER,aAAAA;IACT;IACA,QAAQ;IACR,MAAM;IACN,SAAS,CAAE;IACX,MAAM,CAAE;;;;;EAKT;EACD,MAAM,SAASH,GAAO;AACd,UAAA,EAAE,UAAAI,EAAa,IAAAJ,GAEfK,IAAAA,oBAAW,KAAA,GACXC,KAAWN,EAAQ,YAAY,QAC/BO,IAAWP,EAAQ,YAAY;AACrC,QAAIQ,IAAM;AACV,WAAID,IACFC,IAAM,MAAMJ,IAERA,EAAS,YAAY,GAAG,KAAK,MAC/BI,IAAMJ,EAAS,UAAUA,EAAS,YAAY,GAAG,CAAC,IAKpDE,KACA,MACAD,EAAK,YAAA,IACL,OACCA,EAAK,SAAA,IAAa,KACnB,MACAA,EAAK,QAAA,IACL,MACA,KAAK,MAAM,KAAK,OAAW,IAAA,IAAe,IAC1CG;EAEJ;;AA5FK,IA+FMC,IAAoCC,kBAAUX,EAAa;ACzFxE,SAASY,GAAUC,GAAUC,GAAyB;AACpD/F,gBAAM2F,GAAgBI,CAAM;AAE9B;AAEA,IAAMC,KAAsB1D;AAA5B,IAGe2D,KAAA;EACb,QAAQH,GAAUI,GAA0B;AAC1C,UAAMC,IAAWC,GAAAA,GACX,EAAE,UAAAC,GAAAA,IAAaC,GAAAA;AACrBD,IAAAA,GAASF,CAAQ,GACjBN,GAAUC,GAAKI,CAAO;EACxB;;ACvBF,eAAsBK,GAAgBC,GAAY;AAChD,MAAIC,IAAc;AAClB,SAAID,MAAS,WACFC,IAAA,MAAM,OAAO,wCAAmB,IAChCD,MAAS,QACTC,IAAA,MAAM,OAAO,qCAAgB,IAC7BD,MAAS,SACTC,IAAA,MAAM,OAAO,sCAAiB,IAC9BD,MAAS,UACTC,IAAA,MAAM,OAAO,uCAAkB,IAC/BD,MAAS,OACTC,IAAA,MAAM,OAAO,oCAAe,IAE7B,QAAA,MAAM,MAAMD,CAAI,OAAO,GAE1BC;AACT;ACZsB,eAAAC,IAASC,GAAYrB,GAAkBS,GAA6B;AACxF,SAAOA,EAAO,SAAS;IACrB,UAAAT;IACA,MAAAqB;IACA,GAAGZ;EAAA,CACJ;AACH;SACgBa,KAAW;AACzB,WAASC,IAAc;AACrB,UAAMd,KAASJ;AACf,WAAOI,MAAA,OAAA,SAAAA,GAAQ;EACjB;AACA,WAASe,EAAUN,IAAY;AACzBA,IAAAA,MAAQ,SACVA,KAAOK,EAAc;AAEvB,UAAM5B,IAAgBU,GAEhBI,IAASJ,EAAea,EAAI;AAC9B,WAAAT,EAAO,YAAY,SACrBA,EAAO,WAAWd,EAAc,WAE3Bc;EACT;AAEA,iBAAegB,EAAgBP,IAAY;AACzC,WAAO,MAAMD,GAA4BC,MAASK,EAA2B,CAAA;EAC/E;AAEO,SAAA;IACL,WAAAC;IACA,gBAAAD;IACA,iBAAAE;EAAA;AAEJ;AC/BA,IAAMC,KAAAA,OAAAA,OAAAA,EAAAA,wCAAAA,MAAAA,OAAAA,4CAAAA,GAAAA,+BAAAA,MAAAA,OAAAA,mCAAAA,GAAAA,qCAAAA,MAAAA,OAAAA,yCAAAA,GAAAA,oCAAAA,MAAAA,OAAAA,wCAAAA,GAAAA,qCAAAA,MAAAA,QAAAA,QAAAA,EAAAA,KAAAA,MAAAA,CAAAA,GAAAA,gCAAAA,MAAAA,OAAAA,oCAAAA,EAAAA,CAAAA;AAAN,IAEMC,KAAAA,OAAAA,OAAAA,EAAAA,qCAAAA,EAAAA,CAAAA;AAFN,IAMM,EAAE,2BAAAC,GAAyB,IAAKC,GAAAA;AAItCD,GAA0B;EACxB,MAAM;EACN,OAAO;EACP,QAAQ,CAACE,IAAsC,CAAA,GAAIC,IAA2B,CAAA,MAAM;AAC9E,QAAA,OAAOD,EAAY,QAAS,YAAYA,EAAY,KAAK,SAAS,UAAU,GAAG;AACjF,YAAME,IAAWF,EAAY,UACvBG,KAAYH,EAAY;AAC9BpH,oBAAMoH,GAAa;QACjB,MAAM;UACJ,WAAW;YACT,UAAAE;YACA,WAAAC;UACD;QACF;QACD,QAAQ;UACN,WAAW;YACT,UAAAD;YACA,WAAAC;UACD;QACF;MAAA,CACF;IACF;AACM,WAAAH;EACT;AACD,CAAA;AAED,IAAMI,KAAuB;EAC3B,QAAQ1B,GAAQ;AAER2B,OAAA,KAAK,uBAAuB3B,GAAKkB,IAAc,CAAC,gBAAgB,GAAG,MAAM,IAAI,GACnFS,GAAM,KAAK,sBAAsB3B,GAAKmB,IAAa,MAAM,MAAM,IAAI;EACrE;;AALF,IAQaS,KAAoB;EAC/B,QAAQ5B,GAAUI,GAA0B;AACtCJ,MAAA,IAAIG,IAAgBC,CAAO,GAC/BJ,EAAI,IAAI0B,EAAoB;EAC9B;;ACvDY,SAAAG,KAAA;AACL,SAAA;IACL,eAAe;MACb,MAAM,EAAE,WAAW,EAAE,MAAM,iBAAA,EAAoB;IAChD;IACD,gBAAgB;MACd,MAAM,EAAE,WAAW,EAAE,MAAM,mBAAmB,OAAO,EAAE,QAAQ,IAAA,GAAO,cAAc,EAAE,OAAO,EAAE,QAAQ,IAAK,EAAA,EAAA,EAAM;IACnH;IACD,eAAe;MACb,MAAM,EAAE,WAAW,EAAE,MAAM,iBAAkB,GAAE,KAAK,EAAE,MAAM,GAAA,EAAM;IACnE;EAAA;AAEL;ACLO,IAAM1C,KAAgB;EAC3B,YAAY,CAAE;EACd,aAAa;IACX,cAAc,CAAE;IAChB,eAAe,CAAE;EAClB;;ACPH,SAASY,GAAUC,GAAUC,GAAsB;AACjDD,IAAI,OAAO,iBAAiB,oBAAoB9F,cAAMiF,IAAec,CAAM;AAC7E;AAEA,IAAe6B,KAAA;EACb,QAAQ9B,GAAUI,GAAuB;AACvC,UAAMC,IAAWC,GAAAA,GACX,EAAE,UAAAC,GAAAA,IAAaC,GAAAA;AACrBD,IAAAA,GAASF,CAAQ,GACjBN,GAAUC,GAAKI,CAAO;EACxB;;AANF,ICTM2B,KAAe,CAAA;AAOL,SAAAC,GAAe3E,GAAc4E,GAAW;AACtDF,KAAa1E,CAAI,IAAI4E;AACvB;AAEA,eAAsBC,KAAW;AAC/B,MAAI,OAAO;AACT;AAGI,QAAAC,IAAe,MAAM,OAAO,sCAAkD,GAC9EC,IAAa,MAAM,OAAO,oCAAuD,GACjFC,IAAY,MAAM,OAAO,mCAAqD,GAC9EC,KAAa,MAAM,OAAO,oCAAuD,GACjFC,IAAW,MAAM,OAAO,kCAA2D,GACnFC,IAAa,MAAM,OAAO,oCAAsB;AAStD,SAAO,oBAAoB;;IAEzB,UAAUC,GAAGC,GAAK;AACV,YAAAC,IAASZ,GAAaW,CAAK;AACjC,aAAIC,IACK,IAAIA,EAAM,IAEfD,MAAU,SACL,IAAIN,EAAW,QAAA,IACbM,MAAU,SAASA,MAAU,UAAUA,MAAU,SACnD,IAAIL,EAAU,QAAA,IACZK,MAAU,UAAUA,MAAU,gBAAgBA,MAAU,UAC1D,IAAIJ,GAAW,QAAA,IACbI,MAAU,gBAAgBA,MAAU,eACtC,IAAIH,EAAS,QAAA,IACXG,MAAU,UAAUA,MAAU,QAEhC,IAAIF,EAAW,QAAA,IAEjB,IAAIL,EAAa,QAAA;IAC1B;EAAA;AAEJ;ACpDA,IAAMS,KAAW;EACf,WAAW,OAAOrG,GAAWhD,MAAc;AAEzC,QAAIA;AACE,UAAA;AACF,aAAK,MAAMA,CAAK;MAAA,SACTsJ,GAAQ;AACf,cAAA,QAAQ,MAAMA,CAAC,GACT,IAAI,MAAM,cAAcA,EAAE,OAAO;MACxC;EAEL;EACA,SAAS;;AAZX,IAeMC,KAAW;EACf,WAAW,OAAOvG,GAAWhD,MAAc;AAEzC,QAAIA;AACE,UAAA;AAEI,cAAAwJ,IAAO,MAAM,OAAO,uBAAS;AACnCA,UAAK,KAAKxJ,GAAO,EAAE,QAAQwJ,EAAK,YAAA,CAAa;MAAA,SACtCF,GAAQ;AACf,cAAA,QAAQ,MAAMA,CAAC,GACT,IAAI,MAAM,cAAcA,EAAE,OAAO;MACxC;EAEL;EACA,SAAS;;AA7BX,IAgCaG,KAAyB;EACpC,UAAAJ;EACA,UAAAE;;AAlCF,ICKM5B,KAAAA,OAAAA,OAAAA,EAAAA,yCAAAA,MAAAA,OAAAA,8BAAAA,GAAAA,yCAAAA,MAAAA,OAAAA,8BAAAA,GAAAA,0CAAAA,MAAAA,OAAAA,8BAAAA,EAAAA,CAAAA;ADLN,ICMM+B,KAAsB;EAC1B,QAAQjD,GAAQ;AACd2B,OAAM,KAAK,uBAAuB3B,GAAKkB,IAAc,MAAM,0BAA0B,IAAI;EAC3F;;ADTF,ICYagC,KAAkB;EAC7B,QAAQlD,GAAUI,GAAuB;AACnCJ,MAAA,IAAI8B,IAAa1B,CAAO,GAC5BJ,EAAI,IAAIiD,EAAmB;EAC7B;;AChBY,SAAAE,KAAA;AACL,SAAA;IACL,MAAM;MACJ,MAAM;QACJ,WAAW;UACT,MAAM;QACP;MACF;IACF;EAAA;AAEL;ACPA,IAAeC,KAAA;EACb,QAAQpD,GAAQ;AACd,UAAMK,IAAWC,GAAAA,GACX,EAAE,UAAAC,EAAAA,IAAaC,GAAAA;AACrBD,MAASF,CAAQ;EACnB;;AALF,ICCMa,KAAAA,OAAAA,OAAAA,EAAAA,mCAAAA,MAAAA,OAAAA,uCAAAA,EAAAA,CAAAA;ADDN,ICEM+B,KAAsB;EAC1B,QAAQjD,GAAQ;AAEd2B,OAAM,KAAK,uBAAuB3B,GAAKkB,IAAc,CAAA,GAAI,MAAM,IAAI;EACrE;;ADNF,ICSamC,KAAgB;EAC3B,QAAQrD,GAAQ;AACdA,MAAI,IAAIsD,EAAa,GACrBtD,EAAI,IAAIiD,EAAmB;EAC7B;;AChBY,SAAAM,KAAA;AACL,SAAA;IACL,UAAU;MACR,QAAQ;QACN,WAAW;UACT,MAAM;UACN,QAAQ;QACT;MACF;IACF;EAAA;AAEL;ACPA,IAAeC,KAAA;EACb,QAAQxD,GAAQ;AACd,UAAMK,IAAWC,GAAAA,GACX,EAAE,UAAAC,EAAAA,IAAaC,GAAAA;AACrBD,MAASF,CAAQ;EACnB;;ACJF,IAAIoD,KAAiB,OAAO,aAAe,MAAc,aAAa,OAAO,SAAW,MAAc,SAAS,OAAO,SAAW,MAAc,SAAS,OAAO,OAAS,MAAc,OAAO,CAAA;AAE7L,SAASC,GAAyBC,GAAG;AACpC,SAAOA,KAAKA,EAAE,cAAc,OAAO,UAAU,eAAe,KAAKA,GAAG,SAAS,IAAIA,EAAE,UAAaA;AACjG;AAEA,SAASC,GAAqBC,GAAIC,GAASnD,GAAQ;AAClD,SAAOA,IAAS;IACf,MAAMmD;IACN,SAAS,CAAE;IACX,SAAS,SAAUC,IAAMC,GAAM;AAC9B,aAAOC,GAAgBF,IAA6BC,KAAiBrD,EAAO,IAAW;IACvF;EACH,GAAIkD,EAAGlD,GAAQA,EAAO,OAAO,GAAGA,EAAO;AACvC;AAEA,SAASsD,KAAmB;AAC3B,QAAM,IAAI,MAAM,yEAAyE;AAC1F;AAEA,IAAIC,KAAYN,GAAqB,SAAUjD,GAAQwD,GAAS;AAOhE,GAAC,SAA0CC,IAAMC,GAAS;AACzD1D,MAAO,UAAU0D,EAAAA;EAClB,GAAGZ,IAAgB,WAAW;AAC9B;;MAAiB,SAASa,GAAS;AAEzB,YAAIC,KAAmB,CAAA;AAGvB,iBAASC,EAAoBC,GAAU;AAGtC,cAAGF,GAAiBE,CAAQ;AAC3B,mBAAOF,GAAiBE,CAAQ,EAAE;AAGnC,cAAI9D,IAAS4D,GAAiBE,CAAQ,IAAI;;YACzC,GAAGA;;YACH,GAAG;;YACH,SAAS,CAAE;;UACvB;AAGW,iBAAAH,EAAQG,CAAQ,EAAE,KAAK9D,EAAO,SAASA,GAAQA,EAAO,SAAS6D,CAAmB,GAGlF7D,EAAO,IAAI,MAGJA,EAAO;QACd;AAID,eAAA6D,EAAoB,IAAIF,GAGxBE,EAAoB,IAAID,IAGxBC,EAAoB,IAAI,SAASL,GAAS9G,GAAMqH,GAAQ;AACnDF,YAAoB,EAAEL,GAAS9G,CAAI,KACtC,OAAO,eAAe8G,GAAS9G,GAAM,EAAE,YAAY,MAAM,KAAKqH,EAAM,CAAE;QAElF,GAGUF,EAAoB,IAAI,SAASL,GAAS;AACtC,iBAAO,SAAW,OAAe,OAAO,eAC1C,OAAO,eAAeA,GAAS,OAAO,aAAa,EAAE,OAAO,SAAQ,CAAE,GAEvE,OAAO,eAAeA,GAAS,cAAc,EAAE,OAAO,KAAI,CAAE;QACvE,GAOUK,EAAoB,IAAI,SAASjL,GAAOoL,GAAM;AAG7C,cAFGA,IAAO,MAAGpL,IAAQiL,EAAoBjL,CAAK,IAC3CoL,IAAO,KACNA,IAAO,KAAM,OAAOpL,KAAU,YAAYA,KAASA,EAAM;AAAY,mBAAOA;AAChF,cAAIqL,IAAK,uBAAO,OAAO,IAAI;AAG3B,cAFAJ,EAAoB,EAAEI,CAAE,GACxB,OAAO,eAAeA,GAAI,WAAW,EAAE,YAAY,MAAM,OAAOrL,EAAK,CAAE,GACpEoL,IAAO,KAAK,OAAOpL,KAAS;AAAU,qBAAQqF,KAAOrF;AAAOiL,gBAAoB,EAAEI,GAAIhG,IAAK,SAASA,GAAK;AAAE,uBAAOrF,EAAMqF,CAAG;cAAI,GAAC,KAAK,MAAMA,CAAG,CAAC;AAClJ,iBAAOgG;QAClB,GAGUJ,EAAoB,IAAI,SAAS7D,GAAQ;AACxC,cAAI+D,IAAS/D,KAAUA,EAAO;;YAC7B,WAAsB;AAAE,qBAAOA,EAAO;YAAa;;;YACnD,WAA4B;AAAE,qBAAOA;YAAA;;AACtC,iBAAA6D,EAAoB,EAAEE,GAAQ,KAAKA,CAAM,GAClCA;QAClB,GAGUF,EAAoB,IAAI,SAASK,GAAQC,GAAU;AAAE,iBAAO,OAAO,UAAU,eAAe,KAAKD,GAAQC,CAAQ;QAAE,GAGnHN,EAAoB,IAAI,IAIjBA,EAAoBA,EAAoB,IAAI,CAAC;MACpD,EAEA;;;QAEH,SAAS7D,GAAQwD,IAAS;AAEjC,mBAASY,EAAOC,GAAS;AACrB,gBAAIC;AAEJ,gBAAID,EAAQ,aAAa;AACrBA,gBAAQ,MAAK,GAEbC,IAAeD,EAAQ;qBAElBA,EAAQ,aAAa,WAAWA,EAAQ,aAAa,YAAY;AACtE,kBAAIE,IAAaF,EAAQ,aAAa,UAAU;AAE3CE,mBACDF,EAAQ,aAAa,YAAY,EAAE,GAGvCA,EAAQ,OAAM,GACdA,EAAQ,kBAAkB,GAAGA,EAAQ,MAAM,MAAM,GAE5CE,KACDF,EAAQ,gBAAgB,UAAU,GAGtCC,IAAeD,EAAQ;YAC1B,OACI;AACGA,gBAAQ,aAAa,iBAAiB,KACtCA,EAAQ,MAAK;AAGjB,kBAAIG,IAAY,OAAO,aAAA,GACnBC,IAAQ,SAAS,YAAA;AAErBA,gBAAM,mBAAmBJ,CAAO,GAChCG,EAAU,gBAAe,GACzBA,EAAU,SAASC,CAAK,GAExBH,IAAeE,EAAU,SAAA;YAC5B;AAED,mBAAOF;UACX;AAEAtE,YAAO,UAAUoE;QAGV;;;QAEA,SAASpE,GAAQwD,IAAS;AAEjC,mBAASkB,IAAK;UAGd;AAEAA,YAAE,YAAY;YACZ,IAAI,SAAUhI,GAAMiI,GAAU3L,GAAK;AACjC,kBAAIkJ,IAAI,KAAK,MAAM,KAAK,IAAI,CAAA;AAE5B,sBAACA,EAAExF,CAAI,MAAMwF,EAAExF,CAAI,IAAI,CAAA,IAAK,KAAK;gBAC/B,IAAIiI;gBACJ,KAAK3L;cACX,CAAK,GAEM;YACR;YAED,MAAM,SAAU0D,GAAMiI,GAAU3L,GAAK;AACnC,kBAAI4L,IAAO;AACX,uBAASC,IAAY;AACnBD,kBAAK,IAAIlI,GAAMmI,CAAQ,GACvBF,EAAS,MAAM3L,GAAK,SAAS;cAC9B;AACD,qBAAA6L,EAAS,IAAIF,GACN,KAAK,GAAGjI,GAAMmI,GAAU7L,CAAG;YACnC;YAED,MAAM,SAAU0D,GAAM;AACpB,kBAAIoI,IAAO,CAAA,EAAG,MAAM,KAAK,WAAW,CAAC,GACjCC,MAAW,KAAK,MAAM,KAAK,IAAI,CAAA,IAAKrI,CAAI,KAAK,CAAE,GAAE,MAAK,GACtD5C,IAAI,GACJkL,IAAMD,EAAO;AAEjB,mBAAKjL,GAAGA,IAAIkL,GAAKlL;AACfiL,kBAAOjL,CAAC,EAAE,GAAG,MAAMiL,EAAOjL,CAAC,EAAE,KAAKgL,CAAI;AAGxC,qBAAO;YACR;YAED,KAAK,SAAUpI,GAAMiI,GAAU;AAC7B,kBAAIzC,IAAI,KAAK,MAAM,KAAK,IAAI,CAAA,IACxB+C,IAAO/C,EAAExF,CAAI,GACbwI,IAAa,CAAA;AAEjB,kBAAID,KAAQN;AACV,yBAAS7K,IAAI,GAAGkL,IAAMC,EAAK,QAAQnL,IAAIkL,GAAKlL;AACtCmL,oBAAKnL,CAAC,EAAE,OAAO6K,KAAYM,EAAKnL,CAAC,EAAE,GAAG,MAAM6K,KAC9CO,EAAW,KAAKD,EAAKnL,CAAC,CAAC;AAQ7B,qBAACoL,EAAW,SACRhD,EAAExF,CAAI,IAAIwI,IACV,OAAOhD,EAAExF,CAAI,GAEV;YACR;UACH,GAEAsD,EAAO,UAAU0E,GACjB1E,EAAO,QAAQ,cAAc0E;QAGtB;;;QAEA,SAAS1E,GAAQwD,IAASK,GAAqB;AAEtD,cAAIsB,IAAKtB,EAAoB,CAAC,GAC1BuB,IAAWvB,EAAoB,CAAC;AAWpC,mBAASwB,EAAOC,GAAQvF,GAAM4E,GAAU;AACpC,gBAAI,CAACW,KAAU,CAACvF,KAAQ,CAAC4E;AACrB,oBAAM,IAAI,MAAM,4BAA4B;AAGhD,gBAAI,CAACQ,EAAG,OAAOpF,CAAI;AACf,oBAAM,IAAI,UAAU,kCAAkC;AAG1D,gBAAI,CAACoF,EAAG,GAAGR,CAAQ;AACf,oBAAM,IAAI,UAAU,mCAAmC;AAG3D,gBAAIQ,EAAG,KAAKG,CAAM;AACd,qBAAOC,EAAWD,GAAQvF,GAAM4E,CAAQ;AAEvC,gBAAIQ,EAAG,SAASG,CAAM;AACvB,qBAAOE,EAAeF,GAAQvF,GAAM4E,CAAQ;AAE3C,gBAAIQ,EAAG,OAAOG,CAAM;AACrB,qBAAOG,EAAeH,GAAQvF,GAAM4E,CAAQ;AAG5C,kBAAM,IAAI,UAAU,2EAA2E;UAEvG;AAWA,mBAASY,EAAWG,GAAM3F,GAAM4E,GAAU;AACtC,mBAAAe,EAAK,iBAAiB3F,GAAM4E,CAAQ,GAE7B;cACH,SAAS,WAAW;AAChBe,kBAAK,oBAAoB3F,GAAM4E,CAAQ;cAC1C;YACJ;UACL;AAWA,mBAASa,EAAeG,GAAU5F,GAAM4E,GAAU;AAC9C,mBAAA,MAAM,UAAU,QAAQ,KAAKgB,GAAU,SAASD,GAAM;AAClDA,gBAAK,iBAAiB3F,GAAM4E,CAAQ;YAC5C,CAAK,GAEM;cACH,SAAS,WAAW;AAChB,sBAAM,UAAU,QAAQ,KAAKgB,GAAU,SAASD,GAAM;AAClDA,oBAAK,oBAAoB3F,GAAM4E,CAAQ;gBACvD,CAAa;cACJ;YACJ;UACL;AAWA,mBAASc,EAAeG,GAAU7F,GAAM4E,GAAU;AAC9C,mBAAOS,EAAS,SAAS,MAAMQ,GAAU7F,GAAM4E,CAAQ;UAC3D;AAEA3E,YAAO,UAAUqF;QAGV;;;QAEA,SAASrF,GAAQwD,IAAS;AAQjCA,UAAAA,GAAQ,OAAO,SAAS5K,GAAO;AAC3B,mBAAOA,MAAU,UACVA,aAAiB,eACjBA,EAAM,aAAa;UAC9B,GAQA4K,GAAQ,WAAW,SAAS5K,GAAO;AAC/B,gBAAImH,IAAO,OAAO,UAAU,SAAS,KAAKnH,CAAK;AAE/C,mBAAOA,MAAU,WACTmH,MAAS,uBAAuBA,MAAS,8BACzC,YAAYnH,MACZA,EAAM,WAAW,KAAK4K,GAAQ,KAAK5K,EAAM,CAAC,CAAC;UACvD,GAQA4K,GAAQ,SAAS,SAAS5K,GAAO;AAC7B,mBAAO,OAAOA,KAAU,YACjBA,aAAiB;UAC5B,GAQA4K,GAAQ,KAAK,SAAS5K,GAAO;AACzB,gBAAImH,IAAO,OAAO,UAAU,SAAS,KAAKnH,CAAK;AAE/C,mBAAOmH,MAAS;UACpB;QAGO;;;QAEA,SAASC,GAAQwD,IAASK,GAAqB;AAEtD,cAAIgC,IAAUhC,EAAoB,CAAC;AAYnC,mBAASiC,EAAUzB,GAASuB,GAAU7F,GAAM4E,GAAUoB,GAAY;AAC9D,gBAAIC,IAAanB,EAAS,MAAM,MAAM,SAAS;AAE/C,mBAAAR,EAAQ,iBAAiBtE,GAAMiG,GAAYD,CAAU,GAE9C;cACH,SAAS,WAAW;AAChB1B,kBAAQ,oBAAoBtE,GAAMiG,GAAYD,CAAU;cAC3D;YACJ;UACL;AAYA,mBAASX,EAASa,GAAUL,GAAU7F,GAAM4E,GAAUoB,GAAY;AAE9D,mBAAI,OAAOE,EAAS,oBAAqB,aAC9BH,EAAU,MAAM,MAAM,SAAS,IAItC,OAAO/F,KAAS,aAGT+F,EAAU,KAAK,MAAM,QAAQ,EAAE,MAAM,MAAM,SAAS,KAI3D,OAAOG,KAAa,aACpBA,IAAW,SAAS,iBAAiBA,CAAQ,IAI1C,MAAM,UAAU,IAAI,KAAKA,GAAU,SAAU5B,GAAS;AACzD,qBAAOyB,EAAUzB,GAASuB,GAAU7F,GAAM4E,GAAUoB,CAAU;YACtE,CAAK;UACL;AAWA,mBAASlB,EAASR,GAASuB,GAAU7F,GAAM4E,GAAU;AACjD,mBAAO,SAASzC,GAAG;AACfA,gBAAE,iBAAiB2D,EAAQ3D,EAAE,QAAQ0D,CAAQ,GAEzC1D,EAAE,kBACFyC,EAAS,KAAKN,GAASnC,CAAC;YAE/B;UACL;AAEAlC,YAAO,UAAUoF;QAGV;;;QAEA,SAASpF,GAAQwD,IAAS;AAEjC,cAAI0C,IAAqB;AAKzB,cAAI,OAAO,UAAY,OAAe,CAAC,QAAQ,UAAU,SAAS;AAC9D,gBAAIC,IAAQ,QAAQ;AAEpBA,cAAM,UAAUA,EAAM,mBACNA,EAAM,sBACNA,EAAM,qBACNA,EAAM,oBACNA,EAAM;UAC1B;AASA,mBAASN,EAASxB,GAASuB,GAAU;AACjC,mBAAOvB,KAAWA,EAAQ,aAAa6B,KAAoB;AACvD,kBAAI,OAAO7B,EAAQ,WAAY,cAC3BA,EAAQ,QAAQuB,CAAQ;AAC1B,uBAAOvB;AAETA,kBAAUA,EAAQ;YACrB;UACL;AAEArE,YAAO,UAAU6F;QAGV;;;QAEA,SAAS7F,GAAQoG,IAAqBvC,GAAqB;AAClEA,YAAoB,EAAEuC,EAAmB;AAGzC,cAAIC,IAAaxC,EAAoB,CAAC,GAClCyC,IAA8BzC,EAAoB,EAAEwC,CAAU,GAG9DE,IAAU,OAAO,UAAW,cAAc,OAAO,OAAO,YAAa,WAAW,SAAUC,GAAK;AAAE,mBAAO,OAAOA;UAAI,IAAK,SAAUA,GAAK;AAAE,mBAAOA,KAAO,OAAO,UAAW,cAAcA,EAAI,gBAAgB,UAAUA,MAAQ,OAAO,YAAY,WAAW,OAAOA;UAAI,GAEtQC,IAAe,WAAY;AAAE,qBAASC,EAAiBpB,GAAQvM,GAAO;AAAE,uBAASe,IAAI,GAAGA,IAAIf,EAAM,QAAQe,KAAK;AAAE,oBAAI6M,IAAa5N,EAAMe,CAAC;AAAG6M,kBAAW,aAAaA,EAAW,cAAc,OAAOA,EAAW,eAAe,MAAU,WAAWA,MAAYA,EAAW,WAAW,OAAM,OAAO,eAAerB,GAAQqB,EAAW,KAAKA,CAAU;cAAE;YAAI;AAAC,mBAAO,SAAUC,GAAaC,GAAYC,GAAa;AAAE,qBAAID,KAAYH,EAAiBE,EAAY,WAAWC,CAAU,GAAOC,KAAaJ,EAAiBE,GAAaE,CAAW,GAAUF;YAAc;UAAG,EAAA;AAEjjB,mBAASG,EAAgBC,GAAUJ,GAAa;AAAE,gBAAI,EAAEI,aAAoBJ;AAAgB,oBAAM,IAAI,UAAU,mCAAmC;UAAM;AASzJ,cAAIK,IAAmC,WAAY;AAI/C,qBAASC,EAAgBzH,GAAS;AAC9BsH,gBAAgB,MAAMG,CAAe,GAErC,KAAK,eAAezH,CAAO,GAC3B,KAAK,cAAa;YACrB;AAQD,mBAAAgH,EAAaS,GAAiB,CAAC;cAC3B,KAAK;cACL,OAAO,WAA0B;AAC7B,oBAAIzH,IAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA;AAElF,qBAAK,SAASA,EAAQ,QACtB,KAAK,YAAYA,EAAQ,WACzB,KAAK,UAAUA,EAAQ,SACvB,KAAK,SAASA,EAAQ,QACtB,KAAK,OAAOA,EAAQ,MACpB,KAAK,UAAUA,EAAQ,SAEvB,KAAK,eAAe;cACvB;;;;;YAOT,GAAO;cACC,KAAK;cACL,OAAO,WAAyB;AACxB,qBAAK,OACL,KAAK,WAAU,IACR,KAAK,UACZ,KAAK,aAAY;cAExB;;;;;YAOT,GAAO;cACC,KAAK;cACL,OAAO,WAAsB;AACzB,oBAAI0H,IAAQ,MAERC,IAAQ,SAAS,gBAAgB,aAAa,KAAK,KAAK;AAE5D,qBAAK,WAAU,GAEf,KAAK,sBAAsB,WAAY;AACnC,yBAAOD,EAAM,WAAA;gBAC7B,GACY,KAAK,cAAc,KAAK,UAAU,iBAAiB,SAAS,KAAK,mBAAmB,KAAK,MAEzF,KAAK,WAAW,SAAS,cAAc,UAAU,GAEjD,KAAK,SAAS,MAAM,WAAW,QAE/B,KAAK,SAAS,MAAM,SAAS,KAC7B,KAAK,SAAS,MAAM,UAAU,KAC9B,KAAK,SAAS,MAAM,SAAS,KAE7B,KAAK,SAAS,MAAM,WAAW,YAC/B,KAAK,SAAS,MAAMC,IAAQ,UAAU,MAAM,IAAI;AAEhD,oBAAIC,IAAY,OAAO,eAAe,SAAS,gBAAgB;AAC/D,qBAAK,SAAS,MAAM,MAAMA,IAAY,MAEtC,KAAK,SAAS,aAAa,YAAY,EAAE,GACzC,KAAK,SAAS,QAAQ,KAAK,MAE3B,KAAK,UAAU,YAAY,KAAK,QAAQ,GAExC,KAAK,eAAef,EAAgB,EAAC,KAAK,QAAQ,GAClD,KAAK,SAAQ;cAChB;;;;;YAOT,GAAO;cACC,KAAK;cACL,OAAO,WAAsB;AACrB,qBAAK,gBACL,KAAK,UAAU,oBAAoB,SAAS,KAAK,mBAAmB,GACpE,KAAK,cAAc,MACnB,KAAK,sBAAsB,OAG3B,KAAK,aACL,KAAK,UAAU,YAAY,KAAK,QAAQ,GACxC,KAAK,WAAW;cAEvB;;;;YAMT,GAAO;cACC,KAAK;cACL,OAAO,WAAwB;AAC3B,qBAAK,eAAeA,EAAgB,EAAC,KAAK,MAAM,GAChD,KAAK,SAAQ;cAChB;;;;YAMT,GAAO;cACC,KAAK;cACL,OAAO,WAAoB;AACvB,oBAAIgB,IAAY;AAEhB,oBAAI;AACAA,sBAAY,SAAS,YAAY,KAAK,MAAM;gBAC/C,QAAa;AACVA,sBAAY;gBACf;AAED,qBAAK,aAAaA,CAAS;cAC9B;;;;;YAOT,GAAO;cACC,KAAK;cACL,OAAO,SAAsBA,GAAW;AACpC,qBAAK,QAAQ,KAAKA,IAAY,YAAY,SAAS;kBAC/C,QAAQ,KAAK;kBACb,MAAM,KAAK;kBACX,SAAS,KAAK;kBACd,gBAAgB,KAAK,eAAe,KAAK,IAAI;gBAC7D,CAAa;cACJ;;;;YAMT,GAAO;cACC,KAAK;cACL,OAAO,WAA0B;AACzB,qBAAK,WACL,KAAK,QAAQ,MAAA,GAEjB,SAAS,cAAc,KAAA,GACvB,OAAO,aAAA,EAAe,gBAAA;cACzB;;;;;YAOT,GAAO;cACC,KAAK;;;;cAML,OAAO,WAAmB;AACtB,qBAAK,WAAU;cAClB;YACT,GAAO;cACC,KAAK;cACL,KAAK,WAAe;AAChB,oBAAIC,IAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAIjF,oBAFA,KAAK,UAAUA,GAEX,KAAK,YAAY,UAAU,KAAK,YAAY;AAC5C,wBAAM,IAAI,MAAM,oDAAoD;cAE3E;cAOD,KAAK,WAAe;AAChB,uBAAO,KAAK;cACf;;;;;;YAQT,GAAO;cACC,KAAK;cACL,KAAK,SAAajC,GAAQ;AACtB,oBAAIA,MAAW;AACX,sBAAIA,MAAW,OAAOA,IAAW,MAAc,cAAciB,EAAQjB,CAAM,OAAO,YAAYA,EAAO,aAAa,GAAG;AACjH,wBAAI,KAAK,WAAW,UAAUA,EAAO,aAAa,UAAU;AACxD,4BAAM,IAAI,MAAM,mFAAmF;AAGvG,wBAAI,KAAK,WAAW,UAAUA,EAAO,aAAa,UAAU,KAAKA,EAAO,aAAa,UAAU;AAC3F,4BAAM,IAAI,MAAM,uGAAwG;AAG5H,yBAAK,UAAUA;kBACnC;AACoB,0BAAM,IAAI,MAAM,6CAA6C;cAGxE;cAOD,KAAK,WAAe;AAChB,uBAAO,KAAK;cACf;YACJ,CAAA,CAAC,GAEK4B;UACX,EAAA,GAEiCM,IAAoBP,GAEjDQ,IAAe5D,EAAoB,CAAC,GACpC6D,IAAoC7D,EAAoB,EAAE4D,CAAY,GAGtEpC,IAASxB,EAAoB,CAAC,GAC9B8D,IAA8B9D,EAAoB,EAAEwB,CAAM,GAG1DuC,IAAmB,OAAO,UAAW,cAAc,OAAO,OAAO,YAAa,WAAW,SAAUpB,GAAK;AAAE,mBAAO,OAAOA;UAAI,IAAK,SAAUA,GAAK;AAAE,mBAAOA,KAAO,OAAO,UAAW,cAAcA,EAAI,gBAAgB,UAAUA,MAAQ,OAAO,YAAY,WAAW,OAAOA;UAAI,GAE/QqB,IAAwB,WAAY;AAAE,qBAASnB,EAAiBpB,GAAQvM,GAAO;AAAE,uBAASe,IAAI,GAAGA,IAAIf,EAAM,QAAQe,KAAK;AAAE,oBAAI6M,IAAa5N,EAAMe,CAAC;AAAG6M,kBAAW,aAAaA,EAAW,cAAc,OAAOA,EAAW,eAAe,MAAU,WAAWA,MAAYA,EAAW,WAAW,OAAM,OAAO,eAAerB,GAAQqB,EAAW,KAAKA,CAAU;cAAE;YAAI;AAAC,mBAAO,SAAUC,GAAaC,GAAYC,GAAa;AAAE,qBAAID,KAAYH,EAAiBE,EAAY,WAAWC,CAAU,GAAOC,KAAaJ,EAAiBE,GAAaE,CAAW,GAAUF;YAAc;UAAG,EAAA;AAE1jB,mBAASkB,EAAyBd,GAAUJ,GAAa;AAAE,gBAAI,EAAEI,aAAoBJ;AAAgB,oBAAM,IAAI,UAAU,mCAAmC;UAAM;AAElK,mBAASmB,EAA2BnD,GAAMoD,GAAM;AAAE,gBAAI,CAACpD;AAAQ,oBAAM,IAAI,eAAe,2DAA2D;AAAK,mBAAOoD,MAAS,OAAOA,KAAS,YAAY,OAAOA,KAAS,cAAcA,IAAOpD;UAAO;AAEhP,mBAASqD,EAAUC,GAAUC,GAAY;AAAE,gBAAI,OAAOA,KAAe,cAAcA,MAAe;AAAQ,oBAAM,IAAI,UAAU,6DAA6D,OAAOA,CAAU;AAAKD,cAAS,YAAY,OAAO,OAAOC,KAAcA,EAAW,WAAW,EAAE,aAAa,EAAE,OAAOD,GAAU,YAAY,OAAO,UAAU,MAAM,cAAc,KAAM,EAAA,CAAE,GAAOC,MAAY,OAAO,iBAAiB,OAAO,eAAeD,GAAUC,CAAU,IAAID,EAAS,YAAYC;UAAa;AAW9e,cAAIC,IAAsB,SAAUC,GAAU;AAC1CJ,cAAUK,GAAWD,CAAQ;AAM7B,qBAASC,EAAUnL,GAASsC,GAAS;AACjCqI,gBAAyB,MAAMQ,CAAS;AAExC,kBAAInB,IAAQY,EAA2B,OAAOO,EAAU,aAAa,OAAO,eAAeA,CAAS,GAAG,KAAK,IAAI,CAAC;AAEjH,qBAAAnB,EAAM,eAAe1H,CAAO,GAC5B0H,EAAM,YAAYhK,CAAO,GAClBgK;YACV;AASD,mBAAAU,EAAsBS,GAAW,CAAC;cAC9B,KAAK;cACL,OAAO,WAA0B;AAC7B,oBAAI7I,IAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAA;AAElF,qBAAK,SAAS,OAAOA,EAAQ,UAAW,aAAaA,EAAQ,SAAS,KAAK,eAC3E,KAAK,SAAS,OAAOA,EAAQ,UAAW,aAAaA,EAAQ,SAAS,KAAK,eAC3E,KAAK,OAAO,OAAOA,EAAQ,QAAS,aAAaA,EAAQ,OAAO,KAAK,aACrE,KAAK,YAAYmI,EAAiBnI,EAAQ,SAAS,MAAM,WAAWA,EAAQ,YAAY,SAAS;cACpG;;;;;YAOT,GAAO;cACC,KAAK;cACL,OAAO,SAAqBtC,GAAS;AACjC,oBAAIoL,IAAS;AAEb,qBAAK,WAAWZ,EAAc,EAAGxK,GAAS,SAAS,SAAU+E,GAAG;AAC5D,yBAAOqG,EAAO,QAAQrG,CAAC;gBACvC,CAAa;cACJ;;;;;YAOT,GAAO;cACC,KAAK;cACL,OAAO,SAAiBA,GAAG;AACvB,oBAAI/E,IAAU+E,EAAE,kBAAkBA,EAAE;AAEhC,qBAAK,oBACL,KAAK,kBAAkB,OAG3B,KAAK,kBAAkB,IAAIsF,EAAiB;kBACxC,QAAQ,KAAK,OAAOrK,CAAO;kBAC3B,QAAQ,KAAK,OAAOA,CAAO;kBAC3B,MAAM,KAAK,KAAKA,CAAO;kBACvB,WAAW,KAAK;kBAChB,SAASA;kBACT,SAAS;gBACzB,CAAa;cACJ;;;;;YAOT,GAAO;cACC,KAAK;cACL,OAAO,SAAuBA,GAAS;AACnC,uBAAOqL,EAAkB,UAAUrL,CAAO;cAC7C;;;;;YAOT,GAAO;cACC,KAAK;cACL,OAAO,SAAuBA,GAAS;AACnC,oBAAIyI,IAAW4C,EAAkB,UAAUrL,CAAO;AAElD,oBAAIyI;AACA,yBAAO,SAAS,cAAcA,CAAQ;cAE7C;;;;;;YAQT,GAAO;cACC,KAAK;;;;;cAOL,OAAO,SAAqBzI,GAAS;AACjC,uBAAOqL,EAAkB,QAAQrL,CAAO;cAC3C;;;;YAMT,GAAO;cACC,KAAK;cACL,OAAO,WAAmB;AACtB,qBAAK,SAAS,QAAA,GAEV,KAAK,oBACL,KAAK,gBAAgB,QAAA,GACrB,KAAK,kBAAkB;cAE9B;YACJ,CAAA,GAAG,CAAC;cACD,KAAK;cACL,OAAO,WAAuB;AAC1B,oBAAIoK,IAAS,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC,QAAQ,KAAK,GAE3FkB,IAAU,OAAOlB,KAAW,WAAW,CAACA,CAAM,IAAIA,GAClDmB,IAAU,CAAC,CAAC,SAAS;AAEzB,uBAAAD,EAAQ,QAAQ,SAAUlB,IAAQ;AAC9BmB,sBAAUA,KAAW,CAAC,CAAC,SAAS,sBAAsBnB,EAAM;gBAC5E,CAAa,GAEMmB;cACV;YACJ,CAAA,CAAC,GAEKJ;UACX,EAAEZ,EAAqB,CAAC;AASxB,mBAASc,EAAkBG,GAAQtE,GAAS;AACxC,gBAAIuE,IAAY,oBAAoBD;AAEpC,gBAAKtE,EAAQ,aAAauE,CAAS;AAInC,qBAAOvE,EAAQ,aAAauE,CAAS;UACzC;AAE6BxC,UAAAA,GAAoB,UAAcgC;QAExD;;MACP,CAAU,EAAE;;EACZ,CAAC;AACD,CAAC;AAp8BD,IAs8BIS,KAA2B9F,GAAwBQ,EAAS;AAEhE,IAAM/E,IAAgB;EAClB,kBAAkB;EAClB,cAAc;AAClB;AAHA,IAIMsK,KAAe;EACjB,QAAQ,CAACrJ,MAAY;AACjB,UAAM,EAAE,kBAAAsJ,GAAkB,cAAAC,EAAc,IAAGvJ;AAC3CjB,MAAc,mBAAmBuK,KAAsC,OACvEvK,EAAc,eAAewK,KAA8B;EAC9D;EACD,SAAS,CAAC3J,MAAQ;AACdA,MAAI,OAAO,iBAAiB,cAAc4J,GAC1C5J,EAAI,UAAU,aAAa;MACvB,YAAY6J,GAAIC,GAAS;AACrB,YAAIA,EAAQ,QAAQ;AAChBD,YAAG,sBAAsBC,EAAQ;iBAE5BA,EAAQ,QAAQ;AACrBD,YAAG,oBAAoBC,EAAQ;aAE9B;AACD,gBAAM5F,KAAY,IAAIsF,GAAYK,GAAI;YAClC,MAAM,MAAMC,EAAQ;YACpB,QAAQ,MACGA,EAAQ,QAAQ,QAAQ,QAAQ;YAE3C,WAAW3K,EAAc,mBAAmB0K,IAAK;UACzE,CAAqB;AACD3F,UAAAA,GAAU,GAAG,WAAW,CAACrB,MAAM;AAC3B,kBAAMyC,IAAWuE,EAAG;AACpBvE,iBAAYA,EAASzC,CAAC;UAC9C,CAAqB,GACDqB,GAAU,GAAG,SAAS,CAACrB,MAAM;AACzB,kBAAMyC,IAAWuE,EAAG;AACpBvE,iBAAYA,EAASzC,CAAC;UAC9C,CAAqB,GACDgH,EAAG,cAAc3F;QACpB;MACJ;MACD,QAAQ2F,GAAIC,GAAS;AACbA,UAAQ,QAAQ,YAChBD,EAAG,sBAAsBC,EAAQ,QAE5BA,EAAQ,QAAQ,UACrBD,EAAG,oBAAoBC,EAAQ,SAG/BD,EAAG,YAAY,OAAO,MACXC,EAAQ,OAEnBD,EAAG,YAAY,SAAS,MACbC,EAAQ,QAAQ,QAAQ,QAAQ;MAGlD;MACD,UAAUD,GAAIC,GAAS;AACfA,UAAQ,QAAQ,YAChB,OAAOD,EAAG,sBAELC,EAAQ,QAAQ,UACrB,OAAOD,EAAG,qBAGVA,EAAG,YAAY,QAAA,GACf,OAAOA,EAAG;MAEjB;IACb,CAAS;EACJ;EACD,aAAa,CAACE,GAAM7B,MAAW0B,EAAYG,GAAM7B,CAAM;AAC3D;AAtEA,IAuEM0B,IAAc,CAACG,GAAM7B,IAAS,WACzB,IAAI,QAAQ,CAAC7I,GAASC,OAAW;AACpC,QAAM0K,IAAc,SAAS,cAAc,QAAQ,GAC7C9F,IAAY,IAAIsF,GAAYQ,GAAa;IAC3C,MAAM,MAAMD;IACZ,QAAQ,MAAM7B;EAC1B,CAAS;AACDhE,IAAU,GAAG,WAAW,CAACrB,MAAM;AAC3BqB,MAAU,QAAO,GACjB7E,EAAQwD,CAAC;EACrB,CAAS,GACDqB,EAAU,GAAG,SAAS,CAACrB,MAAM;AACzBqB,MAAU,QAAO,GACjB5E,GAAOuD,CAAC;EACpB,CAAS,GACG1D,EAAc,gBACd,SAAS,KAAK,YAAY6K,CAAW,GAEzCA,EAAY,MAAK,GACb7K,EAAc,gBACd,SAAS,KAAK,YAAY6K,CAAW;AAEjD,CAAK;AA7FL,IC99BM9I,KAAAA,OAAAA,OAAAA,EAAAA,gCAAAA,MAAAA,OAAAA,oCAAAA,EAAAA,CAAAA;AD89BN,IC59BM+B,KAAsB;EAC1B,QAAQjD,GAAQ;AAEd2B,OAAM,KAAK,uBAAuB3B,GAAKkB,IAAc,CAAA,GAAI,MAAM,IAAI;EACrE;;ADw9BF,ICr9Ba+I,KAAoB;EAC/B,QAAQjK,GAAQ;AACdA,MAAI,IAAIsD,EAAa,GACrBtD,EAAI,IAAIiD,EAAmB,GAC3BjD,EAAI,IAAIyJ,EAAY;EACtB;;ACjBY,SAAAS,KAAA;AACL,SAAA;IACL,iBAAiB;MACf,QAAQ;QACN,WAAW;UACT,MAAM;UACN,QAAQ;QACT;MACF;IACF;EAAA;AAEL;ACPA,IAAeC,MAAA;EACb,QAAQnK,GAAQ;AACd,UAAMK,IAAWC,GAAAA,GACX,EAAE,UAAAC,EAAAA,IAAaC,GAAAA;AACrBD,MAASF,CAAQ;EACnB;;AALF,ICDMa,KAAAA,OAAAA,OAAAA,EAAAA,qCAAAA,MAAAA,OAAAA,yCAAAA,EAAAA,CAAAA;ADCN,ICAM+B,KAAsB;EAC1B,QAAQjD,GAAQ;AAEd2B,OAAM,KAAK,uBAAuB3B,GAAKkB,IAAc,CAAA,GAAI,MAAM,IAAI;EACrE;;ADJF,ICOakJ,KAAgB;EAC3B,QAAQpK,GAAQ;AACdA,MAAI,IAAIsD,GAAa,GACrBtD,EAAI,IAAIiD,EAAmB;EAC7B;;ACfF,eAAsBoH,IAAY;AAC1B,QAAAnJ,IAAAA,OAAAA,OAAAA,EAAAA,2BAAAA,MAAAA,OAAAA,2CAAAA,EAAAA,CAAAA,GAEA,EAAE,WAAAoJ,EAAS,IAAK,MAAMpJ,EAAa,yBAAyB,EAAC;AAC5D,SAAAoJ;AACT;AACA,eAAsBC,GAAkBhR,GAAU;AAC1C,QAAA+Q,IAAY,MAAMD,EAAAA;AACxB,MAAI9K,IAAM;AACV,SAAIhG,KAAS,SACPA,EAAM,eAAe,OACvBgG,IAAM+K,EAAU,KAAK,CAACjQ,OAASA,GAAK,SAASd,EAAM,WAAW,IACrDA,EAAM,eAAe,SAC9BgG,IAAM+K,EAAU,KAAK,CAACjQ,OAASA,GAAK,aAAad,EAAM,WAAW,KAGlEgG,KAAO,SACHA,IAAA;IACJ,aAAaA,EAAI;IACjB,aAAaA,EAAI;EAAA,IAGdA;AACT;;;;;;;;;;;;;;;;;;;;;;;;ACOM,UAAA,EAAE,IAAA3F,EAAAA,IAAOC,EAAAA,GAmDT2Q,KAAgB5Q,EAAG,SAAS,sBAAsB,GAClDF,IAAQ+Q,GAIRC,IAAQC,GAERC,IAAmB7Q;MACvBL,EAAM,cAAc;QAClB,aAAa;;QACb,aAAa;;QACb,aAAa;;MACf;IAAA,GAGImR,IAAcC,GAAK;MACvB,OAAO;MACP,OAAO;IAAA,CACR,GAEKC,IAAehR,IAAI,CAAA,CAAE;AAE3B,mBAAeiR,IAAgB;AAChBD,QAAA,QAAQ,MAAMV,EAAAA;IAC7B;AACcW,MAAAA;AAER,UAAAC,IAAiBhR,SAAS,MAAM;AACpC,YAAMqQ,IAAYS,EAAa;AAC/B,UAAI3K,IAAU,CAAA;AACd,UAAI1G,EAAM,iBAAiB,QAAQA,EAAM,cAAc,SAAS;AAC9D,iBAASwR,KAAWZ;AACd5Q,YAAM,cAAc,KAAK,CAACW,MAASA,EAAK,YAAY,MAAM6Q,EAAQ,KAAK,YAAa,CAAA,KACtF9K,EAAQ,KAAK8K,CAAO;WAGnB;AACC,cAAAC,IAAoBzR,EAAM,qBAAqB,CAAA,GAC/C0R,IAAmB1R,EAAM,oBAAoB,CAAA,GAC7C2R,IAAa,CAAA,GACbC,IAAS,CAAA;AACf,iBAASJ,KAAWZ;AACda,YAAkB,KAAK,CAAC9Q,MAASA,EAAK,YAAY,MAAM6Q,EAAQ,KAAK,YAAY,CAAC,KACpFG,EAAW,KAAKH,CAAO,GAEpBE,EAAiB,KAAK,CAAC/Q,MAASA,EAAK,YAAY,MAAM6Q,EAAQ,KAAK,YAAa,CAAA,KACpFI,EAAO,KAAKJ,CAAO;AAGb9K,YAAAiL,EAAW,OAAOC,CAAM;MACpC;AAEU,aAAAlL,IAAAA,EAAQ,IAAI,CAAC/F,OACd;QACL,GAAGA;QACH,OAAOA,EAAK,OAAO,MAAMA,EAAK,WAAW;MAAA,EAE5C,GACM+F;IAAA,CACR,GAEKmL,IAAiBtR,SAAS,MAAM;AACpC,YAAMuR,IAAM;QACV,aAAa;QACb,CAAC5R,EAAG,OAAO,UAAU,GAAG;QACxB,CAACA,EAAG,OAAO,SAAS,GAAG;QACvB,CAACA,EAAG,OAAO,UAAU,GAAGgR,EAAY,MAAM;QAC1C,CAAC,cAAchR,EAAG,OAAO,UAAU,GAAG6R;MAAA;AAEjC,aAAAvR,cAAMsR,GAAK9R,EAAM,MAAM;IAAA,CAC/B,GAEKgS,IAAgBzR,SAAS,MAAM;AACnC,YAAMuR,IAAW;QACf,aAAa;QACb,CAAC5R,EAAG,OAAO,SAAS,GAAG;QACvB,CAACA,EAAG,MAAM,UAAU,GAAGgR,EAAY,MAAM;QACzC,CAAC,YAAYhR,EAAG,MAAM,UAAU,EAAE,GAAG+R;MAAA;AAEhC,aAAAzR,cAAMsR,GAAK9R,EAAM,KAAK;IAAA,CAC9B;AAkBD,mBAAekS,EAASrS,GAAY;AAClCqR,QAAY,QAAQ,EAAE,aAAa,QAAW,aAAa,QAAW,aAAa,OAAA;AAC7E,YAAArL,IAAM,MAAMgL,EAAkBhR,CAAK;AACrCgG,WAAO,SACGqL,EAAA,MAAM,cAAcrL,EAAI,aACxBqL,EAAA,MAAM,cAAcrL,EAAI,cAElChG,KAASA,EAAM,cACLqR,EAAA,MAAM,cAAcrR,EAAM,cAEtCqR,EAAY,MAAM,cAAc;IAEpC;AACA,mBAAeL,EAAkBhR,GAA0B;AACzD,UAAIgG,IAAW;AACf,aAAIhG,KAAS,SACPA,EAAM,eAAe,OACjBgG,IAAA0L,EAAe,MAAM,KAAK,CAAC5Q,MAASA,EAAK,SAASd,EAAM,WAAW,IAChEA,EAAM,eAAe,SACxBgG,IAAA0L,EAAe,MAAM,KAAK,CAAC5Q,MAASA,EAAK,aAAad,EAAM,WAAW,KAG7EgG,KAAO,SACHA,IAAA;QACJ,aAAaA,EAAI;QACjB,aAAaA,EAAI;MAAA,IAGjBA,KAAO,SACTA,IAAM,MAAMsM,GAA0B,EAAE,aAAanS,EAAM,eAAA,CAAgB,IAEtE6F;IACT;AAEA,mBAAekM,EAAkBK,GAAkB;AACjD,YAAMC,EAAcD,CAAW;AAC/B,UAAIE,IAAiBC,EAAAA;AACrBvB,QAAM,qBAAqBsB,CAAS,GACpCtB,EAAM,SAASsB,CAAS,GACxBtB,EAAM,UAAUsB,CAAS,GACzB,MAAMxB,GAAc,SAAA,GACpB,MAAMA,GAAc,OAAA;IACtB;AAEA,mBAAemB,EAAkBO,GAAa;AAE5C,UADAtB,EAAY,MAAM,cAAcsB,GAC5BtB,EAAY,MAAM,eAAe,QAAQA,EAAY,MAAM,eAAe,MAAM;AACtEA,UAAA,MAAM,cAAclR,EAAM;AACtC,cAAMwR,IAAU,MAAMX,EAAkBK,EAAY,KAAK;AACrDM,cACUN,EAAA,MAAM,cAAcM,EAAQ;MAE5C;AACA,UAAIc,IAAYC,EAAAA;AAChBvB,QAAM,qBAAqBsB,CAAS,GACpCtB,EAAM,SAASsB,CAAS,GACxBtB,EAAM,UAAUsB,CAAS,GACzB,MAAMxB,GAAc,SAAA,GACpB,MAAMA,GAAc,OAAA;IACtB;AAEA,aAASyB,IAAgC;AAChC,aAAA;QACL,aAAarB,EAAY,MAAM;QAC/B,aAAaA,EAAY,MAAM;QAC/B,aAAaA,EAAY,MAAM;MAAA;IAEnC;AAEA,mBAAemB,EAAcD,GAAkB;AACxCA,YACHlB,EAAY,MAAM,cAAc,SAElCA,EAAY,MAAM,cAAckB;AAChC,UAAIvM,IAAM,MAAMgL,EAAkBK,EAAY,KAAK;AAC/CrL,YACUqL,EAAA,MAAM,cAAcrL,EAAI;IAExC;AAEA,WAAApE;MACE,MACSzB,EAAM;MAEf,OAAOH,GAAO4S,MAAa;AACzB,cAAMP,EAASrS,CAAK,GACdmR,EAAA,UAAUE,EAAY,KAAK;MACnC;MACA;QACE,WAAW;MACb;IAAA,GAAA,CAAA,GAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;AC3QF,eAAsBwB,GAA8B,EAAE,aAAAC,GAAa,aAAAP,EAAAA,GAAkB;AAEnF,QAAMQ,MADiB,MAAM,OAAO,8BAAuB,GACT,4BAC5CC,IAAUF,KAAeP,IAAcQ,GAA2BD,GAAaP,CAAW,IAAI;AAC7F,SAAA;IACL,aAAaO,KAAe;IAC5B,aAAAP;IACA,SAAS;IACT,GAAIS,IACA;MACE,iBAAiBA,EAAQ;MACzB,gBAAgBA,EAAQ;MACxB,SAASA,EAAQ,QAAS;MAC1B,MAAMA,EAAQ,QAAS;MACvB,qBAAqBA,EAAQ,oBAAqB;MAClD,gBAAgBA,EAAQ,eAAgB;MACxC,KAAKA,EAAQ,OAAQ;MACrB,MAAMA,EAAQ,OAAO,OAAO;IAAA,IAE9B;EAAA;AAER;AAEA,eAAehC,GAAkBhR,GAAU;AACnC,QAAA2H,IAAAA,OAAAA,OAAAA,EAAAA,cAAAA,MAAAA,QAAAA,QAAAA,EAAAA,KAAAA,MAAAA,EAAAA,EAAAA,CAAAA,GAEA,EAAE,mBAAAqJ,EAAAA,IAAsB,MAAMrJ,EAAa,YAAY,EAAA;AAC7D,SAAOqJ,EAAkBhR,CAAK;AAChC;AASsB,eAAAiT,GAAqBjQ,GAAWhD,GAAU;AAC9D,MAAI,CAACA,KAASA,EAAM,eAAe,QAAQA,EAAM,gBAAgB;AACxD,WAAA;AAGT,MAAI,CAACA,EAAM,eAAeA,EAAM,aAAa;AACrC,UAAA2R,KAAU,MAAMX,GAAkBhR,CAAK;AACzC2R,IAAAA,OACF3R,EAAM,cAAc2R,GAAQ;EAE/B;AACK,QAAAuB,IAAQ,MAAML,GAA8B;IAChD,aAAa7S,EAAM;IACnB,aAAaA,EAAM;EAAA,CACpB;AACG,MAAA,CAACkT,EAAM;AACD,UAAA,QAAA,KAAK,UAAUA,CAAK,GACtB,IAAI,MAAM,QAAQ;AAEnB,SAAA;AACT;AASsB,eAAAC,GAAgBnQ,GAAWhD,GAAY+L,GAAa;AACxE,MACE,CAAC/L,KACDA,EAAM,eAAe,QACrBA,EAAM,gBAAgB,MACtBA,EAAM,eAAe,QACrBA,EAAM,gBAAgB;AAEf,WAAA;AAGT,MAAI,CAACA,EAAM,eAAeA,EAAM,aAAa;AACrC,UAAA2R,IAAU,MAAMX,GAAkBhR,CAAK;AACzC2R,UACF3R,EAAM,cAAc2R,EAAQ;EAE/B;AACK,QAAAuB,KAAQ,MAAML,GAA8B;IAChD,aAAa7S,EAAM;IACnB,aAAaA,EAAM;EAAA,CACpB;AACG,MAAA,CAACkT,GAAM,WAAYA,GAAM,SAAS,YAAYA,GAAM,SAAS;AACvD,UAAA,QAAA,KAAK,UAAUA,EAAK,GACtB,IAAI,MAAM,OAAO;AAElB,SAAA;AACT;AAQsB,eAAAE,GAAwBpQ,GAAWhD,GAAU;AACjE,QAAMqT,IAAgBrT,EAAM,eAAe,QAAQA,EAAM,gBAAgB,IACnEsT,KAAgBtT,EAAM,eAAe,QAAQA,EAAM,gBAAgB,IACnEuT,IAAgBvT,EAAM,eAAe,QAAQA,EAAM,gBAAgB;AACzE,MAAI,CAACA,KAASuT,KAAkBF,KAAiBC;AACzC,UAAA,IAAI,MAAM,MAAM;AAEjB,SAAA;AACT;AC5Gc,SAAAvM,KAAA;AACZ,SAAO;IACLyM,OAAO;MACLhP,QAAQ;QACNiP,WAAW;UAAEzT,OAAAA;QAAY,GAAA;AACvB,iBAAI,CAACA,KAAS,CAACA,EAAM8S,cACZ,KAETnQ,YAAA,OAAA,MAAA,CAAA+Q,gBAAA,GAAA,GAEM1T,EAAM2T,eAAe,MAAID,gBAAG1T,GAAAA,GAAAA,EAAM8S,WAAW,CAAA;QAGrD;MACD;MACDlP,MAAM;QACJC,WAAW;UACTC,MAAM;UACN8P,QAAQ;QACT;QACDxP,OAAO,CAAC;UAAEC,WAAW8O;UAAiB7O,SAAS;QAAA,CAAc;MAC9D;IACF;EAAA;AAEL;ACtBA,IAAeyF,KAAA;EACb,QAAQtD,GAAQ;AACd,UAAMK,IAAWC,GAAAA,GACX,EAAE,UAAAC,EAAAA,IAAaC,GAAAA;AACrBD,MAASF,CAAQ;EACnB;;AALF,ICAMa,KAAAA,OAAAA,OAAAA,EAAAA,kDAAAA,MAAAA,OAAAA,uCAAAA,EAAAA,CAAAA;ADAN,ICCM+B,KAAsB;EAC1B,QAAQjD,GAAQ;AAEd2B,OAAM,KAAK,uBAAuB3B,GAAKkB,IAAc,CAAA,GAAI,MAAM,IAAI;EACrE;;ADLF,ICQakM,KAAiB;EAC5B,QAAQpN,GAAQ;AACdA,MAAI,IAAIsD,EAAa,GACrBtD,EAAI,IAAIiD,EAAmB;EAC7B;;", "names": ["_sfc_main$1", "defineComponent", "value", "url", "index", "props", "ctx", "ui", "useUi", "imageListRef", "ref", "errorBinding", "computed", "merge", "computedValues", "urls", "item", "buildImageList", "images", "previewUrls", "i", "image", "imageList", "previewUrl", "preview", "buildImageListUrls", "list", "values", "buildImageListByValue", "buildImageListByUrls", "watch", "_hoisted_1", "_hoisted_2", "_resolveComponent", "_openBlock", "_createElementBlock", "_createBlock", "_resolveDynamicComponent", "_ctx", "_guardReactiveProps", "_withCtx", "_Fragment", "_renderList", "_mergeProps", "_createElementVNode", "_createVNode", "_component_fs_loading", "_hoisted_3", "createAllUploadSuccessValidator", "getFormComponentRef", "rule", "AllUploadSuccessValidator", "compute", "getComponentRef", "createUploaderRules", "yourRules", "uploadingWarningMessage", "types$5", "t", "useI18n", "uiContext", "get", "form", "component", "name", "listType", "upload", "typeImageCard", "accept", "formItem", "rules", "validator", "message", "trigger", "column", "style", "previewTeleported", "previewMask", "icons", "eye", "viewForm", "height", "width", "limit", "align", "valueResolve", "row", "key", "Array", "length", "cropper", "aspectRatio", "autoCropArea", "viewMode", "defaultConfig", "context", "resolve", "reject", "ret", "fileName", "date", "fileType", "<PERSON><PERSON><PERSON>", "ext", "uploaderConfig", "cloneDeep", "setConfig", "app", "config", "AllSuccessValidator", "FsUploaderType", "options", "newTypes", "types", "addTypes", "useTypes", "loadUploader", "type", "module", "buildKey", "file", "useUploader", "getDefaultType", "getConfig", "getUploaderImpl", "asyncModules", "syncModules", "registerMergeColumnPlugin", "useColumns", "columnProps", "crudOptions", "buildUrl", "buildUrls", "FsUploaderComponents", "utils", "FsExtendsUploader", "types$4", "ExtendsType", "WorkerBucket", "registerWorker", "worker", "initWorkers", "editor<PERSON><PERSON><PERSON>", "jsonWorker", "cssWorker", "htmlWorker", "tsWorker", "yamlWorker", "_", "label", "custom", "jsonRule", "e", "yamlRule", "yaml", "FsEditorCodeValidators", "FsExtendsComponents", "FsExtendsEditor", "types$3", "FsExtendsType$3", "FsExtendsJson", "FsExtendsType", "types$2", "FsExtendsType$2", "commonjsGlobal", "getDefaultExportFromCjs", "x", "createCommonjsModule", "fn", "basedir", "path", "base", "commonjsRequire", "clipboard", "exports", "root", "factory", "modules", "installedModules", "__webpack_require__", "moduleId", "getter", "mode", "ns", "object", "property", "select", "element", "selectedText", "isReadOnly", "selection", "range", "E", "callback", "self", "listener", "data", "evtArr", "len", "evts", "liveEvents", "is", "delegate", "listen", "target", "listenNode", "listenNodeList", "listenSelector", "node", "nodeList", "selector", "closest", "_delegate", "useCapture", "listenerFn", "elements", "DOCUMENT_NODE_TYPE", "proto", "__webpack_exports__", "src_select", "select_default", "_typeof", "obj", "_createClass", "defineProperties", "descriptor", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "_classCallCheck", "instance", "clipboard_action_ClipboardAction", "ClipboardAction", "_this", "isRTL", "yPosition", "succeeded", "action", "clipboard_action", "tiny_emitter", "tiny_emitter_default", "listen_default", "clipboard_typeof", "clipboard_createClass", "clipboard_classCallCheck", "_possibleConstructorReturn", "call", "_inherits", "subClass", "superClass", "clipboard_Clipboard", "_Emitter", "Clipboard", "_this2", "getAttributeValue", "actions", "support", "suffix", "attribute", "ClipboardJS", "VueClipboard", "autoSetContainer", "appendToBody", "toClipboard", "el", "binding", "text", "fakeElement", "FsExtendsCopyable", "types$1", "FsExtendsType$1", "FsExtendsTime", "getCountries", "countries", "getCountryByValue", "formValidator", "__props", "emits", "__emit", "selectValue", "countryDict", "dict", "countriesRef", "loadCountries", "countryOptions", "country", "priorityCountries", "ignoredCountries", "priorities", "leaved", "computedSelect", "def", "handleSelectInput", "computedInput", "handleNumberInput", "setValue", "getCountryByValueFromUtil", "countryCode", "changeCountry", "emitValue", "getEmitValue", "number", "oldValue", "getParsePhoneNumberFromString", "phoneNumber", "parsePhoneNumberFromString", "parsing", "phoneNumberValidator", "parse", "mobileValidator", "mobileRequiredValidator", "noCountryCode", "noCallingCode", "noPhoneNumber", "phone", "cellRender", "_createTextVNode", "callingCode", "vModel", "FsExtendsInput"]}