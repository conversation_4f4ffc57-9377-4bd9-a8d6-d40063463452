/**
 * CS_MANAGE 模块 API 接口
 */

import { request } from '/@/utils/service';

// 接口基础路径
const API_BASE = '/api/cs_manage'

// 类型定义
export interface DashboardFilters {
  start_date?: string
  end_date?: string
  group_id?: number // 兼容旧版本
  group_ids?: number[] // 新版本多选
}

export interface GroupOption {
  id: number
  name: string
}

export interface CardsData {
  total_sessions: number
  ai_sessions: number
  manual_sessions: number
  worksheet_count: number
  ai_transfer_rate: number
  manual_satisfaction: number
  fcr_ratio: number
  resp_30_ratio: number
  avg_first_resp: number
  eva_count: number
  invite_count: number
  emotion_score?: number
}

export interface ChartSeries {
  name: string
  data: number[]
  type: 'line' | 'bar'
}

export interface ChartData {
  dates: string[]
  series: ChartSeries[]
}

export interface ChartsData {
  session_trend: ChartData
  worksheet_trend: ChartData
  quality_metrics: ChartData
  emotion_trend: ChartData
}

export interface DashboardOverviewResponse {
  cards: CardsData
  charts: ChartsData
  filters: {
    groups: GroupOption[]
    current_filters: DashboardFilters
  }
}

// Link排行榜相关类型定义
export interface LinkRankingFilters {
  start_time?: string
  end_time?: string
  group_id?: string
}

export interface ArticleData {
  id: number
  title: string
  summary: string
  create_datetime: string
  link_count: number
  game: {
    id: number
    name: string
  }
  trend_data: Array<{
    time: string | number
    count: number
  }>
}

export interface LinkTrendData {
  time: string | number
  count: number
}

export interface LinkRankingResponse {
  top_articles: ArticleData[]
  link_trend: LinkTrendData[]
  summary: {
    total_links: number
    total_articles: number
    period: {
      start_time: string
      end_time: string
    }
  }
}

/**
 * 获取仪表盘概览数据
 */
export const getDashboardOverview = (params: DashboardFilters) => {
  return request<DashboardOverviewResponse>({
    url: `${API_BASE}/dashboard/overview/`,
    method: 'get',
    params
  })
}

/**
 * 获取可用的客服组列表
 */
export const getAvailableGroups = () => {
  return request<GroupOption[]>({
    url: `${API_BASE}/dashboard/groups/`,
    method: 'get'
  })
}

/**
 * 获取历史数据趋势
 */
export const getHistoricalTrend = (params: DashboardFilters & { metric: string }) => {
  return request({
    url: `${API_BASE}/dashboard/trend/`,
    method: 'get',
    params
  })
}

/**
 * 导出数据
 */
export const exportDashboardData = (params: DashboardFilters) => {
  return request({
    url: `${API_BASE}/dashboard/export/`,
    method: 'get',
    params,
    responseType: 'blob'
  })
}

/**
 * 获取Link排行榜数据
 */
export const getLinkRanking = (data: LinkRankingFilters) => {
  return request<LinkRankingResponse>({
    url: `${API_BASE}/dashboard/link_ranking/`,
    method: 'post',
    data
  })
}
